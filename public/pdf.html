<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>StudyMind - PDF Study Session</title>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Geist:wght@100..900&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Geist+Mono:wght@100..900&family=Geist:wght@100..900&display=swap"
      rel="stylesheet"
    />

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>
    <style type="text/tailwindcss">
      :root {
        --bg-primary: #fdfbf6;
        --bg-secondary: #f0e5d8;
        --text-primary: #2d3748;
        --text-secondary: #4a5568;
        --text-muted: #718096;
        --text-subtle: #a0aec0;
        --text-on-tinted: #2d3748;
        --text-on-tinted-muted: #4a5568;
        --text-on-tinted-subtle: #718096;
        --accent-primary: #a9c2b9;
        --accent-secondary: #d3b8ae;
        --accent-tertiary: #e6cba5;
        --highlight: #8b9d8f;
        --success: #68d391;
        --warning: #f6ad55;
        --info: #63b3ed;
        --paper-white: #ffffff;
        --paper-shadow: rgba(45, 55, 72, 0.08);
      }

      body {
        font-family: "Geist", sans-serif;
        background-color: var(--bg-primary);
        color: var(--text-primary);
      }
      .geist-mono {
        font-family: "Geist Mono", monospace;
      }

      .stagger-entrance > * {
        opacity: 0;
        transform: translateY(20px);
        animation: fadeInUp 0.6s forwards;
      }
      @keyframes fadeInUp {
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .hover-lift {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .hover-lift:hover {
        transform: translateY(-2px) scale(1.01);
        box-shadow: 0 8px 16px -4px rgba(45, 55, 72, 0.12),
          0 4px 6px -2px rgba(45, 55, 72, 0.08);
      }

      .minimal-artwork-bg {
        background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23e6cba5' fill-opacity='0.1'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-repeat: repeat;
        background-size: 200px 200px;
      }
      .minimal-artwork-subtle {
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23a9c2b9' fill-opacity='0.07'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-repeat: repeat;
      }

      /* Enhanced button styles */
      .btn-primary {
        background: var(--accent-primary);
        color: white;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        border-radius: 0.75rem;
        transition: all 0.2s ease;
        border: none;
        cursor: pointer;
      }

      .btn-primary:hover {
        background: var(--highlight);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(169, 194, 185, 0.3);
      }

      .focus-ring:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(169, 194, 185, 0.3);
        border-radius: 8px;
      }

      /* PDF Document Styles */
      .pdf-document {
        background: var(--paper-white);
        border-radius: 1rem;
        box-shadow: 0 8px 32px -4px var(--paper-shadow),
          0 4px 16px -2px var(--paper-shadow);
        overflow: hidden;
        max-width: 800px;
        margin: 0 auto;
        transition: all 0.3s ease;
      }

      .pdf-page {
        padding: 3rem;
        line-height: 1.7;
        font-size: 16px;
        color: var(--text-primary);
      }

      .pdf-page h1 {
        color: var(--text-primary);
        font-size: 2.25rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        line-height: 1.2;
      }

      .pdf-page h2 {
        color: var(--text-primary);
        font-size: 1.875rem;
        font-weight: 600;
        margin: 2rem 0 1rem 0;
        line-height: 1.3;
      }

      .pdf-page h3 {
        color: var(--text-secondary);
        font-size: 1.5rem;
        font-weight: 600;
        margin: 1.5rem 0 0.75rem 0;
        line-height: 1.4;
      }

      .pdf-page p {
        margin-bottom: 1.25rem;
        color: var(--text-secondary);
      }

      .pdf-page ul {
        margin: 1rem 0 1.5rem 0;
        padding-left: 1.5rem;
        color: var(--text-secondary);
      }

      .pdf-page li {
        margin-bottom: 0.5rem;
      }

      /* Gentle Highlighting System */
      .highlighted-text {
        background: linear-gradient(
          120deg,
          rgba(169, 194, 185, 0.2) 0%,
          rgba(169, 194, 185, 0.15) 100%
        );
        border-bottom: 2px solid rgba(169, 194, 185, 0.4);
        padding: 2px 4px;
        border-radius: 4px;
        cursor: pointer;
        position: relative;
        transition: all 0.3s ease;
        color: var(--text-primary);
        font-weight: 500;
      }

      .highlighted-text:hover {
        background: linear-gradient(
          120deg,
          rgba(169, 194, 185, 0.3) 0%,
          rgba(169, 194, 185, 0.25) 100%
        );
        border-bottom-color: var(--accent-primary);
      }

      /* Learning Insight Bubble */
      .learning-insight {
        position: absolute;
        top: -8px;
        left: 50%;
        transform: translateX(-50%) translateY(-100%);
        background: var(--paper-white);
        border: 1px solid var(--accent-primary);
        border-radius: 1rem;
        padding: 1rem 1.25rem;
        z-index: 100;
        min-width: 320px;
        max-width: 400px;
        box-shadow: 0 12px 32px -8px rgba(169, 194, 185, 0.3),
          0 4px 16px -4px rgba(169, 194, 185, 0.2);
        opacity: 0;
        transform: translateX(-50%) translateY(-100%) scale(0.95);
        transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        pointer-events: none;
      }

      .learning-insight.show {
        opacity: 1;
        transform: translateX(-50%) translateY(-100%) scale(1);
        pointer-events: auto;
      }

      .learning-insight::after {
        content: "";
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-top: 8px solid var(--paper-white);
      }

      .insight-actions {
        display: flex;
        gap: 0.5rem;
        margin-top: 0.75rem;
      }

      .insight-btn {
        background: var(--accent-primary);
        border: none;
        color: white;
        padding: 0.5rem 0.75rem;
        border-radius: 0.5rem;
        cursor: pointer;
        font-size: 0.75rem;
        font-weight: 500;
        transition: all 0.2s ease;
      }

      .insight-btn:hover {
        background: var(--highlight);
        transform: translateY(-1px);
      }

      /* Focus Mode */
      .focus-mode .ai-panel {
        display: none;
      }

      .focus-mode .page-nav-panel {
        display: none;
      }

      .focus-mode .pdf-container {
        width: 100%;
      }

      .focus-mode .content-area {
        height: calc(100vh - 100px);
      }

      /* PDF Page Navigation */
      .page-nav-panel {
        transition: all 0.3s ease;
        background: white;
        border-right: 1px solid var(--bg-secondary);
        height: 100%;
        max-height: 100%;
      }

      .page-nav-panel.collapsed {
        width: 0;
        overflow: hidden;
      }

      .page-nav-toggle {
        position: fixed;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 200;
        background: var(--accent-primary);
        border: none;
        border-radius: 0 0.5rem 0.5rem 0;
        padding: 0.75rem 0.5rem;
        color: white;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(169, 194, 185, 0.3);
        transition: all 0.3s ease;
        opacity: 0;
        pointer-events: none;
      }

      .page-nav-toggle.show {
        opacity: 1;
        pointer-events: auto;
      }

      .page-nav-toggle:hover {
        background: var(--highlight);
        transform: translateY(-50%) translateX(4px);
      }

      .page-thumbnail {
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 0.5rem;
        padding: 0.25rem;
        position: relative;
      }

      .page-thumbnail:hover {
        background: var(--bg-secondary);
        transform: scale(1.05);
      }

      .page-thumbnail.current {
        background: rgba(169, 194, 185, 0.2);
        border: 2px solid var(--accent-primary);
      }

      .page-thumbnail.active {
        background: rgba(169, 194, 185, 0.1);
        border: 1px solid var(--accent-tertiary);
      }

      .thumbnail-preview {
        width: 48px;
        height: 64px;
        background: white;
        border: 1px solid var(--bg-secondary);
        border-radius: 0.25rem;
        position: relative;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(45, 55, 72, 0.1);
      }

      .page-content {
        padding: 0.375rem;
        height: 100%;
        display: flex;
        flex-direction: column;
        gap: 0.125rem;
      }

      .text-line {
        height: 1px;
        background: var(--text-muted);
        border-radius: 0.5px;
        opacity: 0.6;
      }

      .text-line.short {
        width: 60%;
      }

      .text-line.medium {
        width: 80%;
      }

      .page-number {
        text-align: center;
        font-size: 0.625rem;
        color: var(--text-muted);
        margin-top: 0.25rem;
        font-family: "Geist Mono", monospace;
      }

      .page-thumbnail.current .page-number {
        color: var(--accent-primary);
        font-weight: 600;
      }

      #pageThumbnails {
        flex: 1;
        min-height: 0;
        overflow-y: auto;
        overflow-x: hidden;
      }

      /* Content Area Layout Fix */
      .content-area {
        height: calc(100vh - 100px); /* Account for header + progress bar */
        min-height: 0;
      }

      .pdf-viewer-container {
        height: 100%;
        min-height: 0;
      }

      /* Navigation Panel */
      .chapter-item {
        transition: all 0.3s ease;
        border-radius: 0.75rem;
        padding: 0.75rem 1rem;
        cursor: pointer;
        border-left: 3px solid transparent;
        margin-bottom: 0.5rem;
      }

      .chapter-item:hover {
        background-color: var(--bg-secondary);
        transform: translateX(4px);
        border-left-color: var(--accent-tertiary);
      }

      .sidebar-card-item:hover {
        background-color: var(--bg-secondary);
        transform: translateX(2px);
      }

      .chapter-item.current {
        background: rgba(169, 194, 185, 0.15);
        border-left-color: var(--accent-primary);
        box-shadow: 0 2px 8px rgba(169, 194, 185, 0.2);
      }

      .breathing-animation {
        animation: breathe 4s ease-in-out infinite;
      }

      @keyframes breathe {
        0%,
        100% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.02);
        }
      }

      /* Zoom Controls */
      .zoom-controls {
        background: var(--bg-secondary);
        border-radius: 0.75rem;
        padding: 0.5rem;
        display: flex;
        gap: 0.25rem;
      }

      .zoom-btn {
        background: transparent;
        border: none;
        color: var(--text-muted);
        padding: 0.5rem 0.75rem;
        border-radius: 0.5rem;
        cursor: pointer;
        font-size: 0.875rem;
        transition: all 0.2s ease;
      }

      .zoom-btn:hover {
        background: var(--accent-tertiary);
        color: var(--text-primary);
      }

      .zoom-btn.active {
        background: var(--accent-primary);
        color: white;
      }

      /* Progress Indicator */
      .reading-progress {
        background: var(--bg-secondary);
        height: 4px;
        border-radius: 2px;
        overflow: hidden;
      }

      .progress-fill {
        background: var(--accent-primary);
        height: 100%;
        border-radius: 2px;
        transition: width 0.5s cubic-bezier(0.65, 0, 0.35, 1);
      }

      @media (max-width: 1024px) {
        .ai-panel {
          width: 320px;
        }
      }

      @media (max-width: 768px) {
        .study-container {
          flex-direction: column;
        }
        .page-nav-panel {
          width: 60px;
        }
        .ai-panel {
          width: 100%;
          height: 40vh;
        }
        .pdf-page {
          padding: 2rem;
        }
        .thumbnail-preview {
          width: 40px;
          height: 52px;
        }
      }
    </style>
  </head>
  <body class="text-[var(--text-primary)]">
    <!-- Hidden Page Navigation Toggle -->
    <button
      class="page-nav-toggle"
      id="pageNavToggle"
      onclick="togglePageNav()"
      title="Show page navigation"
    >
      <i class="w-4 h-4" data-lucide="chevron-right"></i>
    </button>

    <div class="flex h-screen">
      <!-- PDF Study Panel -->
      <div
        class="pdf-container flex-1 bg-[var(--bg-primary)] flex flex-col transition-all duration-500"
      >
        <!-- Header -->
        <header
          class="flex justify-between items-center p-6 bg-white border-b border-[var(--bg-secondary)]"
        >
          <div class="flex items-center space-x-4">
            <button
              onclick="goBack()"
              class="w-10 h-10 bg-[var(--bg-secondary)] rounded-full flex items-center justify-center hover:bg-[var(--accent-tertiary)]/50 transition-colors focus-ring"
            >
              <i
                class="w-5 h-5 text-[var(--text-primary)]"
                data-lucide="arrow-left"
              ></i>
            </button>
            <div>
              <h1 class="text-xl font-semibold text-[var(--text-primary)]">
                Market Structures and Competition
              </h1>
              <div
                class="flex items-center space-x-4 text-sm text-[var(--text-muted)] mt-1"
              >
                <span class="flex items-center">
                  <i class="w-4 h-4 mr-1" data-lucide="book-open"></i>
                  Chapter 5
                </span>
                <span class="flex items-center">
                  <i class="w-4 h-4 mr-1" data-lucide="clock"></i>
                  15 min read
                </span>
                <span class="flex items-center">
                  <i class="w-4 h-4 mr-1" data-lucide="eye"></i>
                  Page 2 of 15
                </span>
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            <div class="zoom-controls">
              <button class="zoom-btn" onclick="setZoom(75)">75%</button>
              <button class="zoom-btn active" onclick="setZoom(100)">100%</button>
              <button class="zoom-btn" onclick="setZoom(125)">125%</button>
            </div>
            <button
              id="focusToggle"
              onclick="toggleFocus()"
              class="btn-primary text-sm"
            >
              <i class="w-4 h-4 mr-2" data-lucide="focus"></i>
              Focus Mode
            </button>
            <div class="flex items-center space-x-3">
              <div
                class="w-8 h-8 bg-[var(--accent-secondary)] rounded-full flex items-center justify-center"
              >
                <img
                  src="/images/profile-avatar.png"
                  alt="Avatar"
                  class="w-6 h-6 rounded-full"
                />
              </div>
            </div>
          </div>
        </header>

        <!-- Reading Progress -->
        <div class="reading-progress">
          <div class="progress-fill" style="width: 13%" id="readingProgress"></div>
        </div>

        <!-- Content Area with Page Navigation -->
        <div class="flex content-area">
          <!-- PDF Page Navigation Sidebar -->
          <aside
            class="page-nav-panel w-20 bg-white border-r border-[var(--bg-secondary)] flex flex-col transition-all duration-500"
            id="pageNavPanel"
          >
            <!-- Toggle Button -->
            <div class="p-3 border-b border-[var(--bg-secondary)]">
              <button
                onclick="togglePageNav()"
                class="w-10 h-10 bg-[var(--bg-secondary)] rounded-lg flex items-center justify-center hover:bg-[var(--accent-tertiary)]/50 transition-colors focus-ring mx-auto"
                title="Hide page navigation"
              >
                <i class="w-4 h-4 text-[var(--text-primary)]" data-lucide="chevron-left"></i>
              </button>
            </div>

            <!-- Page Thumbnails -->
            <div class="p-3 space-y-3" id="pageThumbnails">
              <!-- Page 1 -->
              <div class="page-thumbnail active" onclick="jumpToPage(1)" data-page="1">
                <div class="thumbnail-preview">
                  <div class="page-content">
                    <div class="text-line"></div>
                    <div class="text-line short"></div>
                    <div class="text-line"></div>
                    <div class="text-line medium"></div>
                  </div>
                </div>
                <div class="page-number">1</div>
              </div>

              <!-- Page 2 -->
              <div class="page-thumbnail current" onclick="jumpToPage(2)" data-page="2">
                <div class="thumbnail-preview">
                  <div class="page-content">
                    <div class="text-line"></div>
                    <div class="text-line short"></div>
                    <div class="text-line"></div>
                    <div class="text-line medium"></div>
                  </div>
                </div>
                <div class="page-number">2</div>
              </div>

              <!-- Page 3 -->
              <div class="page-thumbnail" onclick="jumpToPage(3)" data-page="3">
                <div class="thumbnail-preview">
                  <div class="page-content">
                    <div class="text-line"></div>
                    <div class="text-line short"></div>
                    <div class="text-line"></div>
                    <div class="text-line medium"></div>
                  </div>
                </div>
                <div class="page-number">3</div>
              </div>

              <!-- Page 4 -->
              <div class="page-thumbnail" onclick="jumpToPage(4)" data-page="4">
                <div class="thumbnail-preview">
                  <div class="page-content">
                    <div class="text-line"></div>
                    <div class="text-line short"></div>
                    <div class="text-line"></div>
                    <div class="text-line medium"></div>
                  </div>
                </div>
                <div class="page-number">4</div>
              </div>

              <!-- Page 5 -->
              <div class="page-thumbnail" onclick="jumpToPage(5)" data-page="5">
                <div class="thumbnail-preview">
                  <div class="page-content">
                    <div class="text-line"></div>
                    <div class="text-line short"></div>
                    <div class="text-line"></div>
                    <div class="text-line medium"></div>
                  </div>
                </div>
                <div class="page-number">5</div>
              </div>
            </div>

            <!-- Page Controls -->
            <div class="p-3 border-t border-[var(--bg-secondary)] space-y-2">
              <button
                onclick="previousPage()"
                class="w-full h-8 bg-[var(--bg-secondary)] rounded-lg flex items-center justify-center hover:bg-[var(--accent-tertiary)]/50 transition-colors text-xs font-medium"
              >
                <i class="w-3 h-3 mr-1" data-lucide="chevron-up"></i>
              </button>
              <div class="text-xs text-center text-[var(--text-muted)] geist-mono">
                2/15
              </div>
              <button
                onclick="nextPage()"
                class="w-full h-8 bg-[var(--bg-secondary)] rounded-lg flex items-center justify-center hover:bg-[var(--accent-tertiary)]/50 transition-colors text-xs font-medium"
              >
                <i class="w-3 h-3 mr-1" data-lucide="chevron-down"></i>
              </button>
            </div>
          </aside>

          <!-- PDF Document Container -->
          <div class="flex-1 pdf-viewer-container overflow-y-auto">
            <div class="p-6">
              <div class="pdf-document hover-lift">
            <div class="pdf-page">
              <h1>Perfect Competition</h1>
              
              <p>
                Perfect competition is a market structure characterized by many
                small firms, identical products, and easy entry and exit. In such
                markets, no single firm can influence the market price, making
                them
                <span 
                  class="highlighted-text" 
                  onclick="showInsight(this, 'price-takers')"
                  data-concept="price-takers"
                >
                  price takers
                </span>
                rather than price makers.
              </p>

              <!-- Learning Insight Bubble -->
              <div class="learning-insight" id="insight-price-takers">
                <div class="flex items-start space-x-3">
                  <div
                    class="w-8 h-8 bg-[var(--accent-primary)] rounded-full flex items-center justify-center flex-shrink-0"
                  >
                    <i class="w-4 h-4 text-white" data-lucide="lightbulb"></i>
                  </div>
                  <div>
                    <div class="text-sm font-semibold text-[var(--text-primary)] mb-1">
                      Key Concept: Price Takers
                    </div>
                    <div class="text-xs text-[var(--text-secondary)] mb-2">
                      Firms in perfect competition cannot set their own prices - they must accept the market price determined by supply and demand.
                    </div>
                  </div>
                  <button
                    onclick="hideInsight('insight-price-takers')"
                    class="text-[var(--text-subtle)] hover:text-[var(--text-primary)] transition-colors"
                  >
                    <i class="w-4 h-4" data-lucide="x"></i>
                  </button>
                </div>
                <div class="insight-actions">
                  <button class="insight-btn" onclick="explainMore('price-takers')">
                    Explain More
                  </button>
                  <button class="insight-btn" onclick="giveExample('price-takers')">
                    Give Example
                  </button>
                  <button class="insight-btn" onclick="quizMe('price-takers')">
                    Quiz Me
                  </button>
                </div>
              </div>

              <p>The key characteristics of perfect competition include:</p>
              <ul>
                <li>Many buyers and sellers</li>
                <li>Homogeneous products</li>
                <li>Perfect information</li>
                <li>Free entry and exit</li>
              </ul>

              <p>
                When firms operate under perfect competition, they face a
                perfectly elastic demand curve. This means that the
                <span 
                  class="highlighted-text"
                  onclick="showInsight(this, 'marginal-revenue')"
                  data-concept="marginal-revenue"
                >
                  marginal revenue equals the market price
                </span>
                for each unit sold.
              </p>

              <!-- Second Learning Insight -->
              <div class="learning-insight" id="insight-marginal-revenue">
                <div class="flex items-start space-x-3">
                  <div
                    class="w-8 h-8 bg-[var(--accent-primary)] rounded-full flex items-center justify-center flex-shrink-0"
                  >
                    <i class="w-4 h-4 text-white" data-lucide="trending-up"></i>
                  </div>
                  <div>
                    <div class="text-sm font-semibold text-[var(--text-primary)] mb-1">
                      Marginal Revenue = Price
                    </div>
                    <div class="text-xs text-[var(--text-secondary)] mb-2">
                      In perfect competition, since firms are price takers, each additional unit sold brings in exactly the market price as revenue.
                    </div>
                  </div>
                  <button
                    onclick="hideInsight('insight-marginal-revenue')"
                    class="text-[var(--text-subtle)] hover:text-[var(--text-primary)] transition-colors"
                  >
                    <i class="w-4 h-4" data-lucide="x"></i>
                  </button>
                </div>
                <div class="insight-actions">
                  <button class="insight-btn" onclick="explainMore('marginal-revenue')">
                    Show Graph
                  </button>
                  <button class="insight-btn" onclick="giveExample('marginal-revenue')">
                    Calculate Example
                  </button>
                  <button class="insight-btn" onclick="quizMe('marginal-revenue')">
                    Practice Problem
                  </button>
                </div>
              </div>

              <h2>Short-run Equilibrium</h2>
              <p>
                In the short run, a perfectly competitive firm will continue to
                produce as long as the market price covers its variable costs. The
                firm maximizes profit by producing where marginal cost equals
                marginal revenue.
              </p>

              <h3>Profit Maximization Rule</h3>
              <p>
                The fundamental rule for profit maximization in perfect competition
                is to produce at the output level where:
              </p>
              <p style="text-align: center; font-weight: 600; color: var(--accent-primary); font-size: 1.125rem; margin: 1.5rem 0;">
                MC = MR = P
              </p>
              <p>
                Where MC is marginal cost, MR is marginal revenue, and P is the market price.
              </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      </div>

      <!-- AI Learning Companion -->
      <aside
        class="ai-panel w-96 bg-[var(--bg-secondary)] border-l border-[var(--accent-tertiary)]/50 flex flex-col transition-all duration-500"
      >
        <!-- AI Header -->
        <div
          class="bg-[var(--accent-primary)] text-white p-6 relative overflow-hidden"
        >
          <div class="absolute inset-0 minimal-artwork-bg opacity-20"></div>
          <div class="relative z-10">
            <div class="flex items-center space-x-3 mb-4">
              <div
                class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center breathing-animation"
              >
                <i class="w-5 h-5 text-white" data-lucide="brain"></i>
              </div>
              <div>
                <h3 class="font-semibold">Study Companion</h3>
                <p class="text-sm text-white/80">Following your reading</p>
              </div>
            </div>
            <div class="bg-white/20 rounded-lg p-3">
              <div class="text-sm text-white/90 mb-1">Current Focus</div>
              <div class="text-xs text-white/70">
                Perfect Competition - Short-run Equilibrium
              </div>
            </div>
          </div>
        </div>

        <!-- AI Content -->
        <div class="flex-1 overflow-y-auto p-6 space-y-4">
          <!-- Context Card -->
          <div
            class="bg-white rounded-xl p-4 border border-gray-100 minimal-artwork-subtle hover-lift"
          >
            <div class="flex items-start space-x-3">
              <div
                class="w-8 h-8 bg-[var(--accent-primary)] rounded-full flex items-center justify-center flex-shrink-0"
              >
                <i class="w-4 h-4 text-white" data-lucide="compass"></i>
              </div>
              <div>
                <h4 class="font-medium text-[var(--text-primary)] mb-1">
                  Building on Previous Knowledge
                </h4>
                <p class="text-sm text-[var(--text-muted)]">
                  This chapter builds on supply & demand concepts from Chapter 2. Notice how market structure affects pricing power.
                </p>
              </div>
            </div>
          </div>

          <!-- Current Insight -->
          <div class="bg-[var(--accent-tertiary)]/30 rounded-xl p-4">
            <div class="flex items-center space-x-2 mb-2">
              <i class="w-4 h-4 text-[var(--highlight)]" data-lucide="lightbulb"></i>
              <h4 class="font-medium text-[var(--text-primary)]">Key Insight</h4>
            </div>
            <p class="text-sm text-[var(--text-muted)] mb-3">
              Perfect competition is theoretical but helps us understand how real markets work. Most markets fall somewhere between perfect competition and monopoly.
            </p>
            <button
              class="text-xs bg-[var(--accent-primary)] text-white px-3 py-1 rounded-full hover:bg-[var(--highlight)] transition-colors"
              onclick="showRealWorldExamples()"
            >
              Show real examples →
            </button>
          </div>

          <!-- Study Progress -->
          <div class="bg-white rounded-xl p-4 border border-gray-100">
            <div class="flex items-center justify-between mb-3">
              <h4 class="font-medium text-[var(--text-primary)]">
                Study Progress
              </h4>
              <span class="text-sm text-[var(--text-muted)]">2 of 8 sections</span>
            </div>
            <div class="space-y-2">
              <div class="flex items-center justify-between text-sm">
                <span class="text-[var(--text-secondary)]">Concepts mastered</span>
                <span class="text-[var(--accent-primary)] font-medium">3/12</span>
              </div>
              <div class="bg-[var(--bg-secondary)] rounded-full h-2">
                <div class="bg-[var(--accent-primary)] h-2 rounded-full" style="width: 25%"></div>
              </div>
            </div>
          </div>

          <!-- Quick Notes -->
          <div class="bg-white rounded-xl p-4 border border-gray-100">
            <div class="flex items-center justify-between mb-3">
              <h4 class="font-medium text-[var(--text-primary)]">
                Quick Notes
              </h4>
              <button
                class="text-[var(--accent-primary)] hover:text-[var(--highlight)] transition-colors"
                onclick="addNote()"
              >
                <i class="w-4 h-4" data-lucide="plus"></i>
              </button>
            </div>
            <div class="space-y-2">
              <div class="bg-[var(--bg-primary)] rounded-lg p-3 text-sm">
                <div class="flex items-center justify-between mb-1">
                  <span class="text-xs text-[var(--text-muted)]">Auto-saved</span>
                  <button class="text-[var(--text-muted)] hover:text-[var(--text-primary)]">
                    <i class="w-3 h-3" data-lucide="x"></i>
                  </button>
                </div>
                <p class="text-[var(--text-secondary)]">
                  Price takers = firms cannot influence market price
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- AI Input -->
        <div class="p-6 border-t border-[var(--accent-tertiary)]/50 bg-white">
          <div class="relative mb-4">
            <textarea
              class="w-full bg-[var(--bg-primary)] border border-[var(--bg-secondary)] rounded-lg p-3 pr-12 text-sm resize-none focus:outline-none focus:bg-white focus:border-[var(--accent-primary)]/50 focus:ring-4 focus:ring-[var(--accent-primary)]/10 transition-all duration-300"
              rows="3"
              placeholder="Ask questions about this section, request examples, or get clarification..."
            ></textarea>
            <button
              class="absolute bottom-3 right-3 w-8 h-8 bg-[var(--accent-primary)] rounded-full flex items-center justify-center text-white hover:bg-[var(--highlight)] transition-colors"
            >
              <i class="w-4 h-4" data-lucide="send"></i>
            </button>
          </div>

          <!-- Quick Actions -->
          <div class="grid grid-cols-2 gap-2">
            <button
              class="sidebar-card-item p-3 text-xs text-center border border-[var(--bg-secondary)] hover:bg-[var(--bg-secondary)] transition-colors rounded-lg"
            >
              <i
                class="w-4 h-4 mx-auto mb-1 text-[var(--accent-primary)]"
                data-lucide="bookmark"
              ></i>
              Make Flashcards
            </button>
            <button
              class="sidebar-card-item p-3 text-xs text-center border border-[var(--bg-secondary)] hover:bg-[var(--bg-secondary)] transition-colors rounded-lg"
            >
              <i
                class="w-4 h-4 mx-auto mb-1 text-[var(--accent-primary)]"
                data-lucide="help-circle"
              ></i>
              Quiz Me
            </button>
          </div>
        </div>
      </aside>
    </div>

    <script>
      lucide.createIcons();

      let isFocusMode = false;
      let currentZoom = 100;

      // Enhanced staggered animations
      const staggeredItems = document.querySelectorAll(".stagger-entrance");
      staggeredItems.forEach((item) => {
        const children = Array.from(item.children);
        children.forEach((child, index) => {
          child.style.animationDelay = `${index * 0.075}s`;
        });
      });

      function showInsight(element, conceptId) {
        // Hide all insights first
        document.querySelectorAll(".learning-insight").forEach((insight) => {
          insight.classList.remove("show");
        });

        // Show the specific insight
        const insight = document.getElementById(`insight-${conceptId}`);
        if (insight) {
          // Position the insight relative to the clicked element
          const rect = element.getBoundingClientRect();
          const container = element.offsetParent;
          
          setTimeout(() => {
            insight.classList.add("show");
          }, 100);
        }
      }

      function hideInsight(insightId) {
        const insight = document.getElementById(insightId);
        if (insight) {
          insight.classList.remove("show");
        }
      }

      function explainMore(concept) {
        console.log(`Explaining more about: ${concept}`);
        // Trigger AI explanation
      }

      function giveExample(concept) {
        console.log(`Giving example for: ${concept}`);
        // Trigger AI example
      }

      function quizMe(concept) {
        console.log(`Creating quiz for: ${concept}`);
        // Trigger AI quiz
      }

      function toggleFocus() {
        const container = document.querySelector(".flex");
        const aiPanel = document.querySelector(".ai-panel");
        const pageNavPanel = document.querySelector(".page-nav-panel");
        const toggleBtn = document.getElementById("focusToggle");
        const pageNavToggle = document.getElementById("pageNavToggle");

        isFocusMode = !isFocusMode;

        if (isFocusMode) {
          container.classList.add("focus-mode");
          
          // Hide panels with transition
          aiPanel.style.transition = "opacity 0.3s ease";
          aiPanel.style.opacity = "0";
          pageNavPanel.style.transition = "opacity 0.3s ease";
          pageNavPanel.style.opacity = "0";
          
          setTimeout(() => {
            aiPanel.style.display = "none";
            pageNavPanel.style.display = "none";
            // Hide the floating toggle in focus mode
            pageNavToggle.classList.remove("show");
          }, 300);
          
          toggleBtn.innerHTML =
            '<i class="w-4 h-4 mr-2" data-lucide="minimize"></i>Exit Focus';
        } else {
          // Show panels
          aiPanel.style.display = "flex";
          aiPanel.style.opacity = "0";
          pageNavPanel.style.display = "flex";
          pageNavPanel.style.opacity = "0";
          
          setTimeout(() => {
            aiPanel.style.opacity = "1";
            pageNavPanel.style.opacity = "1";
            container.classList.remove("focus-mode");
            // Show the floating toggle if page nav was collapsed
            if (pageNavPanel.classList.contains("collapsed")) {
              pageNavToggle.classList.add("show");
            }
          }, 50);
          
          toggleBtn.innerHTML =
            '<i class="w-4 h-4 mr-2" data-lucide="focus"></i>Focus Mode';
        }
        lucide.createIcons();
      }

      function setZoom(percentage) {
        currentZoom = percentage;
        document.querySelectorAll(".zoom-btn").forEach((btn) => {
          btn.classList.remove("active");
        });
        event.target.classList.add("active");
        
        const pdfDocument = document.querySelector(".pdf-document");
        pdfDocument.style.transform = `scale(${percentage / 100})`;
        pdfDocument.style.transformOrigin = "top center";
      }

      function togglePageNav() {
        const pageNavPanel = document.getElementById("pageNavPanel");
        const toggleButton = document.getElementById("pageNavToggle");
        
        pageNavPanel.classList.toggle("collapsed");
        
        if (pageNavPanel.classList.contains("collapsed")) {
          // Show the floating toggle button when sidebar is hidden
          toggleButton.classList.add("show");
        } else {
          // Hide the floating toggle button when sidebar is visible
          toggleButton.classList.remove("show");
        }
      }

      function jumpToPage(pageNumber) {
        // Remove current from all pages
        document.querySelectorAll(".page-thumbnail").forEach((el) => {
          el.classList.remove("current");
        });

        // Add current to selected page
        const selectedPage = document.querySelector(`[data-page="${pageNumber}"]`);
        if (selectedPage) {
          selectedPage.classList.add("current");
        }

        // Update progress based on page
        const progressPercents = [6, 13, 20, 27, 34, 41, 48, 55, 62, 69, 76, 83, 90, 97, 100];
        if (progressPercents[pageNumber - 1] !== undefined) {
          document.getElementById("readingProgress").style.width =
            progressPercents[pageNumber - 1] + "%";
        }

        // Update page counter
        const pageCounter = document.querySelector(".geist-mono");
        if (pageCounter) {
          pageCounter.textContent = `${pageNumber}/15`;
        }

        // Simulate page content change
        console.log(`Jumped to page ${pageNumber}`);
      }

      function previousPage() {
        const currentPage = document.querySelector(".page-thumbnail.current");
        if (currentPage) {
          const currentPageNum = parseInt(currentPage.dataset.page);
          if (currentPageNum > 1) {
            jumpToPage(currentPageNum - 1);
          }
        }
      }

      function nextPage() {
        const currentPage = document.querySelector(".page-thumbnail.current");
        if (currentPage) {
          const currentPageNum = parseInt(currentPage.dataset.page);
          if (currentPageNum < 15) {
            jumpToPage(currentPageNum + 1);
          }
        }
      }

      function goBack() {
        window.location.href = "course.html";
      }

      function showRealWorldExamples() {
        console.log("Showing real-world examples of perfect competition");
      }

      function addNote() {
        console.log("Adding a new note");
      }

      // Hide insights when clicking elsewhere
      document.addEventListener("click", function (e) {
        if (
          !e.target.closest(".highlighted-text") &&
          !e.target.closest(".learning-insight")
        ) {
          document.querySelectorAll(".learning-insight").forEach((insight) => {
            insight.classList.remove("show");
          });
        }
      });

      // Enhanced click handlers with feedback
      const interactiveElements = document.querySelectorAll(
        'button, [tabindex="0"]'
      );
      interactiveElements.forEach((element) => {
        element.addEventListener("click", function (e) {
          this.style.transform = "scale(0.98)";
          setTimeout(() => {
            this.style.transform = "";
          }, 150);
        });

        element.addEventListener("keydown", function (e) {
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            this.click();
          }
        });
      });

      // Keyboard shortcuts
      document.addEventListener("keydown", function (e) {
        if (e.key === "f" && !e.ctrlKey) {
          e.preventDefault();
          toggleFocus();
        } else if (e.key === "ArrowUp" || e.key === "PageUp") {
          e.preventDefault();
          previousPage();
        } else if (e.key === "ArrowDown" || e.key === "PageDown") {
          e.preventDefault();
          nextPage();
        } else if (e.key === "Home") {
          e.preventDefault();
          jumpToPage(1);
        } else if (e.key === "End") {
          e.preventDefault();
          jumpToPage(15);
        }
      });

      // Simulate reading progress updates
      let progress = 13;
      setInterval(() => {
        if (progress < 100 && !isFocusMode) {
          progress += 0.1;
          document.getElementById("readingProgress").style.width = progress + "%";
        }
      }, 5000);
    </script>
  </body>
</html>