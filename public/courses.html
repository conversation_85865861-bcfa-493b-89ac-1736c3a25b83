<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>StudyMind - My Courses</title>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Geist:wght@100..900&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Geist+Mono:wght@100..900&family=Geist:wght@100..900&display=swap"
      rel="stylesheet"
    />

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>
    <style type="text/tailwindcss">
      :root {
        --bg-primary: #fdfbf6;
        --bg-secondary: #f0e5d8;
        --text-primary: #2d3748;
        --text-secondary: #4a5568;
        --text-muted: #718096;
        --text-subtle: #a0aec0;
        --text-on-tinted: #2d3748;
        --text-on-tinted-muted: #4a5568;
        --text-on-tinted-subtle: #718096;
        --accent-primary: #a9c2b9;
        --accent-secondary: #d3b8ae;
        --accent-tertiary: #e6cba5;
        --highlight: #8b9d8f;
        --success: #68d391;
        --warning: #f6ad55;
        --info: #63b3ed;
      }

      body {
        font-family: "Geist", sans-serif;
        background-color: var(--bg-primary);
        color: var(--text-primary);
      }
      .geist-mono {
        font-family: "Geist Mono", monospace;
      }
      .animated-gradient {
        background: linear-gradient(
          -45deg,
          var(--accent-tertiary),
          var(--bg-secondary),
          var(--accent-primary),
          var(--highlight)
        );
        background-size: 400% 400%;
        animation: gradientBG 20s ease infinite;
      }
      @keyframes gradientBG {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }
      .stagger-entrance > * {
        opacity: 0;
        transform: translateY(20px);
        animation: fadeInUp 0.6s forwards;
      }
      @keyframes fadeInUp {
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      .hover-lift {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .hover-lift:hover {
        transform: translateY(-5px) scale(1.02);
        box-shadow: 0 12px 20px -5px rgba(45, 55, 72, 0.15),
          0 6px 8px -3px rgba(45, 55, 72, 0.1);
      }
      .sidebar-card-item:hover {
        background-color: var(--bg-secondary);
        transform: translateX(2px);
      }
      .nav-link-active {
        color: var(--text-primary);
        font-weight: 600;
        border-bottom-width: 2px;
        border-color: var(--accent-primary);
        padding-bottom: 0.25rem;
      }
      .nav-link {
        color: var(--text-muted);
        font-weight: 500;
      }
      .nav-link:hover {
        color: var(--text-secondary);
        transition-duration: 300ms;
      }
      .icon-primary {
        color: var(--accent-primary);
      }
      .icon-secondary {
        color: var(--accent-secondary);
      }
      .text-highlight {
        color: var(--highlight);
        font-weight: 600;
      }
      .minimal-artwork-bg {
        background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23e6cba5' fill-opacity='0.1'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-repeat: repeat;
        background-size: 200px 200px;
      }
      .minimal-artwork-subtle {
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23a9c2b9' fill-opacity='0.07'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-repeat: repeat;
      }

      /* Enhanced button styles */
      .btn-primary {
        background: var(--accent-primary);
        color: white;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        border-radius: 0.75rem;
        transition: all 0.2s ease;
        border: none;
        cursor: pointer;
      }

      .btn-primary:hover {
        background: var(--highlight);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(169, 194, 185, 0.3);
      }

      /* Better focus states */
      .focus-ring:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(169, 194, 185, 0.3);
      }

      /* Course Cards */
      .course-card {
        background: white;
        border: 1px solid rgba(169, 194, 185, 0.15);
        border-radius: 1.5rem;
        padding: 2rem;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.03);
        min-height: 320px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }

      .course-card:hover {
        border-color: var(--accent-primary);
        background: white;
        transform: translateY(-8px);
        box-shadow: 0 16px 32px rgba(169, 194, 185, 0.15);
      }

      /* Text overflow handling */
      .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      /* Course Grid */
      .courses-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
        gap: 1.5rem;
      }

      .course-card {
        background: var(--card);
        border: 1px solid var(--border);
        border-radius: 1rem;
        padding: 1.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.02);
      }

      .course-card:hover {
        border-color: var(--primary);
        background: var(--primary-light);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.05);
      }

      .course-status {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: var(--primary);
        color: var(--primary-foreground);
        padding: 0.25rem 0.5rem;
        border-radius: 0.75rem;
        font-size: 0.625rem;
        font-weight: 600;
      }

      .course-status.paused {
        background: #f59e0b;
        color: #ffffff;
      }

      .course-status.completed {
        background: #3b82f6;
        color: #ffffff;
      }

      .course-header {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        margin-bottom: 1rem;
      }

      .course-icon {
        width: 60px;
        height: 60px;
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.75rem;
        flex-shrink: 0;
      }

      .course-icon.economics {
        background: var(--primary);
        color: var(--primary-foreground);
      }

      .course-icon.physics {
        background: linear-gradient(135deg, #f59e0b 0%, #ef4444 100%);
        color: #ffffff;
      }

      .course-icon.computer-science {
        background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
        color: #ffffff;
      }

      .course-icon.calculus {
        background: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
        color: #ffffff;
      }

      .course-icon.chemistry {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: #ffffff;
      }

      .course-info {
        flex: 1;
      }

      .course-title {
        font-size: 1.125rem;
        font-weight: 600;
        margin-bottom: 0.375rem;
        line-height: 1.3;
        color: var(--foreground);
      }

      .course-instructor {
        color: var(--muted-foreground);
        font-size: 0.875rem;
        margin-bottom: 0.25rem;
      }

      .course-meta {
        color: var(--muted-foreground);
        font-size: 0.75rem;
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;
      }

      .course-description {
        color: var(--muted-foreground);
        font-size: 0.875rem;
        line-height: 1.4;
        margin-bottom: 1rem;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .course-progress {
        margin-bottom: 1rem;
      }

      .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
      }

      .progress-text {
        color: var(--muted-foreground);
        font-size: 0.8125rem;
        font-weight: 500;
      }

      .progress-percentage {
        color: var(--primary);
        font-weight: 600;
        font-size: 0.8125rem;
      }

      .progress-bar {
        width: 100%;
        height: 6px;
        background: var(--border);
        border-radius: 3px;
        overflow: hidden;
      }

      .progress-fill {
        height: 100%;
        background: var(--primary);
        border-radius: 3px;
        transition: width 0.3s ease;
      }

      .course-actions {
        display: flex;
        gap: 0.75rem;
        align-items: center;
      }

      .course-action-btn {
        background: var(--secondary);
        border: 1px solid var(--border);
        color: var(--foreground);
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        cursor: pointer;
        font-size: 0.75rem;
        font-weight: 500;
        transition: all 0.2s;
        flex: 1;
      }

      .course-action-btn.primary {
        background: var(--primary);
        color: var(--primary-foreground);
        border-color: var(--primary);
      }

      .course-action-btn:hover {
        background: #d1d5db;
        border-color: #d1d5db;
      }

      .course-action-btn.primary:hover {
        background: #059669;
        border-color: #059669;
      }

      .course-stats {
        display: flex;
        gap: 1rem;
        margin-top: 0.75rem;
        padding-top: 0.75rem;
        border-top: 1px solid var(--border);
      }

      .course-stat {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.75rem;
        color: var(--muted-foreground);
      }

      .course-stat-icon {
        font-size: 0.875rem;
      }

      /* AI Recommendations */
      .ai-recommendations {
        background: var(--card);
        border: 1px solid var(--border);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.02);
      }

      .ai-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
      }

      .ai-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--primary);
        color: var(--primary-foreground);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.125rem;
      }

      .ai-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--foreground);
      }

      .recommendations-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
      }

      .recommendation-item {
        background: var(--secondary);
        border: 1px solid var(--border);
        border-radius: 0.75rem;
        padding: 1rem;
        cursor: pointer;
        transition: all 0.2s;
      }

      .recommendation-item:hover {
        border-color: var(--primary);
        background: var(--primary-light);
      }

      .recommendation-icon {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
      }

      .recommendation-title {
        font-size: 0.875rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
        color: var(--foreground);
      }

      .recommendation-desc {
        font-size: 0.75rem;
        color: var(--muted-foreground);
        line-height: 1.4;
      }

      /* Responsive Design */
      @media (max-width: 1200px) {
        .courses-grid {
          grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
        }
      }

      @media (max-width: 768px) {
        .header-content {
          flex-direction: column;
          gap: 1rem;
        }

        .nav-items {
          order: -1;
        }

        .page-header-content,
        .courses-controls,
        .main-content {
          padding-left: 1rem;
          padding-right: 1rem;
        }

        .courses-controls {
          flex-direction: column;
          align-items: stretch;
          gap: 1rem;
        }

        .search-container {
          max-width: none;
        }

        .filter-tabs {
          justify-content: center;
          flex-wrap: wrap;
        }

        .courses-grid {
          grid-template-columns: 1fr;
        }

        .page-stats {
          justify-content: space-around;
        }

        .recommendations-grid {
          grid-template-columns: 1fr;
        }
      }

      /* Loading States */
      .loading-card {
        background: var(--card);
        border: 1px solid var(--border);
        border-radius: 1rem;
        padding: 1.5rem;
        opacity: 0.6;
      }

      .loading-shimmer {
        background: linear-gradient(
          90deg,
          var(--border) 25%,
          #d1d5db 50%,
          var(--border) 75%
        );
        background-size: 200% 100%;
        animation: shimmer 1.5s infinite;
        border-radius: 0.25rem;
      }

      @keyframes shimmer {
        0% {
          background-position: -200% 0;
        }
        100% {
          background-position: 200% 0;
        }
      }
    </style>
  </head>
  <body class="text-[var(--text-primary)]">
    <div class="flex h-screen">
      <!-- Sidebar -->
      <aside
        class="w-96 bg-[var(--bg-secondary)] p-7 border-r border-[var(--accent-tertiary)]/50 overflow-y-auto space-y-7"
      >
        <!-- AI Learning Assistant -->
        <div
          class="bg-[var(--accent-primary)] text-white p-7 rounded-2xl shadow-xl stagger-entrance relative overflow-hidden"
        >
          <div
            class="absolute inset-0 minimal-artwork-bg opacity-20"
            style="background-size: 100px 100px"
          ></div>
          <div class="relative z-10 flex items-center space-x-4 mb-4">
            <i class="w-10 h-10 text-white" data-lucide="brain-circuit"></i>
            <div>
              <h3 class="text-xl font-semibold text-white">
                AI Learning Assistant
              </h3>
              <p class="text-sm text-white/80">Your personal guide</p>
            </div>
          </div>
          <div
            class="relative z-10 bg-white/20 hover:bg-white/30 p-4 rounded-lg mb-3 cursor-pointer transition-all duration-300 ease-in-out group sidebar-card-item focus-ring"
            style="animation-delay: 0.1s"
            tabindex="0"
          >
            <div class="flex items-start space-x-3">
              <i
                class="w-5 h-5 text-white/90 mt-1 flex-shrink-0 group-hover:text-white transition-colors"
                data-lucide="sparkles"
              ></i>
              <div>
                <h4 class="text-md font-semibold text-white">
                  Continue Economics
                </h4>
                <p class="text-xs text-white/80 mt-1 leading-relaxed">
                  You're excelling at supply & demand. Next up: Market
                  Structures.
                </p>
              </div>
            </div>
          </div>
          <div
            class="relative z-10 bg-white/20 hover:bg-white/30 p-4 rounded-lg mb-3 cursor-pointer transition-all duration-300 ease-in-out group sidebar-card-item focus-ring"
            style="animation-delay: 0.2s"
            tabindex="0"
          >
            <div class="flex items-start space-x-3">
              <i
                class="w-5 h-5 text-white/90 mt-1 flex-shrink-0 group-hover:text-white transition-colors"
                data-lucide="bar-chart-3"
              ></i>
              <div>
                <h4 class="text-md font-semibold text-white">
                  Study Pattern Analysis
                </h4>
                <p class="text-xs text-white/80 mt-1 leading-relaxed">
                  Your peak learning time is 2-4 PM. Perfect for next lecture!
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Filters -->
        <div
          class="bg-white p-7 rounded-2xl shadow-xl stagger-entrance minimal-artwork-subtle border border-gray-100"
        >
          <h3
            class="text-xl font-semibold text-[var(--text-primary)] mb-5 flex items-center"
          >
            <i class="w-6 h-6 mr-3 icon-primary" data-lucide="filter"></i>
            Quick Filters
          </h3>
          <div class="space-y-2">
            <button
              class="w-full flex items-center justify-between text-[var(--text-secondary)] hover:bg-[var(--bg-primary)] p-3 rounded-lg transition-colors text-sm sidebar-card-item focus-ring filter-btn active"
              data-filter="all"
            >
              <span class="font-medium">All Courses</span>
              <span
                class="text-xs bg-[var(--accent-primary)] text-white px-2 py-1 rounded-full geist-mono"
                >7</span
              >
            </button>
            <button
              class="w-full flex items-center justify-between text-[var(--text-secondary)] hover:bg-[var(--bg-primary)] p-3 rounded-lg transition-colors text-sm sidebar-card-item focus-ring filter-btn"
              data-filter="active"
            >
              <span class="font-medium">Active</span>
              <span
                class="text-xs bg-[var(--success)] text-white px-2 py-1 rounded-full geist-mono"
                >5</span
              >
            </button>
            <button
              class="w-full flex items-center justify-between text-[var(--text-secondary)] hover:bg-[var(--bg-primary)] p-3 rounded-lg transition-colors text-sm sidebar-card-item focus-ring filter-btn"
              data-filter="completed"
            >
              <span class="font-medium">Completed</span>
              <span
                class="text-xs bg-[var(--info)] text-white px-2 py-1 rounded-full geist-mono"
                >2</span
              >
            </button>
            <button
              class="w-full flex items-center justify-between text-[var(--text-secondary)] hover:bg-[var(--bg-primary)] p-3 rounded-lg transition-colors text-sm sidebar-card-item focus-ring filter-btn"
              data-filter="paused"
            >
              <span class="font-medium">Paused</span>
              <span
                class="text-xs bg-[var(--warning)] text-white px-2 py-1 rounded-full geist-mono"
                >1</span
              >
            </button>
          </div>
        </div>

        <!-- Study Tools -->
        <div
          class="bg-white p-7 rounded-2xl shadow-xl stagger-entrance minimal-artwork-subtle border border-gray-100"
        >
          <h3
            class="text-xl font-semibold text-[var(--text-primary)] mb-5 flex items-center"
          >
            <i class="w-6 h-6 mr-3 icon-secondary" data-lucide="zap"></i>
            Study Tools
          </h3>
          <div class="grid grid-cols-2 gap-4 text-center">
            <div
              class="bg-[var(--bg-primary)] p-4 rounded-lg hover:bg-[var(--accent-tertiary)]/50 transition-colors cursor-pointer sidebar-card-item focus-ring"
              tabindex="0"
            >
              <i
                class="w-8 h-8 icon-primary mx-auto mb-1.5"
                data-lucide="clipboard-check"
              ></i>
              <p class="text-sm font-semibold text-[var(--text-primary)]">
                AI Quiz
              </p>
              <p class="text-xs text-[var(--text-muted)]">Test knowledge</p>
            </div>
            <div
              class="bg-[var(--bg-primary)] p-4 rounded-lg hover:bg-[var(--accent-tertiary)]/50 transition-colors cursor-pointer sidebar-card-item focus-ring"
              tabindex="0"
            >
              <i
                class="w-8 h-8 icon-primary mx-auto mb-1.5"
                data-lucide="copy"
              ></i>
              <p class="text-sm font-semibold text-[var(--text-primary)]">
                Flashcards
              </p>
              <p class="text-xs text-[var(--text-muted)]">Quick review</p>
            </div>
            <div
              class="bg-[var(--bg-primary)] p-4 rounded-lg hover:bg-[var(--accent-tertiary)]/50 transition-colors cursor-pointer sidebar-card-item focus-ring"
              tabindex="0"
            >
              <i
                class="w-8 h-8 icon-primary mx-auto mb-1.5"
                data-lucide="edit-3"
              ></i>
              <p class="text-sm font-semibold text-[var(--text-primary)]">
                Smart Notes
              </p>
              <p class="text-xs text-[var(--text-muted)]">AI-enhanced</p>
            </div>
            <div
              class="bg-[var(--bg-primary)] p-4 rounded-lg hover:bg-[var(--accent-tertiary)]/50 transition-colors cursor-pointer sidebar-card-item focus-ring"
              tabindex="0"
            >
              <i
                class="w-8 h-8 icon-primary mx-auto mb-1.5"
                data-lucide="trending-up"
              ></i>
              <p class="text-sm font-semibold text-[var(--text-primary)]">
                Progress
              </p>
              <p class="text-xs text-[var(--text-muted)]">Track learning</p>
            </div>
          </div>
        </div>
      </aside>

      <!-- Main Content -->
      <main class="flex-1 p-8 overflow-y-auto">
        <!-- Header -->
        <header class="flex justify-between items-center mb-10">
          <div class="flex items-center space-x-3">
            <i class="w-10 h-10 icon-primary" data-lucide="brain"></i>
            <h1 class="text-3xl font-bold text-[var(--text-primary)]">
              StudyMind
            </h1>
          </div>
          <nav class="flex items-center space-x-8 text-md font-medium">
            <a class="nav-link" href="/dashboard.html">Dashboard</a>
            <a class="nav-link-active" href="#"> My Courses</a>
            <a class="nav-link" href="#">Study Tools</a>
            <a class="nav-link" href="#">Progress</a>
            <a class="nav-link" href="#">Community</a>
          </nav>
          <div class="flex items-center space-x-4">
            <div class="relative">
              <i
                class="w-6 h-6 text-[var(--text-muted)] hover:text-[var(--accent-primary)] cursor-pointer transition-colors focus-ring"
                data-lucide="bell"
                tabindex="0"
              ></i>
              <span
                class="absolute -top-1.5 -right-1.5 w-2.5 h-2.5 bg-red-500 rounded-full border-2 border-[var(--bg-primary)]"
              ></span>
            </div>
            <div class="flex items-center space-x-3">
              <div
                class="w-10 h-10 bg-[var(--accent-secondary)] rounded-full flex items-center justify-center text-white text-lg font-semibold shadow-sm"
              >
                <img
                  src="/images/profile-avatar.png"
                  alt="Profile Avatar"
                  class="w-8 h-8 rounded-full"
                />
              </div>
              <div>
                <p class="text-md font-semibold text-[var(--text-primary)]">
                  Sarah Johnes
                </p>
                <p class="text-xs text-[var(--text-muted)]">Premium Plan</p>
              </div>
            </div>
          </div>
        </header>

        <!-- Hero Section -->
        <section
          class="animated-gradient p-10 rounded-2xl shadow-xl mb-10 stagger-entrance relative overflow-hidden"
        >
          <div class="absolute inset-0 minimal-artwork-bg opacity-50"></div>
          <div class="relative z-10 flex justify-between items-start">
            <div>
              <h2
                class="text-4xl font-bold text-white mb-3"
                style="animation-delay: 0.1s"
              >
                Your Learning Journey
              </h2>
              <p
                class="text-white/90 text-lg mb-6"
                style="animation-delay: 0.2s"
              >
                Continue where you left off or explore new academic frontiers.
              </p>
            </div>
            <i
              class="w-12 h-12 text-white opacity-80"
              data-lucide="graduation-cap"
              style="animation-delay: 0.3s"
            ></i>
          </div>
          <div
            class="relative z-10 grid grid-cols-1 lg:grid-cols-2 lg:grid-cols-4 gap-6 mt-8 text-center"
          >
            <div
              class="bg-white/90 backdrop-blur-sm p-5 rounded-xl shadow-lg hover-lift border border-white/20"
              style="animation-delay: 0.4s"
            >
              <p
                class="geist-mono text-4xl font-bold text-[var(--text-primary)]"
              >
                5
              </p>
              <p class="text-sm text-[var(--text-muted)] mt-1 font-medium">
                Active Courses
              </p>
            </div>
            <div
              class="bg-white/90 backdrop-blur-sm p-5 rounded-xl shadow-lg hover-lift border border-white/20"
              style="animation-delay: 0.5s"
            >
              <p
                class="geist-mono text-4xl font-bold text-[var(--accent-primary)]"
              >
                89%
              </p>
              <p class="text-sm text-[var(--text-muted)] mt-1 font-medium">
                Avg Completion
              </p>
            </div>
            <div
              class="bg-white/90 backdrop-blur-sm p-5 rounded-xl shadow-lg hover-lift border border-white/20"
              style="animation-delay: 0.6s"
            >
              <p
                class="geist-mono text-4xl font-bold text-[var(--text-primary)]"
              >
                2
              </p>
              <p class="text-sm text-[var(--text-muted)] mt-1 font-medium">
                Completed
              </p>
            </div>
            <div
              class="bg-white/90 backdrop-blur-sm p-5 rounded-xl shadow-lg hover-lift border border-white/20"
              style="animation-delay: 0.7s"
            >
              <p
                class="geist-mono text-4xl font-bold text-[var(--text-primary)]"
              >
                147h
              </p>
              <p class="text-sm text-[var(--text-muted)] mt-1 font-medium">
                Study Time
              </p>
            </div>
          </div>
        </section>

        <!-- Search and Sort -->
        <div class="flex justify-between items-center mb-10">
          <div class="relative flex-1 max-w-lg">
            <div class="relative">
              <input
                type="text"
                class="w-full bg-white/70 backdrop-blur-sm border border-[var(--accent-primary)]/20 rounded-2xl px-6 py-4 pl-14 text-[var(--text-primary)] placeholder-[var(--text-muted)] focus:outline-none focus:bg-white focus:border-[var(--accent-primary)]/50 focus:ring-4 focus:ring-[var(--accent-primary)]/10 transition-all duration-300 shadow-sm hover:shadow-md"
                placeholder="Search courses, instructors, or topics..."
                id="searchInput"
              />
              <i
                class="absolute left-5 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--accent-primary)]/60"
                data-lucide="search"
              ></i>
            </div>
          </div>
          <div class="ml-8">
            <select
              class="bg-white/70 backdrop-blur-sm border border-[var(--accent-primary)]/20 rounded-2xl px-6 py-4 text-[var(--text-primary)] focus:outline-none focus:bg-white focus:border-[var(--accent-primary)]/50 focus:ring-4 focus:ring-[var(--accent-primary)]/10 cursor-pointer transition-all duration-300 shadow-sm hover:shadow-md appearance-none bg-no-repeat bg-right pr-12"
              onchange="sortCourses(this.value)"
              style="
                background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; width=&quot;16&quot; height=&quot;16&quot; fill=&quot;%23a9c2b9&quot; viewBox=&quot;0 0 16 16&quot;><path d=&quot;M8 10.5L4 6.5h8L8 10.5z&quot;/></svg>');
                background-position: right 16px center;
              "
            >
              <option value="recent">Recently Accessed</option>
              <option value="progress">Progress</option>
              <option value="alphabetical">Alphabetical</option>
              <option value="completion">Completion Date</option>
            </select>
          </div>
        </div>

        <!-- Courses Grid -->
        <div
          class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8 stagger-entrance"
          id="coursesGrid"
        >
          <!-- Course 1: Microeconomics -->
          <div
            class="course-card hover-lift minimal-artwork-subtle group cursor-pointer"
            data-status="active"
            data-category="economics"
            onclick="openCourse('microeconomics')"
            style="animation-delay: 0.1s"
          >
            <!-- Hero Course Icon -->
            <div class="flex justify-center mb-6">
              <div
                class="w-20 h-20 bg-[var(--accent-primary)] rounded-2xl flex items-center justify-center text-3xl shadow-lg group-hover:scale-105 transition-transform duration-300"
              >
                📊
              </div>
            </div>

            <!-- Course Info -->
            <div class="text-center mb-6">
              <h3
                class="text-xl font-bold text-[var(--text-primary)] mb-2 group-hover:text-[var(--accent-primary)] transition-colors"
              >
                Microeconomics Fundamentals
              </h3>
              <p class="text-sm text-[var(--text-muted)] mb-4">
                Prof. Dr. Michael Johnson
              </p>

              <!-- Progress Section -->
              <div class="mb-4">
                <div class="flex justify-between items-center text-sm mb-2">
                  <span class="text-[var(--text-secondary)]"
                    >Lecture <span class="geist-mono">9</span> of
                    <span class="geist-mono">24</span></span
                  >
                  <span
                    class="geist-mono font-bold text-[var(--accent-primary)]"
                    >67%</span
                  >
                </div>
                <div class="w-full bg-[var(--bg-secondary)] rounded-full h-2">
                  <div
                    class="bg-[var(--accent-primary)] h-2 rounded-full transition-all duration-700"
                    style="width: 67%"
                  ></div>
                </div>
              </div>
            </div>

            <!-- Single Action -->
            <div class="text-center">
              <button class="btn-primary w-full py-3 text-sm font-semibold">
                Continue Learning
              </button>
            </div>

            <!-- Status Badge -->
            <div
              class="absolute top-4 right-4 bg-[var(--success)] text-white px-3 py-1 rounded-full text-xs font-semibold opacity-0 group-hover:opacity-100 transition-opacity"
            >
              Active
            </div>
          </div>

          <!-- Course 2: Physics -->
          <div
            class="course-card hover-lift minimal-artwork-subtle group cursor-pointer"
            data-status="active"
            data-category="physics"
            onclick="openCourse('physics')"
            style="animation-delay: 0.2s"
          >
            <!-- Hero Course Icon -->
            <div class="flex justify-center mb-6">
              <div
                class="w-20 h-20 bg-gradient-to-br from-orange-400 to-red-500 rounded-2xl flex items-center justify-center text-3xl shadow-lg group-hover:scale-105 transition-transform duration-300"
              >
                ⚛️
              </div>
            </div>

            <!-- Course Info -->
            <div class="text-center mb-6">
              <h3
                class="text-xl font-bold text-[var(--text-primary)] mb-2 group-hover:text-[var(--accent-primary)] transition-colors"
              >
                Classical Mechanics
              </h3>
              <p class="text-sm text-[var(--text-muted)] mb-4">
                Prof. Dr. Sarah Chen
              </p>

              <!-- Progress Section -->
              <div class="mb-4">
                <div class="flex justify-between items-center text-sm mb-2">
                  <span class="text-[var(--text-secondary)]"
                    >Lecture <span class="geist-mono">12</span> of
                    <span class="geist-mono">32</span></span
                  >
                  <span
                    class="geist-mono font-bold text-[var(--accent-primary)]"
                    >38%</span
                  >
                </div>
                <div class="w-full bg-[var(--bg-secondary)] rounded-full h-2">
                  <div
                    class="bg-[var(--accent-primary)] h-2 rounded-full transition-all duration-700"
                    style="width: 38%"
                  ></div>
                </div>
              </div>
            </div>

            <!-- Single Action -->
            <div class="text-center">
              <button class="btn-primary w-full py-3 text-sm font-semibold">
                Continue Learning
              </button>
            </div>

            <!-- Status Badge -->
            <div
              class="absolute top-4 right-4 bg-[var(--success)] text-white px-3 py-1 rounded-full text-xs font-semibold opacity-0 group-hover:opacity-100 transition-opacity"
            >
              Active
            </div>
          </div>

          <!-- Course 3: Completed Course -->
          <div
            class="course-card hover-lift minimal-artwork-subtle group cursor-pointer"
            data-status="completed"
            data-category="mathematics"
            onclick="openCourse('calculus')"
            style="animation-delay: 0.3s"
          >
            <!-- Hero Course Icon -->
            <div class="flex justify-center mb-6">
              <div
                class="w-20 h-20 bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl flex items-center justify-center text-3xl shadow-lg group-hover:scale-105 transition-transform duration-300"
              >
                ∫
              </div>
            </div>

            <!-- Course Info -->
            <div class="text-center mb-6">
              <h3
                class="text-xl font-bold text-[var(--text-primary)] mb-2 group-hover:text-[var(--accent-primary)] transition-colors"
              >
                Calculus I: Limits & Derivatives
              </h3>
              <p class="text-sm text-[var(--text-muted)] mb-4">
                Prof. Dr. Emily Watson
              </p>

              <!-- Progress Section -->
              <div class="mb-4">
                <div class="flex justify-between items-center text-sm mb-2">
                  <span class="text-[var(--text-secondary)]"
                    >All <span class="geist-mono">30</span> lectures
                    completed</span
                  >
                  <span class="geist-mono font-bold text-[var(--success)]"
                    >100%</span
                  >
                </div>
                <div class="w-full bg-[var(--bg-secondary)] rounded-full h-2">
                  <div
                    class="bg-[var(--success)] h-2 rounded-full transition-all duration-700"
                    style="width: 100%"
                  ></div>
                </div>
              </div>
            </div>

            <!-- Single Action -->
            <div class="text-center">
              <button
                class="w-full py-3 text-sm font-semibold bg-[var(--bg-secondary)] text-[var(--text-primary)] rounded-xl hover:bg-[var(--accent-tertiary)]/50 transition-colors"
              >
                View Certificate
              </button>
            </div>

            <!-- Status Badge -->
            <div
              class="absolute top-4 right-4 bg-[var(--success)] text-white px-3 py-1 rounded-full text-xs font-semibold opacity-0 group-hover:opacity-100 transition-opacity"
            >
              Completed
            </div>
          </div>

          <!-- Course 4: Computer Science -->
          <div
            class="course-card hover-lift minimal-artwork-subtle group cursor-pointer"
            data-status="active"
            data-category="computer-science"
            onclick="openCourse('algorithms')"
            style="animation-delay: 0.4s"
          >
            <!-- Hero Course Icon -->
            <div class="flex justify-center mb-6">
              <div
                class="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center text-3xl shadow-lg group-hover:scale-105 transition-transform duration-300"
              >
                💻
              </div>
            </div>

            <!-- Course Info -->
            <div class="text-center mb-6">
              <h3
                class="text-xl font-bold text-[var(--text-primary)] mb-2 group-hover:text-[var(--accent-primary)] transition-colors"
              >
                Data Structures & Algorithms
              </h3>
              <p class="text-sm text-[var(--text-muted)] mb-4">
                Prof. Dr. Alex Rodriguez
              </p>

              <!-- Progress Section -->
              <div class="mb-4">
                <div class="flex justify-between items-center text-sm mb-2">
                  <span class="text-[var(--text-secondary)]"
                    >Lecture <span class="geist-mono">18</span> of
                    <span class="geist-mono">28</span></span
                  >
                  <span
                    class="geist-mono font-bold text-[var(--accent-primary)]"
                    >64%</span
                  >
                </div>
                <div class="w-full bg-[var(--bg-secondary)] rounded-full h-2">
                  <div
                    class="bg-[var(--accent-primary)] h-2 rounded-full transition-all duration-700"
                    style="width: 64%"
                  ></div>
                </div>
              </div>
            </div>

            <!-- Single Action -->
            <div class="text-center">
              <button class="btn-primary w-full py-3 text-sm font-semibold">
                Continue Learning
              </button>
            </div>

            <!-- Status Badge -->
            <div
              class="absolute top-4 right-4 bg-[var(--success)] text-white px-3 py-1 rounded-full text-xs font-semibold opacity-0 group-hover:opacity-100 transition-opacity"
            >
              Active
            </div>
          </div>

          <!-- Course 5: Chemistry (Paused) -->
          <div
            class="course-card hover-lift minimal-artwork-subtle group cursor-pointer"
            data-status="paused"
            data-category="chemistry"
            onclick="openCourse('chemistry')"
            style="animation-delay: 0.5s"
          >
            <!-- Hero Course Icon -->
            <div class="flex justify-center mb-6">
              <div
                class="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl flex items-center justify-center text-3xl shadow-lg group-hover:scale-105 transition-transform duration-300 opacity-60"
              >
                🧪
              </div>
            </div>

            <!-- Course Info -->
            <div class="text-center mb-6">
              <h3
                class="text-xl font-bold text-[var(--text-primary)] mb-2 group-hover:text-[var(--accent-primary)] transition-colors"
              >
                Organic Chemistry Basics
              </h3>
              <p class="text-sm text-[var(--text-muted)] mb-4">
                Prof. Dr. James Miller
              </p>

              <!-- Progress Section -->
              <div class="mb-4">
                <div class="flex justify-between items-center text-sm mb-2">
                  <span class="text-[var(--text-secondary)]"
                    >Lecture <span class="geist-mono">8</span> of
                    <span class="geist-mono">26</span></span
                  >
                  <span class="geist-mono font-bold text-[var(--warning)]"
                    >31%</span
                  >
                </div>
                <div class="w-full bg-[var(--bg-secondary)] rounded-full h-2">
                  <div
                    class="bg-[var(--warning)] h-2 rounded-full transition-all duration-700"
                    style="width: 31%"
                  ></div>
                </div>
              </div>
            </div>

            <!-- Single Action -->
            <div class="text-center">
              <button
                class="w-full py-3 text-sm font-semibold bg-[var(--warning)] text-white rounded-xl hover:bg-[var(--warning)]/80 transition-colors"
              >
                Resume Course
              </button>
            </div>

            <!-- Status Badge -->
            <div
              class="absolute top-4 right-4 bg-[var(--warning)] text-white px-3 py-1 rounded-full text-xs font-semibold opacity-0 group-hover:opacity-100 transition-opacity"
            >
              Paused
            </div>
          </div>
        </div>
      </main>
    </div>

    <script>
      lucide.createIcons();

      // Enhanced staggered animations from dashboard
      const staggeredItems = document.querySelectorAll(".stagger-entrance");
      staggeredItems.forEach((item) => {
        const children = Array.from(item.children);
        children.forEach((child, index) => {
          child.style.animationDelay = `${index * 0.075}s`;
        });
      });

      // Filter functionality with sidebar integration
      const filterButtons = document.querySelectorAll(".filter-btn");
      const courseCards = document.querySelectorAll(".course-card");

      filterButtons.forEach((button) => {
        button.addEventListener("click", function () {
          const filter = this.getAttribute("data-filter");

          // Update active filter button
          filterButtons.forEach((btn) => btn.classList.remove("active"));
          this.classList.add("active");

          // Filter courses
          courseCards.forEach((card) => {
            const status = card.getAttribute("data-status");
            if (filter === "all" || status === filter) {
              card.style.display = "block";
            } else {
              card.style.display = "none";
            }
          });
        });
      });

      // Search functionality
      document
        .getElementById("searchInput")
        .addEventListener("input", function (e) {
          const searchTerm = e.target.value.toLowerCase();

          courseCards.forEach((card) => {
            const title = card.querySelector("h3").textContent.toLowerCase();
            const instructor = card
              .querySelector("p")
              .textContent.toLowerCase();
            const description = card
              .querySelector(".line-clamp-2")
              .textContent.toLowerCase();

            const matches =
              title.includes(searchTerm) ||
              instructor.includes(searchTerm) ||
              description.includes(searchTerm);

            card.style.display = matches ? "block" : "none";
          });
        });

      // Sort functionality
      function sortCourses(sortBy) {
        const coursesGrid = document.getElementById("coursesGrid");
        const cards = Array.from(courseCards);

        cards.sort((a, b) => {
          switch (sortBy) {
            case "alphabetical":
              const titleA = a.querySelector("h3").textContent;
              const titleB = b.querySelector("h3").textContent;
              return titleA.localeCompare(titleB);

            case "progress":
              const progressA = parseInt(
                a.querySelector(".geist-mono").textContent
              );
              const progressB = parseInt(
                b.querySelector(".geist-mono").textContent
              );
              return progressB - progressA;

            case "completion":
              const statusA = a.getAttribute("data-status");
              const statusB = b.getAttribute("data-status");
              if (statusA === "completed" && statusB !== "completed") return -1;
              if (statusB === "completed" && statusA !== "completed") return 1;
              return 0;

            case "recent":
            default:
              return 0;
          }
        });

        cards.forEach((card) => coursesGrid.appendChild(card));
      }

      // Enhanced click handlers with feedback
      const interactiveElements = document.querySelectorAll(
        '[tabindex="0"], button, a'
      );
      interactiveElements.forEach((element) => {
        element.addEventListener("click", function (e) {
          this.style.transform = "scale(0.98)";
          setTimeout(() => {
            this.style.transform = "";
          }, 150);
        });

        element.addEventListener("keydown", function (e) {
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            this.click();
          }
        });
      });

      // Navigation functions
      function openCourse(courseId) {
        window.location.href = "course.html?courseId=" + courseId;
      }

      // Accessibility improvements
      document.addEventListener("DOMContentLoaded", function () {
        const quickActions = document.querySelectorAll(
          ".sidebar-card-item button"
        );
        quickActions.forEach((button) => {
          button.setAttribute("aria-label", button.textContent.trim());
        });
      });

      // Progress bar animations on scroll
      const observerOptions = {
        threshold: 0.5,
        rootMargin: "0px 0px -100px 0px",
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const progressBar = entry.target.querySelector(
              ".bg-\\[var\\(--accent-primary\\)\\]"
            );
            if (progressBar) {
              const width = progressBar.style.width;
              progressBar.style.width = "0%";
              setTimeout(() => {
                progressBar.style.width = width;
              }, 100);
            }
          }
        });
      }, observerOptions);

      courseCards.forEach((card) => observer.observe(card));
    </script>
  </body>
</html>
