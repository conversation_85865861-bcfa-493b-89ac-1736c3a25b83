<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>StudyMind - AI Flashcards</title>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Geist:wght@100..900&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Geist+Mono:wght@100..900&family=Geist:wght@100..900&display=swap"
      rel="stylesheet"
    />
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>
    <style type="text/tailwindcss">
      :root {
        --bg-primary: #fdfbf6;
        --bg-secondary: #f0e5d8;
        --text-primary: #2d3748;
        --text-secondary: #4a5568;
        --text-muted: #718096;
        --text-subtle: #a0aec0;
        --accent-primary: #a9c2b9;
        --accent-secondary: #d3b8ae;
        --accent-tertiary: #e6cba5;
        --highlight: #8b9d8f;
        --success: #68d391;
        --warning: #f6ad55;
        --info: #63b3ed;
      }

    </style>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Geist", sans-serif;
        background-color: var(--bg-primary);
        color: var(--text-primary);
        min-height: 100vh;
        overflow: hidden;
        position: relative;
      }
      
      .geist-mono {
        font-family: "Geist Mono", monospace;
      }

      .flashcards-container {
        display: flex;
        height: 100vh;
      }

      /* Main Flashcard Area */
      .flashcard-main {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: var(--bg-primary);
      }

      .flashcard-header {
        background: white;
        padding: 20px 32px;
        border-bottom: 1px solid var(--accent-tertiary);
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-shadow: 0 2px 8px rgba(169, 194, 185, 0.1);
      }

      .session-info {
        display: flex;
        align-items: center;
        gap: 16px;
      }

      .session-avatar {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: var(--accent-primary);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
      }

      .session-details h1 {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 4px;
      }

      .session-meta {
        color: var(--text-muted);
        font-size: 14px;
        display: flex;
        gap: 16px;
      }

      .session-controls {
        display: flex;
        gap: 12px;
      }

      .control-btn {
        background: var(--bg-secondary);
        border: 1px solid var(--accent-tertiary);
        color: var(--text-primary);
        padding: 8px 16px;
        border-radius: 8px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s;
      }

      .control-btn:hover {
        background: var(--accent-tertiary);
        transform: translateY(-1px);
      }

      .control-btn.active {
        background: var(--accent-primary);
        color: white;
        border-color: var(--accent-primary);
      }

      /* Flashcard Display Area */
      .flashcard-display {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 40px 40px 120px 40px;
        position: relative;
      }

      .flashcard-stack {
        position: relative;
        width: 100%;
        max-width: 600px;
        height: 400px;
      }

      .flashcard {
        position: absolute;
        width: 100%;
        height: 100%;
        background: white;
        border: 2px solid var(--accent-tertiary);
        border-radius: 20px;
        cursor: pointer;
        transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        transform-style: preserve-3d;
        box-shadow: 0 8px 32px rgba(169, 194, 185, 0.15);
      }

      .flashcard:hover {
        border-color: var(--accent-primary);
        box-shadow: 0 12px 48px rgba(169, 194, 185, 0.25);
        transform: translateY(-2px);
      }

      .flashcard.flipped {
        transform: rotateY(180deg);
      }

      .flashcard-side {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 18px;
        padding: 32px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        backface-visibility: hidden;
        background: white;
      }

      .flashcard-back {
        transform: rotateY(180deg);
        background: linear-gradient(
          135deg,
          rgba(169, 194, 185, 0.05) 0%,
          rgba(230, 203, 165, 0.05) 100%
        );
      }

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 24px;
      }

      .card-type {
        background: var(--accent-primary);
        color: white;
        padding: 6px 12px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 600;
      }

      .card-difficulty {
        background: var(--bg-secondary);
        color: var(--warning);
        padding: 6px 12px;
        border-radius: 16px;
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 4px;
        border: 1px solid var(--accent-tertiary);
      }

      .card-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: center;
      }

      .card-question {
        font-size: 24px;
        font-weight: 600;
        line-height: 1.4;
        margin-bottom: 16px;
      }

      .card-context {
        color: var(--text-muted);
        font-size: 14px;
        margin-bottom: 20px;
      }

      .card-answer {
        font-size: 20px;
        line-height: 1.5;
        margin-bottom: 16px;
      }

      .card-explanation {
        background: rgba(169, 194, 185, 0.1);
        border: 1px solid rgba(169, 194, 185, 0.2);
        border-radius: 12px;
        padding: 16px;
        font-size: 14px;
        color: var(--text-secondary);
        line-height: 1.4;
        text-align: left;
      }

      .card-footer {
        margin-top: 24px;
        text-align: center;
      }

      .flip-hint {
        color: var(--accent-primary);
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
      }

      /* Stack Effect for Background Cards */
      .flashcard:nth-child(2) {
        transform: translateY(8px) translateX(-4px) scale(0.95);
        opacity: 0.7;
        z-index: -1;
      }

      .flashcard:nth-child(3) {
        transform: translateY(16px) translateX(-8px) scale(0.9);
        opacity: 0.4;
        z-index: -2;
      }

      /* Compact Progress Bar */
      .flashcard-footer {
        background: white;
        padding: 12px 32px;
        border-top: 1px solid var(--accent-tertiary);
        box-shadow: 0 -1px 4px rgba(169, 194, 185, 0.08);
      }

      .progress-section {
        margin-bottom: 0;
      }

      .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
      }

      .progress-text {
        color: var(--text-muted);
        font-size: 12px;
        font-weight: 500;
      }

      .progress-bar {
        width: 100%;
        height: 6px;
        background: var(--bg-secondary);
        border-radius: 3px;
        overflow: hidden;
      }

      .progress-fill {
        height: 100%;
        background: var(--accent-primary);
        border-radius: 2px;
        width: 35%;
        transition: width 0.5s cubic-bezier(0.65, 0, 0.35, 1);
      }

      /* Floating Action Buttons */
      .floating-actions {
        position: absolute;
        bottom: 80px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 12px;
        z-index: 50;
        background: rgba(255, 255, 255, 0.95);
        padding: 12px;
        border-radius: 2rem;
        border: 1px solid var(--accent-tertiary);
        box-shadow: 0 8px 32px rgba(169, 194, 185, 0.2);
        backdrop-filter: blur(8px);
      }

      .action-btn {
        background: var(--bg-secondary);
        border: 1px solid var(--accent-tertiary);
        color: var(--text-primary);
        padding: 10px 16px;
        border-radius: 1rem;
        cursor: pointer;
        font-size: 12px;
        font-weight: 500;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        gap: 6px;
        white-space: nowrap;
      }

      .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(169, 194, 185, 0.2);
      }

      .action-btn.easy {
        background: var(--success);
        color: white;
        border-color: var(--success);
      }

      .action-btn.easy:hover {
        background: #48cc5c;
        box-shadow: 0 4px 12px rgba(104, 211, 145, 0.3);
      }

      .action-btn.medium {
        background: var(--warning);
        color: white;
        border-color: var(--warning);
      }

      .action-btn.medium:hover {
        background: #f4a261;
        box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
      }

      .action-btn.hard {
        background: #ef4444;
        color: white;
        border-color: #ef4444;
      }

      .action-btn.hard:hover {
        background: #dc2626;
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
      }

      .action-btn.again {
        background: var(--info);
        color: white;
        border-color: var(--info);
      }

      .action-btn.again:hover {
        background: #5a9ce8;
        box-shadow: 0 4px 12px rgba(99, 179, 237, 0.3);
      }

      /* AI Learning Panel */
      .ai-learning-panel {
        width: 380px;
        background: var(--bg-secondary);
        border-left: 1px solid var(--accent-tertiary);
        display: flex;
        flex-direction: column;
      }

      .ai-panel-header {
        background: var(--accent-primary);
        padding: 20px;
        color: white;
      }

      .ai-status {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;
      }

      .ai-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
      }

      .ai-panel-content {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
      }

      .learning-analytics {
        background: white;
        border: 1px solid var(--accent-tertiary);
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 16px;
        box-shadow: 0 2px 8px rgba(169, 194, 185, 0.1);
      }

      .analytics-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
      }

      .analytics-icon {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: var(--accent-primary);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
      }

      .analytics-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
      }

      .analytic-item {
        text-align: center;
      }

      .analytic-value {
        font-size: 18px;
        font-weight: 600;
        color: var(--accent-primary);
      }

      .analytic-label {
        font-size: 12px;
        color: var(--text-muted);
      }

      .spaced-repetition {
        background: rgba(169, 194, 185, 0.1);
        border: 1px solid rgba(169, 194, 185, 0.2);
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 16px;
        border-left: 3px solid var(--accent-primary);
      }

      .sr-header {
        color: var(--accent-primary);
        font-size: 12px;
        font-weight: 600;
        margin-bottom: 8px;
      }

      .sr-text {
        font-size: 13px;
        color: var(--text-secondary);
        line-height: 1.4;
      }

      .difficulty-insights {
        background: white;
        border: 1px solid var(--accent-tertiary);
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 16px;
        box-shadow: 0 2px 8px rgba(169, 194, 185, 0.1);
      }

      .insight-item {
        margin-bottom: 12px;
        font-size: 13px;
      }

      .insight-topic {
        color: var(--accent-primary);
        font-weight: 600;
      }

      .insight-desc {
        color: var(--text-muted);
        margin-top: 4px;
      }

      .ai-input {
        padding: 20px;
        border-top: 1px solid var(--accent-tertiary);
        background: white;
      }

      .input-container {
        position: relative;
      }


      /* Keyboard Navigation Hints */
      .keyboard-hints {
        position: absolute;
        bottom: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid var(--accent-tertiary);
        border-radius: 8px;
        padding: 12px;
        font-size: 12px;
        color: var(--text-muted);
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 12px rgba(169, 194, 185, 0.15);
      }

      .hint-row {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;
      }

      .key-hint {
        background: var(--accent-primary);
        color: white;
        padding: 2px 6px;
        border-radius: 4px;
        font-family: "Geist Mono", monospace;
        font-size: 11px;
      }

      /* Brand Animations and Effects */
      .nav-link:hover {
        color: var(--text-secondary) !important;
        transition-duration: 300ms;
      }
      
      .minimal-artwork-subtle {
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23a9c2b9' fill-opacity='0.07'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-repeat: repeat;
      }

      /* Animations */
      @keyframes slideIn {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }

      @keyframes slideOut {
        from {
          transform: translateX(0);
          opacity: 1;
        }
        to {
          transform: translateX(-100%);
          opacity: 0;
        }
      }

      .slide-in {
        animation: slideIn 0.4s ease-out;
      }
      
      .hover-lift {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      
      .hover-lift:hover {
        transform: translateY(-5px) scale(1.02);
        box-shadow: 0 12px 20px -5px rgba(45, 55, 72, 0.15),
          0 6px 8px -3px rgba(45, 55, 72, 0.1);
      }

      .sidebar-card-item:hover {
        background-color: var(--bg-secondary);
        transform: translateX(2px);
      }

      /* Responsive Design */
      @media (max-width: 1024px) {
        .ai-learning-panel {
          width: 320px;
        }
        .floating-actions {
          gap: 8px;
        }
        .action-btn {
          padding: 8px 12px;
          font-size: 11px;
        }
      }

      @media (max-width: 768px) {
        .flashcards-container {
          flex-direction: column;
        }
        .ai-learning-panel {
          width: 100%;
          height: 35vh;
          order: -1;
        }
        .flashcard-display {
          padding: 20px 20px 120px 20px;
        }
        .flashcard-stack {
          max-width: 100%;
          height: 300px;
        }
        .floating-actions {
          gap: 6px;
          padding: 8px;
          bottom: 60px;
        }
        .action-btn {
          padding: 8px 10px;
          font-size: 10px;
          gap: 4px;
        }
        .action-btn span {
          display: none;
        }
        .flashcard-footer {
          padding: 8px 20px;
        }
      }
    </style>
  </head>
  <body>
    <!-- Floating Action Buttons -->
    <div class="floating-actions">
      <button class="action-btn again" onclick="rateCard('again')">
        <i class="w-4 h-4" data-lucide="rotate-ccw"></i>
        <span>Again</span>
      </button>
      <button class="action-btn hard" onclick="rateCard('hard')">
        <i class="w-4 h-4" data-lucide="zap"></i>
        <span>Hard</span>
      </button>
      <button class="action-btn medium" onclick="rateCard('good')">
        <i class="w-4 h-4" data-lucide="thumbs-up"></i>
        <span>Good</span>
      </button>
      <button class="action-btn easy" onclick="rateCard('easy')">
        <i class="w-4 h-4" data-lucide="check-circle"></i>
        <span>Easy</span>
      </button>
    </div>

    <div class="flashcards-container">
      <div class="flashcard-main">
        <header class="flashcard-header">
          <div class="flex items-center space-x-3">
            <i class="w-10 h-10" style="color: var(--accent-primary)" data-lucide="brain"></i>
            <h1 class="text-3xl font-bold" style="color: var(--text-primary)">StudyMind</h1>
          </div>
          <nav class="flex items-center space-x-8 text-md font-medium">
            <a class="nav-link" href="/dashboard.html" style="color: var(--text-muted); font-weight: 500; transition-duration: 300ms;">Dashboard</a>
            <a class="nav-link" href="/courses.html" style="color: var(--text-muted); font-weight: 500; transition-duration: 300ms;">Courses</a>
            <a class="nav-link-active" href="#" style="color: var(--text-primary); font-weight: 600; border-bottom: 2px solid var(--accent-primary); padding-bottom: 0.25rem;">Flashcards</a>
            <a class="nav-link" href="#" style="color: var(--text-muted); font-weight: 500; transition-duration: 300ms;">Progress</a>
          </nav>
          <div class="session-info">
            <div class="session-avatar">
              <i class="w-6 h-6 text-white" data-lucide="brain"></i>
            </div>
            <div class="session-details">
              <h2 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Market Structures Review</h2>
              <div class="session-meta">
                <span class="flex items-center"><i class="w-4 h-4 mr-1" data-lucide="book-open"></i>15 cards</span>
                <span class="flex items-center"><i class="w-4 h-4 mr-1" data-lucide="target"></i>Focused review</span>
                <span class="flex items-center"><i class="w-4 h-4 mr-1" data-lucide="clock"></i>~12 min</span>
              </div>
            </div>
          </div>
        </header>

        <div class="flashcard-display minimal-artwork-subtle">
          <div class="flashcard-stack">
            <!-- Current Card -->
            <div class="flashcard" id="currentCard" onclick="flipCard()">
              <div class="flashcard-side">
                <div class="card-header">
                  <div class="card-type">Definition</div>
                  <div class="card-difficulty flex items-center">
                    <i class="w-4 h-4 mr-1" data-lucide="zap"></i>Hard
                  </div>
                </div>

                <div class="card-content">
                  <div class="card-context">
                    Based on your confusion during Lecture 9
                  </div>
                  <div class="card-question">
                    What are the key characteristics that distinguish a
                    monopolistically competitive market from a perfectly
                    competitive market?
                  </div>
                </div>

                <div class="card-footer">
                  <div class="flip-hint">
                    <span>Click to reveal answer</span>
                    <i class="w-4 h-4" data-lucide="rotate-cw"></i>
                  </div>
                </div>
              </div>

              <div class="flashcard-side flashcard-back">
                <div class="card-header">
                  <div class="card-type">Answer</div>
                  <div class="card-difficulty flex items-center">
                    <i class="w-4 h-4 mr-1" data-lucide="zap"></i>Hard
                  </div>
                </div>

                <div class="card-content">
                  <div class="card-answer">
                    <strong>Key differences:</strong><br /><br />
                    • <strong>Product differentiation:</strong> Products are
                    similar but not identical<br />
                    • <strong>Some price control:</strong> Firms can influence
                    prices slightly<br />
                    • <strong>Brand loyalty:</strong> Customers may prefer
                    specific brands<br />
                    • <strong>Advertising:</strong> Firms compete through
                    marketing
                  </div>

                  <div class="card-explanation">
                    <strong class="flex items-center"><i class="w-4 h-4 mr-1" data-lucide="lightbulb"></i>Memory Aid:</strong> Think of restaurants in a
                    food court - they all serve food (similar) but each has
                    unique offerings (differentiated), allowing them to charge
                    slightly different prices.
                  </div>
                </div>

                <div class="card-footer">
                  <div class="flip-hint">
                    <span>Click to flip back</span>
                    <i class="w-4 h-4" data-lucide="rotate-cw"></i>
                  </div>
                </div>
              </div>
            </div>

            <!-- Background Cards for Stack Effect -->
            <div class="flashcard">
              <div class="flashcard-side">
                <div class="card-content">
                  <div
                    class="card-question"
                    style="font-size: 20px; color: #666"
                  >
                    Next: Price Discrimination Types...
                  </div>
                </div>
              </div>
            </div>

            <div class="flashcard">
              <div class="flashcard-side">
                <div class="card-content">
                  <div
                    class="card-question"
                    style="font-size: 18px; color: #555"
                  >
                    Coming up: Barriers to Entry...
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="keyboard-hints">
            <div class="hint-row">
              <span class="key-hint">Space</span>
              <span>Flip card</span>
            </div>
            <div class="hint-row">
              <span class="key-hint">1</span>
              <span>Again</span>
            </div>
            <div class="hint-row">
              <span class="key-hint">2</span>
              <span>Hard</span>
            </div>
            <div class="hint-row">
              <span class="key-hint">3</span>
              <span>Good</span>
            </div>
            <div class="hint-row">
              <span class="key-hint">4</span>
              <span>Easy</span>
            </div>
          </div>
        </div>

        <div class="flashcard-footer">
          <div class="progress-section">
            <div class="progress-header">
              <div class="progress-text">
                Card 3 of 15 • 2 new, 8 learning, 5 reviewing
              </div>
            </div>
            <div class="progress-bar">
              <div class="progress-fill"></div>
            </div>
          </div>
        </div>
      </div>

      <div class="ai-learning-panel">
        <div class="ai-panel-header">
          <div class="ai-status">
            <div class="ai-avatar">
              <i class="w-5 h-5 text-white" data-lucide="brain-circuit"></i>
            </div>
            <div>
              <div style="font-weight: 600">Adaptive Learning AI</div>
              <div style="font-size: 12px; opacity: 0.8">
                Optimizing your retention
              </div>
            </div>
          </div>
          <div style="font-size: 12px; opacity: 0.8">
            Using spaced repetition + your learning patterns
          </div>
        </div>

        <div class="ai-panel-content">
          <div class="learning-analytics">
            <div class="analytics-header">
              <div class="analytics-icon">
                <i class="w-3 h-3 text-white" data-lucide="bar-chart-3"></i>
              </div>
              <div style="font-weight: 600">Session Analytics</div>
            </div>
            <div class="analytics-grid">
              <div class="analytic-item">
                <div class="analytic-value">87%</div>
                <div class="analytic-label">Retention Rate</div>
              </div>
              <div class="analytic-item">
                <div class="analytic-value">1.8m</div>
                <div class="analytic-label">Avg. Time</div>
              </div>
              <div class="analytic-item">
                <div class="analytic-value">12</div>
                <div class="analytic-label">Cards Due</div>
              </div>
              <div class="analytic-item">
                <div class="analytic-value">5</div>
                <div class="analytic-label">Day Streak</div>
              </div>
            </div>
          </div>

          <div class="spaced-repetition">
            <div class="sr-header flex items-center">
              <i class="w-4 h-4 mr-1" data-lucide="brain"></i>Memory Science
            </div>
            <div class="sr-text">
              This card uses optimal timing based on your previous performance.
              You last saw it 2 days ago and rated it "Hard" - perfect time for
              review!
            </div>
          </div>

          <div class="difficulty-insights">
            <div style="font-weight: 600; margin-bottom: 12px">
              <i class="w-4 h-4 mr-2" data-lucide="trending-up"></i>Learning Insights
            </div>

            <div class="insight-item">
              <div class="insight-topic">Market Structures</div>
              <div class="insight-desc">
                You're struggling with this topic (65% accuracy). More visual
                examples added to help.
              </div>
            </div>

            <div class="insight-item">
              <div class="insight-topic">Perfect Competition</div>
              <div class="insight-desc">
                Strong performance (92% accuracy). Cards will appear less
                frequently.
              </div>
            </div>

            <div class="insight-item">
              <div class="insight-topic">Study Pattern</div>
              <div class="insight-desc">
                Best performance between 2-4 PM. Consider scheduling reviews
                then.
              </div>
            </div>
          </div>

          <div class="learning-analytics">
            <div style="font-weight: 600; margin-bottom: 12px">
              <i class="w-4 h-4 mr-2" data-lucide="target"></i>AI Adaptations
            </div>
            <div style="font-size: 13px; color: #b0b3b8; line-height: 1.5">
              • Added more context cards for difficult concepts<br />
              • Increased review frequency for market structures<br />
              • Added visual memory aids based on your preference<br />
              • Clustered related concepts for better retention
            </div>
          </div>
        </div>

        <div class="ai-input">
          <div class="input-container">
            <textarea
              class="w-full bg-[var(--bg-primary)] border border-[var(--bg-secondary)] rounded-lg p-3 pr-12 text-sm resize-none focus:outline-none focus:bg-white focus:border-[var(--accent-primary)]/50 focus:ring-4 focus:ring-[var(--accent-primary)]/10 transition-all duration-300"
              rows="3"
              placeholder="Ask about this concept, request examples, or get study tips..."
            ></textarea>
            <button
              class="absolute bottom-3 right-3 w-8 h-8 bg-[var(--accent-primary)] rounded-full flex items-center justify-center text-white hover:bg-[var(--highlight)] transition-colors"
            >
              <i class="w-4 h-4" data-lucide="send"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Initialize Lucide icons
      lucide.createIcons();
      
      let isFlipped = false;
      let currentCardIndex = 0;
      const totalCards = 15;

      function flipCard() {
        const card = document.getElementById("currentCard");
        card.classList.toggle("flipped");
        isFlipped = !isFlipped;
      }

      function rateCard(difficulty) {
        if (!isFlipped) {
          alert("Please flip the card first to see the answer!");
          return;
        }

        // Animate card out
        const card = document.getElementById("currentCard");
        card.classList.add("slide-out");

        // Update progress
        updateProgress();

        // Load next card after animation
        setTimeout(() => {
          loadNextCard(difficulty);
        }, 400);
      }

      function loadNextCard(previousRating) {
        currentCardIndex++;

        if (currentCardIndex >= totalCards) {
          showSessionComplete();
          return;
        }

        // Reset card state
        const card = document.getElementById("currentCard");
        card.classList.remove("slide-out", "flipped");
        card.classList.add("slide-in");
        isFlipped = false;

        // Simulate loading new card content
        // In real app, this would fetch next card based on spaced repetition algorithm

        // Remove animation class after animation completes
        setTimeout(() => {
          card.classList.remove("slide-in");
        }, 400);
      }

      function updateProgress() {
        const progressFill = document.querySelector(".progress-fill");
        const progressText = document.querySelector(".progress-text");

        const progress = ((currentCardIndex + 1) / totalCards) * 100;
        progressFill.style.width = progress + "%";

        const remaining = totalCards - currentCardIndex - 1;
        progressText.textContent = `Card ${
          currentCardIndex + 1
        } of ${totalCards} • ${remaining} cards remaining`;
      }

      function showSessionComplete() {
        alert(
          "🎉 Session Complete! Great work on improving your understanding of Market Structures."
        );
      }

      // Keyboard shortcuts
      document.addEventListener("keydown", function (e) {
        switch (e.key) {
          case " ":
            e.preventDefault();
            flipCard();
            break;
          case "1":
            rateCard("again");
            break;
          case "2":
            rateCard("hard");
            break;
          case "3":
            rateCard("good");
            break;
          case "4":
            rateCard("easy");
            break;
        }
      });

      // Simulate real-time analytics updates
      setInterval(() => {
        // Update retention rate occasionally
        if (Math.random() > 0.97) {
          const retentionElement = document.querySelector(".analytic-value");
          const currentValue = parseInt(retentionElement.textContent);
          if (currentValue < 95) {
            retentionElement.textContent = currentValue + 1 + "%";
          }
        }
      }, 5000);
    </script>
  </body>
</html>
