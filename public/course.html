<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>StudyMind - Microeconomics Fundamentals</title>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Geist:wght@100..900&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Geist+Mono:wght@100..900&family=Geist:wght@100..900&display=swap"
      rel="stylesheet"
    />

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>
    <style type="text/tailwindcss">
      :root {
        --bg-primary: #fdfbf6;
        --bg-secondary: #f0e5d8;
        --text-primary: #2d3748;
        --text-secondary: #4a5568;
        --text-muted: #718096;
        --text-subtle: #a0aec0;
        --text-on-tinted: #2d3748;
        --text-on-tinted-muted: #4a5568;
        --text-on-tinted-subtle: #718096;
        --accent-primary: #a9c2b9;
        --accent-secondary: #d3b8ae;
        --accent-tertiary: #e6cba5;
        --highlight: #8b9d8f;
        --success: #68d391;
        --warning: #f6ad55;
        --info: #63b3ed;
      }

      body {
        font-family: "Geist", sans-serif;
        background-color: var(--bg-primary);
        color: var(--text-primary);
      }
      .geist-mono {
        font-family: "Geist Mono", monospace;
      }
      .animated-gradient {
        background: linear-gradient(
          -45deg,
          var(--bg-secondary),
          var(--accent-primary),
          var(--highlight)
        );
        background-size: 400% 400%;
        animation: gradientBG 20s ease infinite;
      }
      @keyframes gradientBG {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }
      .stagger-entrance > * {
        opacity: 0;
        transform: translateY(20px);
        animation: fadeInUp 0.6s forwards;
      }
      @keyframes fadeInUp {
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      .hover-lift {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .hover-lift:hover {
        transform: translateY(-5px) scale(1.02);
        box-shadow: 0 12px 20px -5px rgba(45, 55, 72, 0.15),
          0 6px 8px -3px rgba(45, 55, 72, 0.1);
      }
      .sidebar-card-item:hover {
        background-color: var(--bg-secondary);
        transform: translateX(2px);
      }
      .nav-link-active {
        color: var(--text-primary);
        font-weight: 600;
        border-bottom-width: 2px;
        border-color: var(--accent-primary);
        padding-bottom: 0.25rem;
      }
      .nav-link {
        color: var(--text-muted);
        font-weight: 500;
      }
      .nav-link:hover {
        color: var(--text-secondary);
        transition-duration: 300ms;
      }
      .icon-primary {
        color: var(--accent-primary);
      }
      .icon-secondary {
        color: var(--accent-secondary);
      }
      .text-highlight {
        color: var(--highlight);
        font-weight: 600;
      }
      .minimal-artwork-bg {
        background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23e6cba5' fill-opacity='0.1'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-repeat: repeat;
        background-size: 200px 200px;
      }
      .minimal-artwork-subtle {
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23a9c2b9' fill-opacity='0.07'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-repeat: repeat;
      }

      /* Enhanced button styles */
      .btn-primary {
        background: var(--accent-primary);
        color: white;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        border-radius: 0.75rem;
        transition: all 0.2s ease;
        border: none;
        cursor: pointer;
      }

      .btn-primary:hover {
        background: var(--highlight);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(169, 194, 185, 0.3);
      }

      /* Better focus states */
      .focus-ring:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(169, 194, 185, 0.3);
      }

      /* Responsive Design */
      @media (max-width: 1200px) {
        .main-container {
          grid-template-columns: 1fr;
          gap: 1.5rem;
        }

        .hero-content {
          grid-template-columns: 1fr;
          gap: 1.5rem;
        }

        .study-tools {
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          grid-template-rows: auto;
          flex-direction: row;
        }
      }

      @media (max-width: 768px) {
        .header-content {
          flex-direction: column;
          gap: 1rem;
        }

        .nav-items {
          order: -1;
        }

        .breadcrumb-content,
        .hero-content,
        .main-container {
          padding-left: 1rem;
          padding-right: 1rem;
        }

        .course-title {
          font-size: 1.75rem;
        }

        .course-meta {
          flex-direction: column;
          gap: 0.5rem;
        }

        .hero-actions {
          flex-direction: column;
        }

        .stats-grid {
          grid-template-columns: 1fr;
          gap: 0.75rem;
        }

        .analytics-grid {
          grid-template-columns: 1fr;
        }

        .quick-actions {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body class="text-[var(--text-primary)]">
    <div class="flex h-screen">
      <!-- Sidebar -->
      <aside
        class="w-96 bg-[var(--bg-secondary)] p-7 border-r border-[var(--accent-tertiary)]/50 overflow-y-auto space-y-7"
      >
        <!-- AI Learning Assistant -->
        <div
          class="bg-[var(--accent-primary)] text-white p-7 rounded-2xl shadow-xl stagger-entrance relative overflow-hidden"
        >
          <div
            class="absolute inset-0 minimal-artwork-bg opacity-20"
            style="background-size: 100px 100px"
          ></div>
          <div class="relative z-10 flex items-center space-x-4 mb-4">
            <i class="w-10 h-10 text-white" data-lucide="brain-circuit"></i>
            <div>
              <h3 class="text-xl font-semibold text-white">
                Course AI Assistant
              </h3>
              <p class="text-sm text-white/80">Your personal guide</p>
            </div>
          </div>
          <div
            class="relative z-10 bg-white/20 hover:bg-white/30 p-4 rounded-lg mb-3 cursor-pointer transition-all duration-300 ease-in-out group sidebar-card-item focus-ring"
            style="animation-delay: 0.1s"
            tabindex="0"
          >
            <div class="flex items-start space-x-3">
              <i
                class="w-5 h-5 text-white/90 mt-1 flex-shrink-0 group-hover:text-white transition-colors"
                data-lucide="sparkles"
              ></i>
              <div>
                <h4 class="text-md font-semibold text-white">
                  Smart Recommendation
                </h4>
                <p class="text-xs text-white/80 mt-1 leading-relaxed">
                  You're ready for Market Structures! Strong performance on
                  Supply & Demand shows mastery.
                </p>
              </div>
            </div>
          </div>
          <div
            class="relative z-10 bg-white/20 hover:bg-white/30 p-4 rounded-lg mb-3 cursor-pointer transition-all duration-300 ease-in-out group sidebar-card-item focus-ring"
            style="animation-delay: 0.2s"
            tabindex="0"
          >
            <div class="flex items-start space-x-3">
              <i
                class="w-5 h-5 text-white/90 mt-1 flex-shrink-0 group-hover:text-white transition-colors"
                data-lucide="bar-chart-3"
              ></i>
              <div>
                <h4 class="text-md font-semibold text-white">
                  Learning Pattern
                </h4>
                <p class="text-xs text-white/80 mt-1 leading-relaxed">
                  You learn best with video content afternoons. Perfect timing
                  for next lecture!
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Study Tools -->
        <div
          class="bg-white p-7 rounded-2xl shadow-xl stagger-entrance minimal-artwork-subtle border border-gray-100"
        >
          <h3
            class="text-xl font-semibold text-[var(--text-primary)] mb-5 flex items-center"
          >
            <i class="w-6 h-6 mr-3 icon-secondary" data-lucide="zap"></i>
            Study Tools
          </h3>
          <div class="grid grid-cols-2 gap-4 text-center">
            <div
              class="bg-[var(--bg-primary)] p-4 rounded-lg hover:bg-[var(--accent-tertiary)]/50 transition-colors cursor-pointer sidebar-card-item focus-ring"
              tabindex="0"
              onclick="generateQuiz()"
            >
              <i
                class="w-8 h-8 icon-primary mx-auto mb-1.5"
                data-lucide="clipboard-check"
              ></i>
              <p class="text-sm font-semibold text-[var(--text-primary)]">
                AI Quiz
              </p>
              <p class="text-xs text-[var(--text-muted)]">Test knowledge</p>
            </div>
            <div
              class="bg-[var(--bg-primary)] p-4 rounded-lg hover:bg-[var(--accent-tertiary)]/50 transition-colors cursor-pointer sidebar-card-item focus-ring"
              tabindex="0"
              onclick="createFlashcards()"
            >
              <i
                class="w-8 h-8 icon-primary mx-auto mb-1.5"
                data-lucide="copy"
              ></i>
              <p class="text-sm font-semibold text-[var(--text-primary)]">
                Flashcards
              </p>
              <p class="text-xs text-[var(--text-muted)]">Quick review</p>
            </div>
            <div
              class="bg-[var(--bg-primary)] p-4 rounded-lg hover:bg-[var(--accent-tertiary)]/50 transition-colors cursor-pointer sidebar-card-item focus-ring"
              tabindex="0"
              onclick="openNotes()"
            >
              <i
                class="w-8 h-8 icon-primary mx-auto mb-1.5"
                data-lucide="edit-3"
              ></i>
              <p class="text-sm font-semibold text-[var(--text-primary)]">
                Smart Notes
              </p>
              <p class="text-xs text-[var(--text-muted)]">AI-enhanced</p>
            </div>
            <div
              class="bg-[var(--bg-primary)] p-4 rounded-lg hover:bg-[var(--accent-tertiary)]/50 transition-colors cursor-pointer sidebar-card-item focus-ring"
              tabindex="0"
            >
              <i
                class="w-8 h-8 icon-primary mx-auto mb-1.5"
                data-lucide="trending-up"
              ></i>
              <p class="text-sm font-semibold text-[var(--text-primary)]">
                Progress
              </p>
              <p class="text-xs text-[var(--text-muted)]">Track learning</p>
            </div>
          </div>
        </div>

        <!-- Progress Analytics -->
        <div
          class="bg-white p-7 rounded-2xl shadow-xl stagger-entrance minimal-artwork-subtle border border-gray-100"
        >
          <h3
            class="text-xl font-semibold text-[var(--text-primary)] mb-5 flex items-center"
          >
            <i class="w-6 h-6 mr-3 icon-primary" data-lucide="bar-chart-4"></i>
            Learning Analytics
          </h3>
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div class="text-center bg-[var(--bg-primary)] p-4 rounded-lg">
              <div
                class="geist-mono text-2xl font-bold text-[var(--accent-primary)]"
              >
                8.7
              </div>
              <div class="text-xs text-[var(--text-muted)] mt-1">
                Study Hours/Week
              </div>
            </div>
            <div class="text-center bg-[var(--bg-primary)] p-4 rounded-lg">
              <div
                class="geist-mono text-2xl font-bold text-[var(--accent-primary)]"
              >
                94%
              </div>
              <div class="text-xs text-[var(--text-muted)] mt-1">
                Retention Rate
              </div>
            </div>
          </div>
          <div class="bg-[var(--bg-primary)] p-4 rounded-lg text-center">
            <div class="text-xs text-[var(--text-muted)] mb-2">
              Weekly Performance Trend
            </div>
            <div
              class="h-12 bg-[var(--accent-primary)] rounded flex items-center justify-center text-white text-xs font-semibold"
            >
              📈 Trending upward!
            </div>
          </div>
        </div>
      </aside>

      <!-- Main Content -->
      <main class="flex-1 p-8 overflow-y-auto">
        <!-- Header -->
        <header class="flex justify-between items-center mb-10">
          <div class="flex items-center space-x-3">
            <i class="w-10 h-10 icon-primary" data-lucide="brain"></i>
            <h1 class="text-3xl font-bold text-[var(--text-primary)]">
              StudyMind
            </h1>
          </div>
          <nav class="flex items-center space-x-8 text-md font-medium">
            <a class="nav-link" href="/dashboard.html" onclick="goToDashboard()"
              >Dashboard</a
            >
            <a
              class="nav-link-active"
              href="/courses.html"
              onclick="goToCourses()"
              >My Courses</a
            >
            <a class="nav-link" href="#">Study Tools</a>
            <a class="nav-link" href="#">Progress</a>
          </nav>
          <div class="flex items-center space-x-4">
            <div class="relative">
              <i
                class="w-6 h-6 text-[var(--text-muted)] hover:text-[var(--accent-primary)] cursor-pointer transition-colors focus-ring"
                data-lucide="bell"
                tabindex="0"
              ></i>
              <span
                class="absolute -top-1.5 -right-1.5 w-2.5 h-2.5 bg-red-500 rounded-full border-2 border-[var(--bg-primary)]"
              ></span>
            </div>
            <div class="flex items-center space-x-3">
              <div
                class="w-10 h-10 bg-[var(--accent-secondary)] rounded-full flex items-center justify-center text-white text-lg font-semibold shadow-sm"
              >
                <img
                  src="/images/profile-avatar.png"
                  alt="Profile Avatar"
                  class="w-8 h-8 rounded-full"
                />
              </div>
              <div>
                <p class="text-md font-semibold text-[var(--text-primary)]">
                  Sarah Johnes
                </p>
                <p class="text-xs text-[var(--text-muted)]">Premium Plan</p>
              </div>
            </div>
          </div>
        </header>

        <!-- Course Hero -->
        <section
          class="animated-gradient p-8 rounded-2xl shadow-xl mb-10 stagger-entrance relative overflow-hidden"
        >
          <div class="absolute inset-0 minimal-artwork-bg opacity-30"></div>
          <div class="relative z-10">
            <div class="flex items-start justify-between mb-6">
              <div class="flex-1">
                <h2
                  class="text-3xl font-bold text-white mb-2"
                  style="animation-delay: 0.1s"
                >
                  Microeconomics Fundamentals
                </h2>
                <p
                  class="text-white/80 text-base"
                  style="animation-delay: 0.2s"
                >
                  Prof. Dr. Michael Johnson • 24 lectures
                </p>
              </div>
              <div class="text-right" style="animation-delay: 0.2s">
                <div class="text-white/90 text-sm mb-1">Your Progress</div>
                <div class="geist-mono text-2xl font-bold text-white">67%</div>
              </div>
            </div>

            <!-- Learning Flow Focus -->
            <div
              class="bg-white/15 backdrop-blur-sm rounded-xl p-6 border border-white/20"
              style="animation-delay: 0.3s"
            >
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <div class="text-white/90 text-sm mb-1">
                    Continue Your Journey
                  </div>
                  <h3 class="text-xl font-semibold text-white mb-2">
                    Market Structures and Competition
                  </h3>
                  <div
                    class="flex items-center space-x-4 text-white/70 text-sm"
                  >
                    <span class="flex items-center">
                      <i class="w-4 h-4 mr-1" data-lucide="book-open"></i>
                      Lecture 9 of 24
                    </span>
                    <span class="flex items-center">
                      <i class="w-4 h-4 mr-1" data-lucide="clock"></i>
                      42 min
                    </span>
                    <span
                      class="bg-white/20 px-2 py-1 rounded text-xs font-medium"
                      >PDF</span
                    >
                  </div>
                </div>
                <div class="ml-6">
                  <button
                    class="bg-white text-[var(--accent-primary)] px-6 py-3 rounded-xl font-semibold hover:bg-white/90 transition-all shadow-lg"
                    onclick="continueLearning()"
                  >
                    <i class="w-4 h-4 mr-2" data-lucide="play"></i>
                    Start Lecture
                  </button>
                </div>
              </div>

              <!-- Simplified Progress -->
              <div class="mt-4">
                <div class="w-full bg-white/20 rounded-full h-2">
                  <div
                    class="bg-white h-2 rounded-full transition-all duration-700"
                    style="width: 67%"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </section>
        <!-- Lectures Section -->
        <div
          class="bg-white p-8 rounded-2xl shadow-xl stagger-entrance minimal-artwork-subtle border border-gray-100"
        >
          <div class="mb-6">
            <h2
              class="text-2xl font-semibold text-[var(--text-primary)] flex items-center"
            >
              <i class="w-7 h-7 mr-3 icon-primary" data-lucide="book-open"></i>
              Course Lectures
            </h2>
            <p class="text-[var(--text-muted)] mt-2">
              Track your progress through each lecture and continue where you
              left off.
            </p>
          </div>

          <div class="space-y-4">
            <!-- Week 1 -->
            <div
              class="bg-[var(--success)]/10 border border-[var(--success)]/20 p-5 rounded-xl hover:bg-[var(--success)]/15 transition-all duration-300 cursor-pointer hover-lift group"
              onclick="openLecture('intro', 'video')"
            >
              <div class="flex items-center space-x-4">
                <div
                  class="w-12 h-12 bg-[var(--success)] rounded-full flex items-center justify-center text-white shadow-sm group-hover:scale-105 transition-transform"
                >
                  <i class="w-6 h-6" data-lucide="check"></i>
                </div>
                <div class="flex-1">
                  <h3
                    class="text-lg font-semibold text-[var(--text-primary)] mb-1"
                  >
                    Introduction to Microeconomics
                  </h3>
                  <div
                    class="flex items-center space-x-4 text-sm text-[var(--text-muted)]"
                  >
                    <span
                      class="bg-blue-100 text-blue-700 px-2 py-1 rounded-lg font-medium"
                      >Video</span
                    >
                    <span class="flex items-center"
                      ><i class="w-4 h-4 mr-1" data-lucide="calendar"></i> Week
                      1</span
                    >
                    <span class="flex items-center"
                      ><i class="w-4 h-4 mr-1" data-lucide="clock"></i> 45
                      min</span
                    >
                    <span class="flex items-center text-[var(--success)]"
                      ><i class="w-4 h-4 mr-1" data-lucide="check-circle"></i>
                      Completed</span
                    >
                  </div>
                </div>
                <div
                  class="flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <button
                    class="bg-[var(--bg-secondary)] hover:bg-[var(--accent-tertiary)]/50 text-[var(--text-primary)] px-3 py-2 rounded-lg text-sm font-medium transition-colors focus-ring"
                  >
                    <i class="w-4 h-4 mr-1" data-lucide="file-text"></i> Notes
                  </button>
                  <button
                    class="bg-[var(--bg-secondary)] hover:bg-[var(--accent-tertiary)]/50 text-[var(--text-primary)] px-3 py-2 rounded-lg text-sm font-medium transition-colors focus-ring"
                  >
                    <i class="w-4 h-4 mr-1" data-lucide="refresh-cw"></i> Review
                  </button>
                </div>
              </div>
            </div>

            <div
              class="bg-[var(--success)]/10 border border-[var(--success)]/20 p-5 rounded-xl hover:bg-[var(--success)]/15 transition-all duration-300 cursor-pointer hover-lift group"
              onclick="openLecture('scarcity', 'pdf')"
            >
              <div class="flex items-center space-x-4">
                <div
                  class="w-12 h-12 bg-[var(--success)] rounded-full flex items-center justify-center text-white shadow-sm group-hover:scale-105 transition-transform"
                >
                  <i class="w-6 h-6" data-lucide="check"></i>
                </div>
                <div class="flex-1">
                  <h3
                    class="text-lg font-semibold text-[var(--text-primary)] mb-1"
                  >
                    Scarcity and Resource Allocation
                  </h3>
                  <div
                    class="flex items-center space-x-4 text-sm text-[var(--text-muted)]"
                  >
                    <span
                      class="bg-emerald-100 text-emerald-700 px-2 py-1 rounded-lg font-medium"
                      >PDF</span
                    >
                    <span class="flex items-center"
                      ><i class="w-4 h-4 mr-1" data-lucide="calendar"></i> Week
                      1</span
                    >
                    <span class="flex items-center"
                      ><i class="w-4 h-4 mr-1" data-lucide="clock"></i> 30
                      min</span
                    >
                    <span class="flex items-center text-[var(--success)]"
                      ><i class="w-4 h-4 mr-1" data-lucide="check-circle"></i>
                      Completed</span
                    >
                  </div>
                </div>
                <div
                  class="flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <button
                    class="bg-[var(--bg-secondary)] hover:bg-[var(--accent-tertiary)]/50 text-[var(--text-primary)] px-3 py-2 rounded-lg text-sm font-medium transition-colors focus-ring"
                  >
                    <i class="w-4 h-4 mr-1" data-lucide="file-text"></i> Notes
                  </button>
                  <button
                    class="bg-[var(--bg-secondary)] hover:bg-[var(--accent-tertiary)]/50 text-[var(--text-primary)] px-3 py-2 rounded-lg text-sm font-medium transition-colors focus-ring"
                  >
                    <i class="w-4 h-4 mr-1" data-lucide="refresh-cw"></i> Review
                  </button>
                </div>
              </div>
            </div>

            <!-- Week 2 -->
            <div
              class="bg-[var(--success)]/10 border border-[var(--success)]/20 p-5 rounded-xl hover:bg-[var(--success)]/15 transition-all duration-300 cursor-pointer hover-lift group"
              onclick="openLecture('supply-demand', 'video')"
            >
              <div class="flex items-center space-x-4">
                <div
                  class="w-12 h-12 bg-[var(--success)] rounded-full flex items-center justify-center text-white shadow-sm group-hover:scale-105 transition-transform"
                >
                  <i class="w-6 h-6" data-lucide="check"></i>
                </div>
                <div class="flex-1">
                  <h3
                    class="text-lg font-semibold text-[var(--text-primary)] mb-1"
                  >
                    Supply and Demand Fundamentals
                  </h3>
                  <div
                    class="flex items-center space-x-4 text-sm text-[var(--text-muted)]"
                  >
                    <span
                      class="bg-blue-100 text-blue-700 px-2 py-1 rounded-lg font-medium"
                      >Video</span
                    >
                    <span class="flex items-center"
                      ><i class="w-4 h-4 mr-1" data-lucide="calendar"></i> Week
                      2</span
                    >
                    <span class="flex items-center"
                      ><i class="w-4 h-4 mr-1" data-lucide="clock"></i> 52
                      min</span
                    >
                    <span class="flex items-center text-[var(--success)]"
                      ><i class="w-4 h-4 mr-1" data-lucide="check-circle"></i>
                      Completed</span
                    >
                  </div>
                </div>
                <div
                  class="flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <button
                    class="bg-[var(--bg-secondary)] hover:bg-[var(--accent-tertiary)]/50 text-[var(--text-primary)] px-3 py-2 rounded-lg text-sm font-medium transition-colors focus-ring"
                  >
                    <i class="w-4 h-4 mr-1" data-lucide="file-text"></i> Notes
                  </button>
                  <button
                    class="bg-[var(--bg-secondary)] hover:bg-[var(--accent-tertiary)]/50 text-[var(--text-primary)] px-3 py-2 rounded-lg text-sm font-medium transition-colors focus-ring"
                  >
                    <i class="w-4 h-4 mr-1" data-lucide="refresh-cw"></i> Review
                  </button>
                </div>
              </div>
            </div>

            <div
              class="bg-[var(--success)]/10 border border-[var(--success)]/20 p-5 rounded-xl hover:bg-[var(--success)]/15 transition-all duration-300 cursor-pointer hover-lift group"
              onclick="openLecture('equilibrium', 'audio')"
            >
              <div class="flex items-center space-x-4">
                <div
                  class="w-12 h-12 bg-[var(--success)] rounded-full flex items-center justify-center text-white shadow-sm group-hover:scale-105 transition-transform"
                >
                  <i class="w-6 h-6" data-lucide="check"></i>
                </div>
                <div class="flex-1">
                  <h3
                    class="text-lg font-semibold text-[var(--text-primary)] mb-1"
                  >
                    Market Equilibrium and Price Determination
                  </h3>
                  <div
                    class="flex items-center space-x-4 text-sm text-[var(--text-muted)]"
                  >
                    <span
                      class="bg-purple-100 text-purple-700 px-2 py-1 rounded-lg font-medium"
                      >Audio</span
                    >
                    <span class="flex items-center"
                      ><i class="w-4 h-4 mr-1" data-lucide="calendar"></i> Week
                      2</span
                    >
                    <span class="flex items-center"
                      ><i class="w-4 h-4 mr-1" data-lucide="clock"></i> 38
                      min</span
                    >
                    <span class="flex items-center text-[var(--success)]"
                      ><i class="w-4 h-4 mr-1" data-lucide="check-circle"></i>
                      Completed</span
                    >
                  </div>
                </div>
                <div
                  class="flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <button
                    class="bg-[var(--bg-secondary)] hover:bg-[var(--accent-tertiary)]/50 text-[var(--text-primary)] px-3 py-2 rounded-lg text-sm font-medium transition-colors focus-ring"
                  >
                    <i class="w-4 h-4 mr-1" data-lucide="file-text"></i> Notes
                  </button>
                  <button
                    class="bg-[var(--bg-secondary)] hover:bg-[var(--accent-tertiary)]/50 text-[var(--text-primary)] px-3 py-2 rounded-lg text-sm font-medium transition-colors focus-ring"
                  >
                    <i class="w-4 h-4 mr-1" data-lucide="refresh-cw"></i> Review
                  </button>
                </div>
              </div>
            </div>

            <!-- Week 3 - Current -->
            <div
              class="bg-[var(--accent-primary)]/20 border-2 border-[var(--accent-primary)] p-5 rounded-xl hover:bg-[var(--accent-primary)]/25 transition-all duration-300 cursor-pointer hover-lift group"
              onclick="openLecture('market-structures', 'pdf')"
            >
              <div class="flex items-center space-x-4">
                <div
                  class="w-12 h-12 bg-[var(--accent-primary)] rounded-full flex items-center justify-center text-white shadow-sm group-hover:scale-105 transition-transform geist-mono font-bold"
                >
                  9
                </div>
                <div class="flex-1">
                  <h3
                    class="text-lg font-semibold text-[var(--text-primary)] mb-1"
                  >
                    Market Structures and Competition
                  </h3>
                  <div
                    class="flex items-center space-x-4 text-sm text-[var(--text-muted)]"
                  >
                    <span
                      class="bg-emerald-100 text-emerald-700 px-2 py-1 rounded-lg font-medium"
                      >PDF</span
                    >
                    <span class="flex items-center"
                      ><i class="w-4 h-4 mr-1" data-lucide="calendar"></i> Week
                      3</span
                    >
                    <span class="flex items-center"
                      ><i class="w-4 h-4 mr-1" data-lucide="clock"></i> 42
                      min</span
                    >
                    <span
                      class="flex items-center text-[var(--accent-primary)] font-semibold"
                      ><i class="w-4 h-4 mr-1" data-lucide="play-circle"></i>
                      Next Up</span
                    >
                  </div>
                </div>
                <div class="flex space-x-2">
                  <button
                    class="bg-[var(--accent-primary)] hover:bg-[var(--highlight)] text-white px-4 py-2 rounded-lg text-sm font-semibold transition-colors focus-ring"
                  >
                    <i class="w-4 h-4 mr-1" data-lucide="play"></i> Start
                  </button>
                  <button
                    class="bg-[var(--bg-secondary)] hover:bg-[var(--accent-tertiary)]/50 text-[var(--text-primary)] px-3 py-2 rounded-lg text-sm font-medium transition-colors focus-ring"
                  >
                    <i class="w-4 h-4 mr-1" data-lucide="eye"></i> Preview
                  </button>
                </div>
              </div>
            </div>

            <div
              class="bg-[var(--bg-secondary)]/50 border border-[var(--text-subtle)]/20 p-5 rounded-xl hover:bg-[var(--bg-secondary)]/70 transition-all duration-300 cursor-pointer hover-lift group opacity-60"
              onclick="openLecture('perfect-competition', 'video')"
            >
              <div class="flex items-center space-x-4">
                <div
                  class="w-12 h-12 bg-[var(--text-subtle)] rounded-full flex items-center justify-center text-white shadow-sm geist-mono font-bold"
                >
                  10
                </div>
                <div class="flex-1">
                  <h3
                    class="text-lg font-semibold text-[var(--text-secondary)] mb-1"
                  >
                    Perfect Competition Analysis
                  </h3>
                  <div
                    class="flex items-center space-x-4 text-sm text-[var(--text-muted)]"
                  >
                    <span
                      class="bg-gray-100 text-gray-600 px-2 py-1 rounded-lg font-medium"
                      >Video</span
                    >
                    <span class="flex items-center"
                      ><i class="w-4 h-4 mr-1" data-lucide="calendar"></i> Week
                      3</span
                    >
                    <span class="flex items-center"
                      ><i class="w-4 h-4 mr-1" data-lucide="clock"></i> 48
                      min</span
                    >
                    <span class="flex items-center text-[var(--text-subtle)]"
                      ><i class="w-4 h-4 mr-1" data-lucide="lock"></i>
                      Locked</span
                    >
                  </div>
                </div>
                <div class="flex space-x-2">
                  <button
                    class="bg-[var(--text-subtle)] text-white px-4 py-2 rounded-lg text-sm font-medium cursor-not-allowed"
                  >
                    <i class="w-4 h-4 mr-1" data-lucide="lock"></i> Locked
                  </button>
                </div>
              </div>
            </div>

            <!-- Week 4 -->
            <div
              class="bg-[var(--bg-secondary)]/50 border border-[var(--text-subtle)]/20 p-5 rounded-xl hover:bg-[var(--bg-secondary)]/70 transition-all duration-300 cursor-pointer hover-lift group opacity-60"
              onclick="openLecture('monopoly', 'pdf')"
            >
              <div class="flex items-center space-x-4">
                <div
                  class="w-12 h-12 bg-[var(--text-subtle)] rounded-full flex items-center justify-center text-white shadow-sm geist-mono font-bold"
                >
                  11
                </div>
                <div class="flex-1">
                  <h3
                    class="text-lg font-semibold text-[var(--text-secondary)] mb-1"
                  >
                    Monopoly and Market Power
                  </h3>
                  <div
                    class="flex items-center space-x-4 text-sm text-[var(--text-muted)]"
                  >
                    <span
                      class="bg-gray-100 text-gray-600 px-2 py-1 rounded-lg font-medium"
                      >PDF</span
                    >
                    <span class="flex items-center"
                      ><i class="w-4 h-4 mr-1" data-lucide="calendar"></i> Week
                      4</span
                    >
                    <span class="flex items-center"
                      ><i class="w-4 h-4 mr-1" data-lucide="clock"></i> 40
                      min</span
                    >
                    <span class="flex items-center text-[var(--text-subtle)]"
                      ><i class="w-4 h-4 mr-1" data-lucide="lock"></i>
                      Locked</span
                    >
                  </div>
                </div>
                <div class="flex space-x-2">
                  <button
                    class="bg-[var(--text-subtle)] text-white px-4 py-2 rounded-lg text-sm font-medium cursor-not-allowed"
                  >
                    <i class="w-4 h-4 mr-1" data-lucide="lock"></i> Locked
                  </button>
                </div>
              </div>
            </div>

            <div
              class="bg-[var(--bg-secondary)]/50 border border-[var(--text-subtle)]/20 p-5 rounded-xl hover:bg-[var(--bg-secondary)]/70 transition-all duration-300 cursor-pointer hover-lift group opacity-60"
              onclick="openLecture('oligopoly', 'audio')"
            >
              <div class="flex items-center space-x-4">
                <div
                  class="w-12 h-12 bg-[var(--text-subtle)] rounded-full flex items-center justify-center text-white shadow-sm geist-mono font-bold"
                >
                  12
                </div>
                <div class="flex-1">
                  <h3
                    class="text-lg font-semibold text-[var(--text-secondary)] mb-1"
                  >
                    Oligopoly and Game Theory
                  </h3>
                  <div
                    class="flex items-center space-x-4 text-sm text-[var(--text-muted)]"
                  >
                    <span
                      class="bg-gray-100 text-gray-600 px-2 py-1 rounded-lg font-medium"
                      >Audio</span
                    >
                    <span class="flex items-center"
                      ><i class="w-4 h-4 mr-1" data-lucide="calendar"></i> Week
                      4</span
                    >
                    <span class="flex items-center"
                      ><i class="w-4 h-4 mr-1" data-lucide="clock"></i> 55
                      min</span
                    >
                    <span class="flex items-center text-[var(--text-subtle)]"
                      ><i class="w-4 h-4 mr-1" data-lucide="lock"></i>
                      Locked</span
                    >
                  </div>
                </div>
                <div class="flex space-x-2">
                  <button
                    class="bg-[var(--text-subtle)] text-white px-4 py-2 rounded-lg text-sm font-medium cursor-not-allowed"
                  >
                    <i class="w-4 h-4 mr-1" data-lucide="lock"></i> Locked
                  </button>
                </div>
              </div>
            </div>

            <!-- More lectures... -->
            <div
              class="bg-[var(--bg-secondary)]/50 border border-[var(--text-subtle)]/20 p-5 rounded-xl hover:bg-[var(--bg-secondary)]/70 transition-all duration-300 cursor-pointer hover-lift group opacity-60"
              onclick="openLecture('consumer-theory', 'video')"
            >
              <div class="flex items-center space-x-4">
                <div
                  class="w-12 h-12 bg-[var(--text-subtle)] rounded-full flex items-center justify-center text-white shadow-sm geist-mono font-bold"
                >
                  13
                </div>
                <div class="flex-1">
                  <h3
                    class="text-lg font-semibold text-[var(--text-secondary)] mb-1"
                  >
                    Consumer Choice Theory
                  </h3>
                  <div
                    class="flex items-center space-x-4 text-sm text-[var(--text-muted)]"
                  >
                    <span
                      class="bg-gray-100 text-gray-600 px-2 py-1 rounded-lg font-medium"
                      >Video</span
                    >
                    <span class="flex items-center"
                      ><i class="w-4 h-4 mr-1" data-lucide="calendar"></i> Week
                      5</span
                    >
                    <span class="flex items-center"
                      ><i class="w-4 h-4 mr-1" data-lucide="clock"></i> 46
                      min</span
                    >
                    <span class="flex items-center text-[var(--text-subtle)]"
                      ><i class="w-4 h-4 mr-1" data-lucide="lock"></i>
                      Locked</span
                    >
                  </div>
                </div>
                <div class="flex space-x-2">
                  <button
                    class="bg-[var(--text-subtle)] text-white px-4 py-2 rounded-lg text-sm font-medium cursor-not-allowed"
                  >
                    <i class="w-4 h-4 mr-1" data-lucide="lock"></i> Locked
                  </button>
                </div>
              </div>
            </div>

            <div
              class="bg-[var(--bg-secondary)]/50 border border-[var(--text-subtle)]/20 p-5 rounded-xl hover:bg-[var(--bg-secondary)]/70 transition-all duration-300 cursor-pointer hover-lift group opacity-60"
              onclick="openLecture('production-costs', 'pdf')"
            >
              <div class="flex items-center space-x-4">
                <div
                  class="w-12 h-12 bg-[var(--text-subtle)] rounded-full flex items-center justify-center text-white shadow-sm geist-mono font-bold"
                >
                  14
                </div>
                <div class="flex-1">
                  <h3
                    class="text-lg font-semibold text-[var(--text-secondary)] mb-1"
                  >
                    Production and Cost Analysis
                  </h3>
                  <div
                    class="flex items-center space-x-4 text-sm text-[var(--text-muted)]"
                  >
                    <span
                      class="bg-gray-100 text-gray-600 px-2 py-1 rounded-lg font-medium"
                      >PDF</span
                    >
                    <span class="flex items-center"
                      ><i class="w-4 h-4 mr-1" data-lucide="calendar"></i> Week
                      5</span
                    >
                    <span class="flex items-center"
                      ><i class="w-4 h-4 mr-1" data-lucide="clock"></i> 35
                      min</span
                    >
                    <span class="flex items-center text-[var(--text-subtle)]"
                      ><i class="w-4 h-4 mr-1" data-lucide="lock"></i>
                      Locked</span
                    >
                  </div>
                </div>
                <div class="flex space-x-2">
                  <button
                    class="bg-[var(--text-subtle)] text-white px-4 py-2 rounded-lg text-sm font-medium cursor-not-allowed"
                  >
                    <i class="w-4 h-4 mr-1" data-lucide="lock"></i> Locked
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <script>
      lucide.createIcons();

      // Enhanced staggered animations from dashboard
      const staggeredItems = document.querySelectorAll(".stagger-entrance");
      staggeredItems.forEach((item) => {
        const children = Array.from(item.children);
        children.forEach((child, index) => {
          child.style.animationDelay = `${index * 0.075}s`;
        });
      });

      // Enhanced click handlers with feedback
      const interactiveElements = document.querySelectorAll(
        '[tabindex="0"], button, a'
      );
      interactiveElements.forEach((element) => {
        element.addEventListener("click", function (e) {
          this.style.transform = "scale(0.98)";
          setTimeout(() => {
            this.style.transform = "";
          }, 150);
        });

        element.addEventListener("keydown", function (e) {
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            this.click();
          }
        });
      });

      // Navigation functions
      function goToDashboard() {
        window.location.href = "dashboard.html";
      }

      function goToCourses() {
        window.location.href = "courses.html";
      }

      function continueLearning() {
        openLecture("market-structures", "pdf");
      }

      function openNotes() {
        window.location.href = "notes.html";
      }

      // Lecture functions
      function openLecture(lectureId, contentType) {
        // Route to appropriate interface based on content type
        switch (contentType) {
          case "pdf":
            window.location.href = "pdf.html?lectureId=" + lectureId;
            break;
          case "video":
            window.location.href = "video.html?lectureId=" + lectureId;
            break;
          case "audio":
            window.location.href = "audio.html?lectureId=" + lectureId;
            break;
          default:
            alert(`Opening lecture: ${lectureId}`);
        }
      }

      // Study tool functions
      function generateQuiz() {
        alert("Generating AI quiz based on your progress...");
        window.location.href = "quiz.html";
      }

      function createFlashcards() {
        alert("Opening adaptive flashcards...");
        window.location.href = "flashcard.html";
      }

      function getSummary() {
        alert("Generating AI summary of completed lectures...");
      }

      function askAI() {
        const question = prompt(
          "What would you like to ask about this course?"
        );
        if (question) {
          alert(
            `AI: Great question about "${question}"! Let me help you understand this concept better...`
          );
        }
      }

      function openFlashcards() {
        window.location.href = "flashcard.html";
      }

      function openQuizzes() {
        window.location.href = "quiz.html";
      }

      // Keyboard shortcuts
      document.addEventListener("keydown", function (e) {
        if (e.ctrlKey || e.metaKey) {
          switch (e.key) {
            case "Enter":
              e.preventDefault();
              continueLearning();
              break;
            case "n":
              e.preventDefault();
              openNotes();
              break;
            case "q":
              e.preventDefault();
              generateQuiz();
              break;
            case "f":
              e.preventDefault();
              createFlashcards();
              break;
          }
        }
      });

      // Accessibility improvements
      document.addEventListener("DOMContentLoaded", function () {
        const quickActions = document.querySelectorAll(
          ".sidebar-card-item button"
        );
        quickActions.forEach((button) => {
          button.setAttribute("aria-label", button.textContent.trim());
        });
      });

      // Progress bar animations on scroll
      const observerOptions = {
        threshold: 0.5,
        rootMargin: "0px 0px -100px 0px",
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const progressBar = entry.target.querySelector(".bg-white");
            if (progressBar && progressBar.style.width) {
              const width = progressBar.style.width;
              progressBar.style.width = "0%";
              setTimeout(() => {
                progressBar.style.width = width;
              }, 100);
            }
          }
        });
      }, observerOptions);

      // Observe hero section for progress animation
      const heroSection = document.querySelector(".animated-gradient");
      if (heroSection) observer.observe(heroSection);
    </script>
  </body>
</html>
