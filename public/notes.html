<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI Notes Interface</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: #0a0b0d;
        color: #ffffff;
        height: 100vh;
        overflow: hidden;
      }

      .notes-container {
        display: flex;
        height: 100vh;
      }

      /* Notes Sidebar */
      .notes-sidebar {
        width: 320px;
        background: #0f1114;
        border-right: 1px solid #2a2d31;
        display: flex;
        flex-direction: column;
      }

      .sidebar-header {
        background: #1a1c1f;
        padding: 20px;
        border-bottom: 1px solid #2a2d31;
      }

      .notes-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .search-container {
        position: relative;
        margin-bottom: 16px;
      }

      .search-input {
        width: 100%;
        background: #252830;
        border: 1px solid #3a3d45;
        border-radius: 8px;
        padding: 10px 12px 10px 36px;
        color: #ffffff;
        font-size: 14px;
      }

      .search-input:focus {
        outline: none;
        border-color: #4ade80;
      }

      .search-icon {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #b0b3b8;
      }

      .filter-tabs {
        display: flex;
        gap: 8px;
      }

      .filter-tab {
        background: #252830;
        border: none;
        color: #b0b3b8;
        padding: 6px 12px;
        border-radius: 16px;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.2s;
      }

      .filter-tab.active {
        background: #4ade80;
        color: #1a1c1f;
      }

      .notes-list {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
      }

      .note-item {
        background: #1a1c1f;
        border: 1px solid #2a2d31;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.2s;
      }

      .note-item:hover {
        border-color: #4ade80;
        background: rgba(74, 222, 128, 0.05);
      }

      .note-item.active {
        border-color: #4ade80;
        background: rgba(74, 222, 128, 0.1);
      }

      .note-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 6px;
      }

      .note-type {
        background: #4ade80;
        color: #1a1c1f;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 10px;
        font-weight: 600;
      }

      .note-date {
        color: #b0b3b8;
        font-size: 11px;
      }

      .note-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 4px;
      }

      .note-preview {
        color: #b0b3b8;
        font-size: 12px;
        line-height: 1.3;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .note-tags {
        display: flex;
        gap: 4px;
        margin-top: 6px;
      }

      .note-tag {
        background: #252830;
        color: #4ade80;
        padding: 2px 6px;
        border-radius: 8px;
        font-size: 10px;
      }

      /* Main Notes Editor */
      .notes-main {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: #161719;
      }

      .editor-header {
        background: #1a1c1f;
        padding: 16px 24px;
        border-bottom: 1px solid #2a2d31;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .note-info {
        display: flex;
        align-items: center;
        gap: 16px;
      }

      .note-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        background: linear-gradient(135deg, #4ade80 0%, #3b82f6 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
      }

      .note-details h2 {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 4px;
      }

      .note-meta {
        color: #b0b3b8;
        font-size: 12px;
        display: flex;
        gap: 12px;
      }

      .editor-tools {
        display: flex;
        gap: 8px;
      }

      .tool-btn {
        background: #2a2d31;
        border: none;
        color: #ffffff;
        padding: 8px 12px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.2s;
      }

      .tool-btn:hover {
        background: #3a3d45;
      }

      .tool-btn.active {
        background: #4ade80;
        color: #1a1c1f;
      }

      /* Editor Content */
      .editor-content {
        flex: 1;
        display: flex;
      }

      .note-editor {
        flex: 1;
        background: #1a1c1f;
        border: none;
        color: #ffffff;
        padding: 24px;
        font-size: 16px;
        line-height: 1.6;
        resize: none;
        font-family: inherit;
      }

      .note-editor:focus {
        outline: none;
      }

      .note-editor::placeholder {
        color: #6b7280;
      }

      /* AI Assistant Panel */
      /* AI Assistant Panel */
      .ai-assistant {
        width: 350px;
        background: #0f1114;
        border-left: 1px solid #2a2d31;
        display: flex;
        flex-direction: column;
      }

      .ai-assistant-header {
        background: linear-gradient(135deg, #4ade80 0%, #3b82f6 100%);
        padding: 16px;
        color: #ffffff;
      }

      .ai-status {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 8px;
      }

      .ai-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
      }

      .ai-assistant-content {
        flex: 1;
        padding: 16px;
        overflow-y: auto;
      }

      .ai-suggestion {
        background: #1a1c1f;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 12px;
        border-left: 3px solid #4ade80;
      }

      .suggestion-header {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-bottom: 6px;
      }

      .suggestion-icon {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: #4ade80;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #1a1c1f;
        font-size: 8px;
      }

      .suggestion-title {
        font-size: 12px;
        font-weight: 600;
        color: #4ade80;
      }

      .suggestion-text {
        font-size: 13px;
        color: #b0b3b8;
        line-height: 1.4;
      }

      .suggestion-action {
        background: #4ade80;
        border: none;
        color: #1a1c1f;
        padding: 4px 8px;
        border-radius: 12px;
        cursor: pointer;
        font-size: 10px;
        margin-top: 6px;
        font-weight: 500;
      }

      .smart-highlights {
        background: #252830;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 12px;
      }

      .highlights-header {
        font-size: 12px;
        font-weight: 600;
        color: #ffffff;
        margin-bottom: 8px;
      }

      .highlight-item {
        color: #4ade80;
        font-size: 11px;
        margin-bottom: 4px;
        cursor: pointer;
      }

      .highlight-item:hover {
        color: #22c55e;
      }

      .concept-connections {
        background: #1a1c1f;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 12px;
      }

      .connections-header {
        font-size: 12px;
        font-weight: 600;
        color: #ffffff;
        margin-bottom: 8px;
      }

      .connection-item {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-bottom: 6px;
        font-size: 11px;
        color: #b0b3b8;
        cursor: pointer;
      }

      .connection-item:hover {
        color: #4ade80;
      }

      .connection-icon {
        color: #4ade80;
      }

      .ai-input-section {
        padding: 16px;
        border-top: 1px solid #2a2d31;
      }

      .ai-input-container {
        position: relative;
      }

      .ai-input {
        width: 100%;
        background: #1a1c1f;
        border: 1px solid #2a2d31;
        border-radius: 8px;
        padding: 8px 36px 8px 12px;
        color: #ffffff;
        font-size: 12px;
        resize: none;
        min-height: 32px;
      }

      .ai-input:focus {
        outline: none;
        border-color: #4ade80;
      }

      .ai-send-btn {
        position: absolute;
        right: 6px;
        top: 50%;
        transform: translateY(-50%);
        background: #4ade80;
        border: none;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #1a1c1f;
        font-size: 10px;
      }

      /* Floating AI Insights */
      .floating-insight {
        position: absolute;
        background: rgba(26, 28, 31, 0.95);
        border: 1px solid #4ade80;
        border-radius: 8px;
        padding: 8px 12px;
        font-size: 12px;
        color: #4ade80;
        z-index: 100;
        backdrop-filter: blur(10px);
        max-width: 200px;
        opacity: 0;
        transform: translateY(10px);
        transition: all 0.3s ease;
        pointer-events: none;
      }

      .floating-insight.show {
        opacity: 1;
        transform: translateY(0);
      }

      /* Auto-save indicator */
      .autosave-indicator {
        position: absolute;
        top: 16px;
        right: 24px;
        color: #4ade80;
        font-size: 12px;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .autosave-indicator.saving {
        opacity: 1;
      }

      /* Smart formatting */
      .smart-format {
        background: rgba(74, 222, 128, 0.1);
        border-radius: 4px;
        padding: 2px 4px;
        border-bottom: 1px dotted #4ade80;
      }

      /* Note organization */
      .organization-section {
        margin-bottom: 16px;
      }

      .section-title {
        font-size: 12px;
        font-weight: 600;
        color: #b0b3b8;
        margin-bottom: 8px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .quick-actions {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 6px;
        margin-bottom: 12px;
      }

      .quick-action {
        background: #252830;
        border: none;
        color: #b0b3b8;
        padding: 6px 8px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 10px;
        transition: all 0.2s;
      }

      .quick-action:hover {
        background: #4ade80;
        color: #1a1c1f;
      }

      /* Responsive design */
      @media (max-width: 1200px) {
        .ai-assistant {
          display: none;
        }
      }

      @media (max-width: 768px) {
        .notes-sidebar {
          width: 280px;
        }
      }
    </style>
  </head>
  <body>
    <div class="notes-container">
      <!-- Notes Sidebar -->
      <div class="notes-sidebar">
        <div class="sidebar-header">
          <div class="notes-title">📝 Smart Notes</div>

          <div class="search-container">
            <input
              type="text"
              class="search-input"
              placeholder="Search notes and concepts..."
            />
            <div class="search-icon">🔍</div>
          </div>

          <div class="filter-tabs">
            <button class="filter-tab active">All</button>
            <button class="filter-tab">My Notes</button>
            <button class="filter-tab">AI Generated</button>
            <button class="filter-tab">Highlights</button>
          </div>
        </div>

        <div class="notes-list">
          <div class="note-item active">
            <div class="note-header">
              <div class="note-type">Manual</div>
              <div class="note-date">2 min ago</div>
            </div>
            <div class="note-title">Market Structure Key Points</div>
            <div class="note-preview">
              Perfect competition vs monopolistic competition differences.
              Remember the restaurant analogy...
            </div>
            <div class="note-tags">
              <div class="note-tag">economics</div>
              <div class="note-tag">market-structures</div>
            </div>
          </div>

          <div class="note-item">
            <div class="note-header">
              <div class="note-type" style="background: #3b82f6">AI</div>
              <div class="note-date">15 min ago</div>
            </div>
            <div class="note-title">Auto-Summary: Lecture 9</div>
            <div class="note-preview">
              AI-generated summary of monopolistic competition concepts with key
              formulas and examples...
            </div>
            <div class="note-tags">
              <div class="note-tag">auto-summary</div>
              <div class="note-tag">lecture-9</div>
            </div>
          </div>

          <div class="note-item">
            <div class="note-header">
              <div class="note-type" style="background: #f59e0b">Highlight</div>
              <div class="note-date">1 hour ago</div>
            </div>
            <div class="note-title">Price Takers vs Price Makers</div>
            <div class="note-preview">
              Highlighted from PDF: "In perfect competition, firms are price
              takers because..."
            </div>
            <div class="note-tags">
              <div class="note-tag">pdf-highlight</div>
              <div class="note-tag">definitions</div>
            </div>
          </div>

          <div class="note-item">
            <div class="note-header">
              <div class="note-type" style="background: #ef4444">Question</div>
              <div class="note-date">Yesterday</div>
            </div>
            <div class="note-title">Confusion: Barriers to Entry</div>
            <div class="note-preview">
              Need to clarify the difference between natural and artificial
              barriers. Ask AI for examples...
            </div>
            <div class="note-tags">
              <div class="note-tag">confusion</div>
              <div class="note-tag">need-review</div>
            </div>
          </div>

          <div class="note-item">
            <div class="note-header">
              <div class="note-type" style="background: #8b5cf6">Insight</div>
              <div class="note-date">2 days ago</div>
            </div>
            <div class="note-title">Connection: Supply Curves</div>
            <div class="note-preview">
              Just realized how supply curves from Lecture 3 connect to firm
              behavior in different market structures...
            </div>
            <div class="note-tags">
              <div class="note-tag">connection</div>
              <div class="note-tag">breakthrough</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Notes Editor -->
      <div class="notes-main">
        <div class="editor-header">
          <div class="note-info">
            <div class="note-icon">📝</div>
            <div class="note-details">
              <h2>Market Structure Key Points</h2>
              <div class="note-meta">
                <span>📊 Economics 101</span>
                <span>🕒 Last edited 2 min ago</span>
                <span>📖 Linked to Lecture 9</span>
              </div>
            </div>
          </div>

          <div class="editor-tools">
            <button class="tool-btn">🎨 Format</button>
            <button class="tool-btn active">✏️ Edit</button>
            <button class="tool-btn">👁️ Preview</button>
            <button class="tool-btn">📤 Share</button>
            <button class="tool-btn">⚙️</button>
          </div>
        </div>

        <div class="editor-content">
          <textarea
            class="note-editor"
            placeholder="Start writing your notes... AI will provide suggestions and connections as you type."
          >
# Market Structure Key Points

## Perfect Competition vs Monopolistic Competition

**Perfect Competition:**
- Many sellers, identical products
- Price takers (no control over price)
- Easy entry/exit
- Example: Agricultural markets

**Monopolistic Competition:**
- Many sellers, differentiated products
- Some price control due to brand loyalty
- Easy entry/exit
- Example: Restaurants, clothing brands

### Key Insight 💡
Think of restaurants in a food court - they all serve food (similar function) but each has unique offerings (differentiated products), allowing them to charge slightly different prices.

## Important Formulas
- Profit = Total Revenue - Total Cost
- MR = MC (profit maximization condition)

## Questions to Review:
- How do barriers to entry affect market structure?
- What role does advertising play in monopolistic competition?

---
*Connected to: Lecture 3 (Supply/Demand), Lecture 5 (Perfect Competition)*
                </textarea
          >

          <div class="autosave-indicator" id="autosaveIndicator">
            💾 Saving...
          </div>

          <div class="floating-insight" id="floatingInsight">
            💡 AI detected a great connection to supply curves!
          </div>
        </div>
      </div>

      <!-- AI Assistant Panel -->
      <div class="ai-assistant">
        <div class="ai-assistant-header">
          <div class="ai-status">
            <div class="ai-avatar">🤖</div>
            <div>
              <div style="font-weight: 600; font-size: 14px">
                Note Assistant
              </div>
              <div style="font-size: 11px; opacity: 0.8">
                Analyzing your notes
              </div>
            </div>
          </div>
          <div style="font-size: 11px; opacity: 0.8">
            Real-time suggestions & connections
          </div>
        </div>

        <div class="ai-assistant-content">
          <div class="organization-section">
            <div class="section-title">Quick Actions</div>
            <div class="quick-actions">
              <button class="quick-action">📋 Summary</button>
              <button class="quick-action">🏷️ Add Tags</button>
              <button class="quick-action">🔗 Find Links</button>
              <button class="quick-action">❓ Generate Quiz</button>
            </div>
          </div>

          <div class="ai-suggestion">
            <div class="suggestion-header">
              <div class="suggestion-icon">💡</div>
              <div class="suggestion-title">Smart Suggestion</div>
            </div>
            <div class="suggestion-text">
              Your restaurant analogy is excellent! Consider adding a visual
              diagram to reinforce this concept.
            </div>
            <button
              class="suggestion-action"
              onclick="applySuggestion('diagram')"
            >
              Add Diagram
            </button>
          </div>

          <div class="ai-suggestion">
            <div class="suggestion-header">
              <div class="suggestion-icon">🔗</div>
              <div class="suggestion-title">Missing Connection</div>
            </div>
            <div class="suggestion-text">
              This relates to elasticity concepts from Lecture 5. Want to add
              that connection?
            </div>
            <button
              class="suggestion-action"
              onclick="applySuggestion('elasticity')"
            >
              Link Elasticity
            </button>
          </div>

          <div class="smart-highlights">
            <div class="highlights-header">🎯 Smart Highlights</div>
            <div class="highlight-item" onclick="jumpToHighlight('profit-max')">
              → Profit maximization condition
            </div>
            <div class="highlight-item" onclick="jumpToHighlight('barriers')">
              → Barriers to entry question
            </div>
            <div class="highlight-item" onclick="jumpToHighlight('restaurant')">
              → Restaurant analogy
            </div>
          </div>

          <div class="concept-connections">
            <div class="connections-header">🕸️ Concept Web</div>
            <div
              class="connection-item"
              onclick="showConnection('supply-demand')"
            >
              <span class="connection-icon">↗</span>
              <span>Supply & Demand (Lecture 3)</span>
            </div>
            <div class="connection-item" onclick="showConnection('elasticity')">
              <span class="connection-icon">↗</span>
              <span>Price Elasticity (Lecture 5)</span>
            </div>
            <div
              class="connection-item"
              onclick="showConnection('consumer-theory')"
            >
              <span class="connection-icon">↗</span>
              <span>Consumer Theory (Lecture 6)</span>
            </div>
          </div>

          <div class="ai-suggestion">
            <div class="suggestion-header">
              <div class="suggestion-icon">📊</div>
              <div class="suggestion-title">Study Tip</div>
            </div>
            <div class="suggestion-text">
              Create a comparison table for all market structures. This will
              help on the upcoming exam.
            </div>
            <button
              class="suggestion-action"
              onclick="applySuggestion('table')"
            >
              Create Table
            </button>
          </div>
        </div>

        <div class="ai-input-section">
          <div class="ai-input-container">
            <textarea
              class="ai-input"
              placeholder="Ask about concepts, request explanations, or get study suggestions..."
            ></textarea>
            <button class="ai-send-btn">↗</button>
          </div>
        </div>
      </div>
    </div>

    <script>
      let saveTimeout;

      // Auto-save functionality
      document
        .querySelector(".note-editor")
        .addEventListener("input", function () {
          const indicator = document.getElementById("autosaveIndicator");
          indicator.classList.add("saving");

          clearTimeout(saveTimeout);
          saveTimeout = setTimeout(() => {
            indicator.classList.remove("saving");
            // Simulate save complete
          }, 1500);
        });

      // Note item selection
      document.querySelectorAll(".note-item").forEach((item) => {
        item.addEventListener("click", function () {
          document
            .querySelectorAll(".note-item")
            .forEach((i) => i.classList.remove("active"));
          this.classList.add("active");

          // Load note content (simulated)
          const title = this.querySelector(".note-title").textContent;
          document.querySelector(".note-details h2").textContent = title;
        });
      });

      // Filter tabs
      document.querySelectorAll(".filter-tab").forEach((tab) => {
        tab.addEventListener("click", function () {
          document
            .querySelectorAll(".filter-tab")
            .forEach((t) => t.classList.remove("active"));
          this.classList.add("active");

          // Filter notes (simulated)
          console.log("Filtering by:", this.textContent);
        });
      });

      // AI suggestion actions
      function applySuggestion(type) {
        const editor = document.querySelector(".note-editor");
        const currentText = editor.value;

        switch (type) {
          case "diagram":
            editor.value =
              currentText +
              "\n\n## Visual Diagram\n[Restaurant Food Court Analogy Diagram]\n- Each restaurant = Firm in monopolistic competition\n- Different cuisines = Product differentiation\n- Shared space = Market environment";
            break;
          case "elasticity":
            editor.value =
              currentText +
              "\n\n### Connection to Price Elasticity\nIn monopolistic competition, demand is more elastic than in monopoly but less elastic than in perfect competition due to product differentiation.";
            break;
          case "table":
            editor.value =
              currentText +
              "\n\n## Market Structure Comparison Table\n\n| Aspect | Perfect Competition | Monopolistic Competition | Oligopoly | Monopoly |\n|--------|-------------------|------------------------|-----------|----------|\n| Number of firms | Many | Many | Few | One |\n| Product differentiation | None | High | Some | Unique |\n| Barriers to entry | Low | Low | High | Very High |";
            break;
        }

        // Trigger autosave
        editor.dispatchEvent(new Event("input"));
      }

      // Highlight jumping
      function jumpToHighlight(section) {
        const editor = document.querySelector(".note-editor");
        const text = editor.value;

        // Simple search and focus (in real app, would scroll to specific line)
        let searchTerm;
        switch (section) {
          case "profit-max":
            searchTerm = "MR = MC";
            break;
          case "barriers":
            searchTerm = "barriers to entry";
            break;
          case "restaurant":
            searchTerm = "restaurants in a food court";
            break;
        }

        const index = text.toLowerCase().indexOf(searchTerm.toLowerCase());
        if (index !== -1) {
          editor.focus();
          editor.setSelectionRange(index, index + searchTerm.length);
        }
      }

      // Show concept connections
      function showConnection(concept) {
        alert(`Opening connection to ${concept}...`);
        // In real app, this would open the related lecture or notes
      }

      // Simulate floating insights
      setTimeout(() => {
        const insight = document.getElementById("floatingInsight");
        insight.classList.add("show");

        setTimeout(() => {
          insight.classList.remove("show");
        }, 3000);
      }, 5000);

      // Search functionality
      document
        .querySelector(".search-input")
        .addEventListener("input", function (e) {
          const searchTerm = e.target.value.toLowerCase();
          const noteItems = document.querySelectorAll(".note-item");

          noteItems.forEach((item) => {
            const title = item
              .querySelector(".note-title")
              .textContent.toLowerCase();
            const preview = item
              .querySelector(".note-preview")
              .textContent.toLowerCase();

            if (title.includes(searchTerm) || preview.includes(searchTerm)) {
              item.style.display = "block";
            } else {
              item.style.display = searchTerm ? "none" : "block";
            }
          });
        });
    </script>
  </body>
</html>
