<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>StudyMind - Video Lecture</title>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Geist:wght@100..900&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Geist+Mono:wght@100..900&family=Geist:wght@100..900&display=swap"
      rel="stylesheet"
    />

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>
    <style type="text/tailwindcss">
      :root {
        --bg-primary: #fdfbf6;
        --bg-secondary: #f0e5d8;
        --text-primary: #2d3748;
        --text-secondary: #4a5568;
        --text-muted: #718096;
        --text-subtle: #a0aec0;
        --text-on-tinted: #2d3748;
        --text-on-tinted-muted: #4a5568;
        --text-on-tinted-subtle: #718096;
        --accent-primary: #a9c2b9;
        --accent-secondary: #d3b8ae;
        --accent-tertiary: #e6cba5;
        --highlight: #8b9d8f;
        --success: #68d391;
        --warning: #f6ad55;
        --info: #63b3ed;
        --video-bg: #1a1c1f;
      }

      body {
        font-family: "Geist", sans-serif;
        background-color: var(--bg-primary);
        color: var(--text-primary);
      }
      .geist-mono {
        font-family: "Geist Mono", monospace;
      }

      .stagger-entrance > * {
        opacity: 0;
        transform: translateY(20px);
        animation: fadeInUp 0.6s forwards;
      }
      @keyframes fadeInUp {
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .hover-lift {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .hover-lift:hover {
        transform: translateY(-2px) scale(1.01);
        box-shadow: 0 8px 16px -4px rgba(45, 55, 72, 0.12),
          0 4px 6px -2px rgba(45, 55, 72, 0.08);
      }

      .minimal-artwork-bg {
        background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23e6cba5' fill-opacity='0.1'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-repeat: repeat;
        background-size: 200px 200px;
      }
      .minimal-artwork-subtle {
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23a9c2b9' fill-opacity='0.07'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-repeat: repeat;
      }

      .learning-focus {
        background: var(--video-bg);
        border-radius: 1rem;
        overflow: hidden;
        box-shadow: 0 20px 32px -8px rgba(45, 55, 72, 0.15),
          0 8px 16px -4px rgba(45, 55, 72, 0.1);
      }

      .video-controls {
        background: linear-gradient(
          to top,
          rgba(26, 28, 31, 0.95),
          rgba(26, 28, 31, 0.3)
        );
        backdrop-filter: blur(8px);
      }

      .progress-bar {
        background: rgba(169, 194, 185, 0.2);
        transition: all 0.3s ease;
      }

      .progress-fill {
        background: var(--accent-primary);
        transition: width 0.5s cubic-bezier(0.65, 0, 0.35, 1);
      }

      .chapter-marker {
        background: rgba(255, 255, 255, 0.6);
        width: 1px;
        transition: opacity 0.3s ease;
      }

      .ai-insight {
        background: rgba(169, 194, 185, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(169, 194, 185, 0.3);
        transition: all 0.4s ease;
        transform: translateY(0);
      }

      .ai-insight.hidden {
        transform: translateY(-100%);
        opacity: 0;
      }

      .focus-mode .ai-panel {
        display: none;
      }

      .focus-mode .video-panel {
        width: 100%;
      }

      .focus-mode .chapter-navigation {
        display: none;
      }

      .breathing-animation {
        animation: breathe 4s ease-in-out infinite;
      }

      @keyframes breathe {
        0%,
        100% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.02);
        }
      }

      .sidebar-item {
        transition: all 0.3s ease;
        border-radius: 0.75rem;
      }

      .sidebar-item:hover {
        background-color: var(--bg-secondary);
        transform: translateX(4px);
      }

      .focus-ring:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(169, 194, 185, 0.3);
        border-radius: 8px;
      }

      /* Enhanced button styles */
      .btn-primary {
        background: var(--accent-primary);
        color: white;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        border-radius: 0.75rem;
        transition: all 0.2s ease;
        border: none;
        cursor: pointer;
      }

      .btn-primary:hover {
        background: var(--highlight);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(169, 194, 185, 0.3);
      }

      .chapter-current {
        background: rgba(169, 194, 185, 0.15);
        border-color: var(--accent-primary);
      }

      /* Tab Styles */
      .tab-button {
        background: var(--bg-primary);
        color: var(--text-muted);
        border: 1px solid var(--bg-secondary);
      }

      .tab-button:hover {
        background: var(--bg-secondary);
        color: var(--text-secondary);
      }

      .tab-active {
        background: var(--accent-primary) !important;
        color: white !important;
        border-color: var(--accent-primary) !important;
      }

      .tab-content {
        transition: opacity 0.3s ease;
      }

      .transcript-item {
        transition: all 0.3s ease;
      }

      .transcript-item:hover {
        background: var(--bg-primary);
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-left: -0.75rem;
        padding-left: 1.5rem;
      }

      @media (max-width: 1024px) {
        .ai-panel {
          width: 320px;
        }
      }

      @media (max-width: 768px) {
        .lecture-container {
          flex-direction: column;
        }
        .ai-panel {
          width: 100%;
          height: 40vh;
        }
      }
    </style>
  </head>
  <body class="text-[var(--text-primary)]">
    <div class="flex h-screen">
      <!-- Video Panel -->
      <div
        class="video-panel flex-1 bg-[var(--bg-primary)] flex flex-col transition-all duration-500"
      >
        <!-- Header -->
        <header
          class="flex justify-between items-center p-6 bg-white border-b border-[var(--bg-secondary)]"
        >
          <div class="flex items-center space-x-4">
            <button
              onclick="goBack()"
              class="w-10 h-10 bg-[var(--bg-secondary)] rounded-full flex items-center justify-center hover:bg-[var(--accent-tertiary)]/50 transition-colors focus-ring"
            >
              <i
                class="w-5 h-5 text-[var(--text-primary)]"
                data-lucide="arrow-left"
              ></i>
            </button>
            <div>
              <h1 class="text-xl font-semibold text-[var(--text-primary)]">
                Market Structures and Competition
              </h1>
              <div
                class="flex items-center space-x-4 text-sm text-[var(--text-muted)] mt-1"
              >
                <span class="flex items-center">
                  <i class="w-4 h-4 mr-1" data-lucide="calendar"></i>
                  Week 3
                </span>
                <span class="flex items-center">
                  <i class="w-4 h-4 mr-1" data-lucide="clock"></i>
                  42 minutes
                </span>
                <span class="flex items-center">
                  <i class="w-4 h-4 mr-1" data-lucide="user"></i>
                  Prof. Johnson
                </span>
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            <button
              id="focusToggle"
              onclick="toggleFocus()"
              class="btn-primary text-sm"
            >
              <i class="w-4 h-4" data-lucide="focus"></i>
            </button>
            <div class="flex items-center space-x-3">
              <div
                class="w-8 h-8 bg-[var(--accent-secondary)] rounded-full flex items-center justify-center"
              >
                <img
                  src="/images/profile-avatar.png"
                  alt="Avatar"
                  class="w-6 h-6 rounded-full"
                />
              </div>
            </div>
          </div>
        </header>

        <!-- Video Container -->
        <div class="flex-1 p-6">
          <div class="learning-focus h-full relative group">
            <!-- Video Screen -->
            <div
              class="w-full h-full bg-gradient-to-br from-[var(--accent-tertiary)] via-[var(--accent-primary)] to-[var(--highlight)] flex items-center justify-center relative overflow-hidden"
            >
              <!-- Background Pattern -->
              <div class="absolute inset-0 minimal-artwork-bg opacity-20"></div>

              <!-- Video Content -->
              <div class="relative z-10 text-center text-white">
                <div class="text-6xl mb-4 breathing-animation">📊</div>
                <h2 class="text-2xl font-semibold mb-2">
                  Market Competition Analysis
                </h2>
                <p class="text-white/80">
                  Understanding different market structures
                </p>
              </div>

              <!-- AI Insight Overlay -->
              <div
                class="ai-insight absolute top-6 left-6 right-6 p-4 rounded-xl max-w-md"
                id="aiInsight"
              >
                <div class="flex items-start space-x-3">
                  <div
                    class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0"
                  >
                    <i
                      class="w-4 h-4 text-[var(--text-primary)]"
                      data-lucide="lightbulb"
                    ></i>
                  </div>
                  <div>
                    <div
                      class="text-sm font-semibold text-[var(--text-primary)] mb-1"
                    >
                      Learning Insight
                    </div>
                    <div class="text-xs text-[var(--text-secondary)]">
                      This section builds on supply & demand concepts from last
                      week.
                    </div>
                  </div>
                  <button
                    onclick="hideInsight()"
                    class="text-[var(--text-subtle)] hover:text-[var(--text-primary)]"
                  >
                    <i class="w-4 h-4" data-lucide="x"></i>
                  </button>
                </div>
              </div>

              <!-- Video Controls -->
              <div
                class="absolute bottom-0 left-0 right-0 video-controls p-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              >
                <!-- Progress Bar -->
                <div class="mb-4">
                  <div
                    class="progress-bar w-full h-2 rounded-full relative cursor-pointer"
                    onclick="seekVideo(event)"
                  >
                    <div
                      class="progress-fill h-full rounded-full relative"
                      style="width: 34%"
                      id="progressFill"
                    >
                      <!-- Chapter Markers -->
                      <div class="absolute inset-0">
                        <div
                          class="chapter-marker absolute top-0 h-full"
                          style="left: 20%"
                        ></div>
                        <div
                          class="chapter-marker absolute top-0 h-full"
                          style="left: 45%"
                        ></div>
                        <div
                          class="chapter-marker absolute top-0 h-full"
                          style="left: 70%"
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Playback Controls -->
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-4">
                    <button
                      class="w-12 h-12 bg-white rounded-full flex items-center justify-center text-[var(--text-primary)] hover:bg-white/90 transition-colors"
                      onclick="togglePlay()"
                    >
                      <i class="w-5 h-5" data-lucide="play" id="playIcon"></i>
                    </button>
                    <span class="text-white text-sm geist-mono"
                      >14:32 / 42:15</span
                    >
                  </div>

                  <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                      <i class="w-4 h-4 text-white" data-lucide="volume-2"></i>
                      <input
                        type="range"
                        class="w-20 h-1 bg-white/20 rounded"
                        value="75"
                      />
                    </div>
                    <button
                      class="text-white hover:text-white/80 transition-colors"
                    >
                      <i class="w-5 h-5" data-lucide="settings"></i>
                    </button>
                    <button
                      class="text-white hover:text-white/80 transition-colors"
                    >
                      <i class="w-5 h-5" data-lucide="maximize"></i>
                    </button>
                  </div>
                </div>
              </div>

              <!-- Speed Indicator -->
              <div
                class="absolute top-6 right-6 bg-black/50 text-white px-3 py-1 rounded-full text-sm geist-mono"
              >
                1.25x
              </div>
            </div>
          </div>
        </div>

        <!-- Chapter Navigation -->
        <div
          class="chapter-navigation bg-white border-t border-[var(--bg-secondary)] p-6"
        >
          <!-- Tabs -->
          <div class="flex items-center space-x-1 mb-6">
            <button
              id="chaptersTab"
              onclick="switchTab('chapters')"
              class="tab-button tab-active flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors"
            >
              <i class="w-4 h-4 mr-2" data-lucide="list"></i>
              Chapters
            </button>
            <button
              id="transcriptsTab"
              onclick="switchTab('transcripts')"
              class="tab-button flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors"
            >
              <i class="w-4 h-4 mr-2" data-lucide="type"></i>
              Transcripts
            </button>
          </div>

          <!-- Chapters View -->
          <div id="chaptersView" class="tab-content">
            <div class="grid grid-cols-1 lg:grid-cols-2 lg:grid-cols-3 gap-3">
              <div
                class="sidebar-item p-3 border border-[var(--bg-secondary)] cursor-pointer"
                onclick="jumpToChapter(0)"
              >
                <div class="text-sm font-medium text-[var(--text-primary)]">
                  Introduction & Overview
                </div>
                <div class="text-xs text-[var(--text-muted)] mt-1">
                  0:00 - 3:24
                </div>
              </div>
              <div
                class="sidebar-item p-3 border border-[var(--bg-secondary)] cursor-pointer"
                onclick="jumpToChapter(1)"
              >
                <div class="text-sm font-medium text-[var(--text-primary)]">
                  Perfect Competition
                </div>
                <div class="text-xs text-[var(--text-muted)] mt-1">
                  3:25 - 12:18
                </div>
              </div>
              <div
                class="sidebar-item chapter-current p-3 border cursor-pointer"
                onclick="jumpToChapter(2)"
              >
                <div class="text-sm font-medium text-[var(--text-primary)]">
                  Monopolistic Markets
                </div>
                <div class="text-xs text-[var(--text-muted)] mt-1">
                  12:19 - 18:45
                </div>
              </div>
              <div
                class="sidebar-item p-3 border border-[var(--bg-secondary)] cursor-pointer"
                onclick="jumpToChapter(3)"
              >
                <div class="text-sm font-medium text-[var(--text-primary)]">
                  Oligopoly Structures
                </div>
                <div class="text-xs text-[var(--text-muted)] mt-1">
                  18:46 - 28:30
                </div>
              </div>
              <div
                class="sidebar-item p-3 border border-[var(--bg-secondary)] cursor-pointer"
                onclick="jumpToChapter(4)"
              >
                <div class="text-sm font-medium text-[var(--text-primary)]">
                  Real-World Examples
                </div>
                <div class="text-xs text-[var(--text-muted)] mt-1">
                  28:31 - 35:12
                </div>
              </div>
              <div
                class="sidebar-item p-3 border border-[var(--bg-secondary)] cursor-pointer"
                onclick="jumpToChapter(5)"
              >
                <div class="text-sm font-medium text-[var(--text-primary)]">
                  Summary & Next Steps
                </div>
                <div class="text-xs text-[var(--text-muted)] mt-1">
                  35:13 - 42:15
                </div>
              </div>
            </div>
          </div>

          <!-- Transcripts View -->
          <div id="transcriptsView" class="tab-content hidden">
            <div class="space-y-4 max-h-96 overflow-y-auto">
              <div
                class="transcript-item border-l-2 border-[var(--accent-primary)] pl-4"
              >
                <div class="flex items-center space-x-2 mb-2">
                  <span
                    class="text-xs font-mono bg-[var(--bg-secondary)] px-2 py-1 rounded"
                    >01:28</span
                  >
                  <h4 class="text-sm font-medium text-[var(--text-primary)]">
                    Introduction & Overview
                  </h4>
                </div>
                <p class="text-sm text-[var(--text-secondary)] leading-relaxed">
                  Welcome to today's lecture on market structures and
                  competition. We'll explore how different market types affect
                  pricing, consumer choice, and economic efficiency. This builds
                  directly on our previous discussion of supply and demand
                  fundamentals.
                </p>
              </div>

              <div class="transcript-item border-l-2 border-gray-200 pl-4">
                <div class="flex items-center space-x-2 mb-2">
                  <span
                    class="text-xs font-mono bg-[var(--bg-secondary)] px-2 py-1 rounded"
                    >03:45</span
                  >
                  <h4 class="text-sm font-medium text-[var(--text-primary)]">
                    Perfect Competition
                  </h4>
                </div>
                <p class="text-sm text-[var(--text-secondary)] leading-relaxed">
                  Perfect competition represents an idealized market structure
                  where many small firms compete with identical products. Key
                  characteristics include: price-taking behavior, free entry and
                  exit, perfect information, and homogeneous products. Let's
                  examine how these conditions create market efficiency.
                </p>
              </div>

              <div
                class="transcript-item border-l-2 border-[var(--accent-primary)] pl-4 bg-[var(--bg-primary)] p-3 rounded-r-lg"
              >
                <div class="flex items-center space-x-2 mb-2">
                  <span
                    class="text-xs font-mono bg-[var(--accent-primary)] text-white px-2 py-1 rounded"
                    >14:32</span
                  >
                  <h4 class="text-sm font-medium text-[var(--text-primary)]">
                    Monopolistic Markets
                  </h4>
                  <span
                    class="text-xs bg-[var(--accent-primary)] text-white px-2 py-1 rounded-full"
                    >Currently Playing</span
                  >
                </div>
                <p class="text-sm text-[var(--text-secondary)] leading-relaxed">
                  In contrast to perfect competition, monopolistic markets
                  feature a single seller with significant market power. The
                  monopolist can influence price by controlling quantity. We see
                  this in utilities, patented technologies, and markets with
                  high barriers to entry. Consider how this affects consumer
                  welfare...
                </p>
              </div>

              <div class="transcript-item border-l-2 border-gray-200 pl-4">
                <div class="flex items-center space-x-2 mb-2">
                  <span
                    class="text-xs font-mono bg-[var(--bg-secondary)] px-2 py-1 rounded"
                    >19:15</span
                  >
                  <h4 class="text-sm font-medium text-[var(--text-primary)]">
                    Oligopoly Structures
                  </h4>
                </div>
                <p class="text-sm text-[var(--text-secondary)] leading-relaxed">
                  Oligopolies occupy the middle ground between perfect
                  competition and monopoly. A few large firms dominate the
                  market, leading to strategic interdependence. Think about
                  airlines, telecommunications, or automotive industries. Game
                  theory becomes crucial for understanding firm behavior.
                </p>
              </div>

              <div class="transcript-item border-l-2 border-gray-200 pl-4">
                <div class="flex items-center space-x-2 mb-2">
                  <span
                    class="text-xs font-mono bg-[var(--bg-secondary)] px-2 py-1 rounded"
                    >29:00</span
                  >
                  <h4 class="text-sm font-medium text-[var(--text-primary)]">
                    Real-World Examples
                  </h4>
                </div>
                <p class="text-sm text-[var(--text-secondary)] leading-relaxed">
                  Let's apply these concepts to real markets. Amazon's
                  marketplace shows monopolistic competition with many sellers
                  offering differentiated products. Meanwhile, Google's search
                  dominance exemplifies a near-monopoly. The smartphone industry
                  demonstrates oligopolistic behavior between Apple and Samsung.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- AI Learning Companion -->
      <aside
        class="ai-panel w-96 bg-[var(--bg-secondary)] border-l border-[var(--accent-tertiary)]/50 flex flex-col transition-all duration-500"
      >
        <!-- AI Header -->
        <div
          class="bg-[var(--accent-primary)] text-white p-6 relative overflow-hidden"
        >
          <div class="absolute inset-0 minimal-artwork-bg opacity-20"></div>
          <div class="relative z-10">
            <div class="flex items-center space-x-3 mb-4">
              <div
                class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center"
              >
                <i class="w-5 h-5 text-white" data-lucide="brain"></i>
              </div>
              <div>
                <h3 class="font-semibold">Learning Companion</h3>
                <p class="text-sm text-white/80">Following along with you</p>
              </div>
            </div>
            <div class="bg-white/20 rounded-lg p-3">
              <div class="text-sm text-white/90 mb-1">Current Focus</div>
              <div class="text-xs text-white/70">
                Market Competition - Monopolistic Structures
              </div>
            </div>
          </div>
        </div>

        <!-- AI Content -->
        <div class="flex-1 overflow-y-auto p-6 space-y-4">
          <!-- Context Card -->
          <div
            class="bg-white rounded-xl p-4 border border-gray-100 minimal-artwork-subtle"
          >
            <div class="flex items-start space-x-3">
              <div
                class="w-8 h-8 bg-[var(--accent-primary)] rounded-full flex items-center justify-center flex-shrink-0"
              >
                <i class="w-4 h-4 text-white" data-lucide="compass"></i>
              </div>
              <div>
                <h4 class="font-medium text-[var(--text-primary)] mb-1">
                  Context Connection
                </h4>
                <p class="text-sm text-[var(--text-muted)]">
                  This builds on supply & demand concepts from Week 2. Notice
                  how market structure affects pricing power.
                </p>
              </div>
            </div>
          </div>

          <!-- Previous Question -->
          <div class="space-y-3">
            <div class="bg-[var(--bg-primary)] rounded-lg p-3 ml-8">
              <p class="text-sm text-[var(--text-secondary)]">
                "Can you give me an example of a monopolistic market?"
              </p>
            </div>
            <div
              class="bg-white rounded-lg p-3 border border-[var(--accent-primary)]/20"
            >
              <div class="flex items-start space-x-2">
                <div
                  class="w-6 h-6 bg-[var(--accent-primary)] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5"
                >
                  <i class="w-3 h-3 text-white" data-lucide="sparkles"></i>
                </div>
                <div>
                  <p class="text-sm text-[var(--text-primary)]">
                    Great question! Local utility companies are perfect
                    examples. The professor will cover this exact scenario in
                    about 3 minutes.
                  </p>
                  <button
                    class="mt-2 text-xs bg-[var(--accent-primary)] text-white px-2 py-1 rounded-full hover:bg-[var(--highlight)] transition-colors"
                    onclick="jumpToTime('16:45')"
                  >
                    Jump to example →
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Upcoming -->
          <div class="bg-[var(--accent-tertiary)]/30 rounded-xl p-4">
            <div class="flex items-center space-x-2 mb-2">
              <i
                class="w-4 h-4 text-[var(--highlight)]"
                data-lucide="clock"
              ></i>
              <h4 class="font-medium text-[var(--text-primary)]">Coming Up</h4>
            </div>
            <p class="text-sm text-[var(--text-muted)]">
              In 5 minutes: Real-world monopoly examples<br />
              In 8 minutes: Practice problem walkthrough
            </p>
          </div>
        </div>

        <!-- AI Input -->
        <div class="p-6 border-t border-[var(--accent-tertiary)]/50 bg-white">
          <div class="relative">
            <textarea
              class="w-full bg-[var(--bg-primary)] border border-[var(--bg-secondary)] rounded-lg p-3 pr-12 text-sm resize-none focus:outline-none focus:bg-white focus:border-[var(--accent-primary)]/50 focus:ring-4 focus:ring-[var(--accent-primary)]/10 transition-all duration-300"
              rows="3"
              placeholder="Ask about this section, request examples, or jump to topics..."
            ></textarea>
            <button
              class="absolute bottom-3 right-3 w-8 h-8 bg-[var(--accent-primary)] rounded-full flex items-center justify-center text-white hover:bg-[var(--highlight)] transition-colors"
            >
              <i class="w-4 h-4" data-lucide="send"></i>
            </button>
          </div>

          <!-- Quick Actions -->
          <div class="grid grid-cols-2 gap-2 mt-3">
            <button
              class="sidebar-item p-2 text-xs text-center border border-[var(--bg-secondary)] hover:bg-[var(--bg-secondary)] transition-colors"
            >
              <i
                class="w-4 h-4 mx-auto mb-1 text-[var(--accent-primary)]"
                data-lucide="bookmark"
              ></i>
              Make Flashcards
            </button>
            <button
              class="sidebar-item p-2 text-xs text-center border border-[var(--bg-secondary)] hover:bg-[var(--bg-secondary)] transition-colors"
            >
              <i
                class="w-4 h-4 mx-auto mb-1 text-[var(--accent-primary)]"
                data-lucide="help-circle"
              ></i>
              Quiz Me
            </button>
          </div>
        </div>
      </aside>
    </div>

    <script>
      lucide.createIcons();

      let isPlaying = false;
      let isFocusMode = false;

      // Enhanced staggered animations
      const staggeredItems = document.querySelectorAll(".stagger-entrance");
      staggeredItems.forEach((item) => {
        const children = Array.from(item.children);
        children.forEach((child, index) => {
          child.style.animationDelay = `${index * 0.075}s`;
        });
      });

      function togglePlay() {
        const playIcon = document.getElementById("playIcon");
        isPlaying = !isPlaying;

        if (isPlaying) {
          playIcon.setAttribute("data-lucide", "pause");
        } else {
          playIcon.setAttribute("data-lucide", "play");
        }
        lucide.createIcons();
      }

      function toggleFocus() {
        const container = document.querySelector(".flex");
        const sidebar = document.querySelector(".ai-panel");
        const chapterNav = document.querySelector(".chapter-navigation");
        const toggleBtn = document.getElementById("focusToggle");

        isFocusMode = !isFocusMode;

        if (isFocusMode) {
          container.classList.add("focus-mode");

          // Hide sidebar with transition
          sidebar.style.transition = "opacity 0.3s ease";
          sidebar.style.opacity = "0";

          // Hide chapter navigation with transition
          chapterNav.style.transition = "opacity 0.3s ease";
          chapterNav.style.opacity = "0";

          setTimeout(() => {
            sidebar.style.display = "none";
            chapterNav.style.display = "none";
          }, 300);

          toggleBtn.innerHTML =
            '<i class="w-4 h-4" data-lucide="minimize"></i>';
        } else {
          // Show sidebar
          sidebar.style.display = "flex";
          sidebar.style.opacity = "0";

          // Show chapter navigation
          chapterNav.style.display = "block";
          chapterNav.style.opacity = "0";

          setTimeout(() => {
            sidebar.style.opacity = "1";
            chapterNav.style.opacity = "1";
            container.classList.remove("focus-mode");
          }, 50);

          toggleBtn.innerHTML = '<i class="w-4 h-4" data-lucide="focus"></i>';
        }
        lucide.createIcons();
      }

      function seekVideo(event) {
        const progressBar = event.currentTarget;
        const rect = progressBar.getBoundingClientRect();
        const percent = (event.clientX - rect.left) / rect.width;

        document.getElementById("progressFill").style.width =
          percent * 100 + "%";
      }

      function jumpToChapter(chapterIndex) {
        // Remove current from all chapters
        document.querySelectorAll(".chapter-current").forEach((el) => {
          el.classList.remove("chapter-current");
        });

        // Add current to selected chapter
        document
          .querySelectorAll(".sidebar-item")
          [chapterIndex].classList.add("chapter-current");

        // Update progress based on chapter
        const progressPercents = [0, 20, 34, 45, 70, 85];
        document.getElementById("progressFill").style.width =
          progressPercents[chapterIndex] + "%";
      }

      function jumpToTime(time) {
        alert(`Jumping to ${time}...`);
        // Update progress bar to match time
        document.getElementById("progressFill").style.width = "40%";
      }

      function hideInsight() {
        document.getElementById("aiInsight").classList.add("hidden");
      }

      function goBack() {
        window.location.href = "course.html";
      }

      function switchTab(tabName) {
        // Remove active class from all tabs
        document.querySelectorAll(".tab-button").forEach((tab) => {
          tab.classList.remove("tab-active");
        });

        // Hide all tab content
        document.querySelectorAll(".tab-content").forEach((content) => {
          content.classList.add("hidden");
        });

        // Show selected tab content and activate tab
        if (tabName === "chapters") {
          document.getElementById("chaptersTab").classList.add("tab-active");
          document.getElementById("chaptersView").classList.remove("hidden");
        } else if (tabName === "transcripts") {
          document.getElementById("transcriptsTab").classList.add("tab-active");
          document.getElementById("transcriptsView").classList.remove("hidden");
        }
      }

      // Keyboard shortcuts
      document.addEventListener("keydown", function (e) {
        if (e.key === " ") {
          e.preventDefault();
          togglePlay();
        } else if (e.key === "f" && !e.ctrlKey) {
          e.preventDefault();
          toggleFocus();
        }
      });

      // Auto-hide AI insight after 8 seconds
      setTimeout(() => {
        hideInsight();
      }, 8000);

      // Enhanced click handlers with feedback
      const interactiveElements = document.querySelectorAll(
        'button, [tabindex="0"]'
      );
      interactiveElements.forEach((element) => {
        element.addEventListener("click", function (e) {
          this.style.transform = "scale(0.98)";
          setTimeout(() => {
            this.style.transform = "";
          }, 150);
        });

        element.addEventListener("keydown", function (e) {
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            this.click();
          }
        });
      });
    </script>
  </body>
</html>
