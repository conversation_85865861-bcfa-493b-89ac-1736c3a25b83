<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>StudyMind - Audio Lecture</title>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Geist:wght@100..900&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Geist+Mono:wght@100..900&family=Geist:wght@100..900&display=swap"
      rel="stylesheet"
    />

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>
    <style type="text/tailwindcss">
      :root {
        --bg-primary: #fdfbf6;
        --bg-secondary: #f0e5d8;
        --text-primary: #2d3748;
        --text-secondary: #4a5568;
        --text-muted: #718096;
        --text-subtle: #a0aec0;
        --text-on-tinted: #2d3748;
        --text-on-tinted-muted: #4a5568;
        --text-on-tinted-subtle: #718096;
        --accent-primary: #a9c2b9;
        --accent-secondary: #d3b8ae;
        --accent-tertiary: #e6cba5;
        --highlight: #8b9d8f;
        --success: #68d391;
        --warning: #f6ad55;
        --info: #63b3ed;
      }

      body {
        font-family: "Geist", sans-serif;
        background-color: var(--bg-primary);
        color: var(--text-primary);
      }
      .geist-mono {
        font-family: "Geist Mono", monospace;
      }

      .animated-gradient {
        background: linear-gradient(
          -45deg,
          var(--accent-tertiary),
          var(--bg-secondary),
          var(--accent-primary),
          var(--highlight)
        );
        background-size: 400% 400%;
        animation: gradientBG 20s ease infinite;
      }
      @keyframes gradientBG {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }

      .stagger-entrance > * {
        opacity: 0;
        transform: translateY(20px);
        animation: fadeInUp 0.6s forwards;
      }
      @keyframes fadeInUp {
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .hover-lift {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .hover-lift:hover {
        transform: translateY(-2px) scale(1.01);
        box-shadow: 0 8px 16px -4px rgba(45, 55, 72, 0.12),
          0 4px 6px -2px rgba(45, 55, 72, 0.08);
      }

      .sidebar-card-item:hover {
        background-color: var(--bg-secondary);
        transform: translateX(2px);
      }

      .minimal-artwork-bg {
        background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23e6cba5' fill-opacity='0.1'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-repeat: repeat;
        background-size: 200px 200px;
      }
      .minimal-artwork-subtle {
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23a9c2b9' fill-opacity='0.07'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-repeat: repeat;
      }

      /* Enhanced button styles */
      .btn-primary {
        background: var(--accent-primary);
        color: white;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        border-radius: 0.75rem;
        transition: all 0.2s ease;
        border: none;
        cursor: pointer;
      }

      .btn-primary:hover {
        background: var(--highlight);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(169, 194, 185, 0.3);
      }

      /* Better focus states */
      .focus-ring:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(169, 194, 185, 0.3);
      }

      /* Audio specific styles */
      .waveform {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        gap: 2px;
      }

      .wave-bar {
        width: 3px;
        background: var(--accent-tertiary);
        border-radius: 2px;
        transition: all 0.3s ease;
        min-height: 4px;
      }

      .wave-bar.active {
        background: var(--accent-primary);
        transform: scaleY(1.3);
      }

      .wave-bar.played {
        background: var(--highlight);
      }

      .control-btn {
        background: var(--bg-secondary);
        border: none;
        border-radius: 50%;
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: var(--text-primary);
        transition: all 0.2s ease;
      }

      .control-btn:hover {
        background: var(--accent-tertiary);
        transform: scale(1.05);
      }

      .play-btn {
        background: var(--accent-primary);
        color: white;
        width: 56px;
        height: 56px;
      }

      .play-btn:hover {
        background: var(--highlight);
      }

      .speed-btn {
        background: var(--bg-secondary);
        border: none;
        color: var(--text-primary);
        padding: 0.5rem 0.75rem;
        border-radius: 0.75rem;
        cursor: pointer;
        font-size: 0.875rem;
        transition: all 0.2s ease;
      }

      .speed-btn:hover {
        background: var(--accent-tertiary);
      }

      .speed-btn.active {
        background: var(--accent-primary);
        color: white;
      }

      .transcript-line {
        transition: all 0.3s ease;
        cursor: pointer;
        border-radius: 0.75rem;
        padding: 1rem;
        margin-bottom: 0.75rem;
        border-left: 3px solid transparent;
      }

      .transcript-line:hover {
        background: var(--bg-secondary) / 50;
        border-left-color: var(--accent-tertiary);
      }

      .transcript-line.current {
        background: rgba(169, 194, 185, 0.15);
        border-left-color: var(--accent-primary);
        box-shadow: 0 2px 8px rgba(169, 194, 185, 0.2);
      }

      .transcript-line.past {
        opacity: 0.7;
      }

      .complexity-indicator {
        background: rgba(169, 194, 185, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 1rem;
        padding: 0.5rem 1rem;
        color: var(--text-primary);
      }

      .complexity-dot {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: var(--success);
      }

      .complexity-dot.medium {
        background: var(--warning);
      }

      .complexity-dot.high {
        background: #ef4444;
      }

      .live-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #ef4444;
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
      }

      .breathing-animation {
        animation: breathe 4s ease-in-out infinite;
      }

      @keyframes breathe {
        0%,
        100% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.05);
        }
      }

      /* Progress bar styles */
      .progress-container {
        background: var(--bg-secondary);
        border-radius: 1rem;
        height: 8px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
      }

      .progress-fill {
        background: var(--accent-primary);
        height: 100%;
        border-radius: 1rem;
        transition: width 0.3s ease;
        position: relative;
      }

      .progress-handle {
        position: absolute;
        right: -6px;
        top: -4px;
        width: 16px;
        height: 16px;
        background: white;
        border: 2px solid var(--accent-primary);
        border-radius: 50%;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      /* Transcript search */
      .transcript-search {
        position: sticky;
        top: 0;
        background: white;
        z-index: 10;
        border-bottom: 1px solid var(--bg-secondary);
      }

      /* Chapter navigation */
      .chapter-item {
        padding: 0.75rem 1rem;
        border-radius: 0.75rem;
        cursor: pointer;
        transition: all 0.2s ease;
        border-left: 3px solid transparent;
        margin-bottom: 0.5rem;
      }

      .chapter-item:hover {
        background: var(--bg-secondary);
        border-left-color: var(--accent-tertiary);
      }

      .chapter-item.current {
        background: var(--accent-primary) / 20;
        border-left-color: var(--accent-primary);
      }

      @media (max-width: 1024px) {
        .ai-panel {
          width: 300px;
        }
      }

      @media (max-width: 768px) {
        .lecture-container {
          flex-direction: column;
        }
        .audio-controls-panel {
          width: 100%;
          height: auto;
        }
        .transcript-panel {
          height: 50vh;
        }
        .ai-panel {
          width: 100%;
          height: 30vh;
        }
      }
    </style>
  </head>
  <body class="text-[var(--text-primary)]">
    <div class="flex h-screen">
      <!-- Audio Controls Panel -->
      <div
        class="audio-controls-panel w-80 bg-white border-r border-[var(--bg-secondary)] flex flex-col"
      >
        <!-- Header -->
        <header class="p-6 border-b border-[var(--bg-secondary)]">
          <div class="flex items-center space-x-3 mb-4">
            <button
              onclick="goBack()"
              class="w-10 h-10 bg-[var(--bg-secondary)] rounded-full flex items-center justify-center hover:bg-[var(--accent-tertiary)]/50 transition-colors focus-ring"
            >
              <i
                class="w-5 h-5 text-[var(--text-primary)]"
                data-lucide="arrow-left"
              ></i>
            </button>
            <div class="text-2xl breathing-animation">🎧</div>
            <div>
              <h1 class="text-lg font-semibold text-[var(--text-primary)]">
                Audio Lecture
              </h1>
              <p class="text-sm text-[var(--text-muted)]">Mathematics 201</p>
            </div>
          </div>

          <div class="space-y-2">
            <h2 class="font-medium text-[var(--text-primary)]">
              Advanced Calculus: Integration Techniques
            </h2>
            <div
              class="flex items-center space-x-4 text-sm text-[var(--text-muted)]"
            >
              <span class="flex items-center">
                <i class="w-4 h-4 mr-1" data-lucide="user"></i>
                Dr. Sarah Chen
              </span>
              <span class="flex items-center">
                <i class="w-4 h-4 mr-1" data-lucide="clock"></i>
                62 min
              </span>
            </div>
          </div>
        </header>

        <!-- Current Status -->
        <div class="p-6 border-b border-[var(--bg-secondary)]">
          <div class="animated-gradient p-4 rounded-xl text-white mb-4 hidden">
            <div class="flex items-center justify-between mb-2">
              <div>
                <h3 class="font-semibold">Currently Playing</h3>
                <p class="text-sm text-white/80">
                  Integration by Parts Formula
                </p>
              </div>
              <div
                class="complexity-indicator bg-white/20 text-white text-xs px-2 py-1 rounded-full"
              >
                Advanced
              </div>
            </div>
          </div>

          <!-- Progress Bar -->
          <div class="mb-4">
            <div
              class="flex justify-between text-sm text-[var(--text-muted)] mb-2"
            >
              <span class="geist-mono">23:18</span>
              <span class="geist-mono">62:00</span>
            </div>
            <div class="progress-container" onclick="seekAudio(event)">
              <div class="progress-fill" style="width: 37.6%" id="progressBar">
                <div class="progress-handle"></div>
              </div>
            </div>
          </div>

          <!-- Mini Waveform -->
          <div class="bg-[var(--bg-secondary)] rounded-lg p-3 mb-4 hidden">
            <div class="waveform h-8" id="miniWaveform">
              <!-- Mini waveform bars -->
            </div>
          </div>
        </div>

        <!-- Playback Controls -->
        <div class="p-6 border-b border-[var(--bg-secondary)]">
          <!-- Main Controls -->
          <div class="flex items-center justify-center space-x-3 mb-4">
            <button class="control-btn" onclick="skipBackward()">
              <i class="w-4 h-4" data-lucide="skip-back"></i>
            </button>
            <button class="control-btn" onclick="rewind()">
              <i class="w-4 h-4" data-lucide="rewind"></i>
            </button>
            <button
              class="control-btn play-btn"
              onclick="togglePlay()"
              id="playButton"
            >
              <i class="w-5 h-5" data-lucide="pause" id="playIcon"></i>
            </button>
            <button class="control-btn" onclick="fastForward()">
              <i class="w-4 h-4" data-lucide="fast-forward"></i>
            </button>
            <button class="control-btn" onclick="skipForward()">
              <i class="w-4 h-4" data-lucide="skip-forward"></i>
            </button>
          </div>

          <!-- Speed & Settings -->
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <span class="text-sm font-medium text-[var(--text-muted)]"
                >Speed:</span
              >
              <div class="flex space-x-1">
                <button class="speed-btn" onclick="setSpeed(0.75)">
                  0.75x
                </button>
                <button class="speed-btn active" onclick="setSpeed(1)">
                  1x
                </button>
                <button class="speed-btn" onclick="setSpeed(1.25)">
                  1.25x
                </button>
                <button class="speed-btn" onclick="setSpeed(1.5)">1.5x</button>
              </div>
            </div>
          </div>
        </div>

        <!-- Chapter Navigation -->
        <div class="flex-1 overflow-y-auto p-6">
          <h3
            class="font-semibold text-[var(--text-primary)] mb-4 flex items-center"
          >
            <i
              class="w-5 h-5 mr-2 text-[var(--accent-primary)]"
              data-lucide="list"
            ></i>
            Chapters
          </h3>

          <div class="space-y-2">
            <div class="chapter-item" onclick="jumpToChapter(0)">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-sm font-medium text-[var(--text-primary)]">
                    Introduction
                  </div>
                  <div class="text-xs text-[var(--text-muted)]">
                    0:00 - 3:24
                  </div>
                </div>
                <i
                  class="w-4 h-4 text-[var(--text-muted)]"
                  data-lucide="play"
                ></i>
              </div>
            </div>

            <div class="chapter-item" onclick="jumpToChapter(1)">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-sm font-medium text-[var(--text-primary)]">
                    Basic Integration Review
                  </div>
                  <div class="text-xs text-[var(--text-muted)]">
                    3:25 - 12:18
                  </div>
                </div>
                <i
                  class="w-4 h-4 text-[var(--text-muted)]"
                  data-lucide="play"
                ></i>
              </div>
            </div>

            <div class="chapter-item current" onclick="jumpToChapter(2)">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-sm font-medium text-[var(--text-primary)]">
                    Integration by Parts
                  </div>
                  <div class="text-xs text-[var(--text-muted)]">
                    12:19 - 28:45
                  </div>
                </div>
                <i
                  class="w-4 h-4 text-[var(--accent-primary)]"
                  data-lucide="pause"
                ></i>
              </div>
            </div>

            <div class="chapter-item" onclick="jumpToChapter(3)">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-sm font-medium text-[var(--text-primary)]">
                    Worked Examples
                  </div>
                  <div class="text-xs text-[var(--text-muted)]">
                    28:46 - 45:30
                  </div>
                </div>
                <i
                  class="w-4 h-4 text-[var(--text-muted)]"
                  data-lucide="play"
                ></i>
              </div>
            </div>

            <div class="chapter-item" onclick="jumpToChapter(4)">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-sm font-medium text-[var(--text-primary)]">
                    Practice Problems
                  </div>
                  <div class="text-xs text-[var(--text-muted)]">
                    45:31 - 62:00
                  </div>
                </div>
                <i
                  class="w-4 h-4 text-[var(--text-muted)]"
                  data-lucide="play"
                ></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Live Transcript Panel (Center - Primary Focus) -->
      <div class="transcript-panel flex-1 bg-white flex flex-col">
        <!-- Transcript Header with Search -->
        <div class="transcript-search p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-2">
              <div class="live-indicator"></div>
              <h2 class="text-xl font-semibold text-[var(--text-primary)]">
                Live Transcript
              </h2>
              <span class="text-sm text-[var(--text-muted)]"
                >Following along automatically</span
              >
            </div>
            <div class="flex items-center space-x-2">
              <button class="control-btn" title="Download transcript">
                <i class="w-4 h-4" data-lucide="download"></i>
              </button>
              <button class="control-btn" title="Settings">
                <i class="w-4 h-4" data-lucide="settings"></i>
              </button>
            </div>
          </div>

          <!-- Search Bar -->
          <div class="relative">
            <input
              type="text"
              placeholder="Search transcript..."
              class="w-full bg-[var(--bg-primary)] border border-[var(--bg-secondary)] rounded-lg pl-10 pr-4 py-3 text-sm focus:outline-none focus:border-[var(--accent-primary)] focus:ring-2 focus:ring-[var(--accent-primary)]/20 transition-all"
              id="transcriptSearch"
            />
            <i
              class="w-4 h-4 text-[var(--text-muted)] absolute left-3 top-1/2 transform -translate-y-1/2"
              data-lucide="search"
            ></i>
          </div>
        </div>

        <!-- Transcript Content -->
        <div class="flex-1 overflow-y-auto p-6 pt-0" id="transcriptContainer">
          <div class="space-y-4">
            <!-- Past transcript lines -->
            <div class="transcript-line past" onclick="jumpToTime('20:45')">
              <div class="flex items-start space-x-4">
                <span
                  class="text-xs geist-mono bg-[var(--bg-secondary)] px-2 py-1 rounded text-[var(--text-muted)] flex-shrink-0 mt-1"
                  >20:45</span
                >
                <div>
                  <p class="text-[var(--text-secondary)] leading-relaxed">
                    Before we dive into integration by parts, let's quickly
                    review the basic integration rules we've covered. Remember
                    that integration is essentially the reverse of
                    differentiation.
                  </p>
                </div>
              </div>
            </div>

            <div class="transcript-line past" onclick="jumpToTime('21:30')">
              <div class="flex items-start space-x-4">
                <span
                  class="text-xs geist-mono bg-[var(--bg-secondary)] px-2 py-1 rounded text-[var(--text-muted)] flex-shrink-0 mt-1"
                  >21:30</span
                >
                <div>
                  <p class="text-[var(--text-secondary)] leading-relaxed">
                    The power rule, substitution method, and basic trigonometric
                    integrals should all be fresh in your memory from our
                    previous sessions.
                  </p>
                </div>
              </div>
            </div>

            <div class="transcript-line past" onclick="jumpToTime('22:15')">
              <div class="flex items-start space-x-4">
                <span
                  class="text-xs geist-mono bg-[var(--bg-secondary)] px-2 py-1 rounded text-[var(--text-muted)] flex-shrink-0 mt-1"
                  >22:15</span
                >
                <div>
                  <p class="text-[var(--text-secondary)] leading-relaxed">
                    Now, integration by parts is particularly useful when we
                    have a product of two functions that don't simplify easily
                    with basic methods.
                  </p>
                </div>
              </div>
            </div>

            <!-- Current line -->
            <div class="transcript-line current" onclick="jumpToTime('23:18')">
              <div class="flex items-start space-x-4">
                <span
                  class="text-xs geist-mono bg-[var(--accent-primary)] text-white px-2 py-1 rounded flex-shrink-0 mt-1"
                  >23:18</span
                >
                <div>
                  <p
                    class="text-[var(--text-primary)] leading-relaxed font-medium"
                  >
                    The integration by parts formula is: integral of u dv equals
                    uv minus integral of v du. This comes directly from the
                    product rule for differentiation.
                  </p>
                  <div
                    class="mt-2 flex items-center space-x-2 text-xs text-[var(--text-muted)]"
                  >
                    <span class="flex items-center">
                      <i class="w-3 h-3 mr-1" data-lucide="volume-2"></i>
                      Currently speaking
                    </span>
                    <span>•</span>
                    <span>Key concept</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Upcoming lines (placeholder) -->
            <div
              class="transcript-line opacity-50"
              onclick="jumpToTime('24:05')"
            >
              <div class="flex items-start space-x-4">
                <span
                  class="text-xs geist-mono bg-[var(--bg-secondary)] px-2 py-1 rounded text-[var(--text-muted)] flex-shrink-0 mt-1"
                  >24:05</span
                >
                <div>
                  <p class="text-[var(--text-muted)] leading-relaxed italic">
                    The key insight is choosing the right u and dv. We'll use
                    the LIATE rule to guide our selection...
                  </p>
                </div>
              </div>
            </div>

            <div
              class="transcript-line opacity-50"
              onclick="jumpToTime('25:20')"
            >
              <div class="flex items-start space-x-4">
                <span
                  class="text-xs geist-mono bg-[var(--bg-secondary)] px-2 py-1 rounded text-[var(--text-muted)] flex-shrink-0 mt-1"
                  >25:20</span
                >
                <div>
                  <p class="text-[var(--text-muted)] leading-relaxed italic">
                    LIATE stands for Logarithmic, Inverse trigonometric,
                    Algebraic, Trigonometric, and Exponential functions...
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- AI Learning Companion -->
      <aside
        class="ai-panel w-96 bg-[var(--bg-secondary)] border-l border-[var(--accent-tertiary)]/50 flex flex-col"
      >
        <!-- AI Header -->
        <div
          class="bg-[var(--accent-primary)] text-white p-6 relative overflow-hidden"
        >
          <div class="absolute inset-0 minimal-artwork-bg opacity-20"></div>
          <div class="relative z-10">
            <div class="flex items-center space-x-3 mb-4">
              <div
                class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center"
              >
                <i class="w-5 h-5 text-white" data-lucide="brain"></i>
              </div>
              <div>
                <h3 class="font-semibold">Learning Companion</h3>
                <p class="text-sm text-white/80">Following your progress</p>
              </div>
            </div>
            <div class="bg-white/20 rounded-lg p-3">
              <div class="text-sm text-white/90 mb-1">Currently Learning</div>
              <div class="text-xs text-white/70">
                Integration by Parts Formula
              </div>
            </div>
          </div>
        </div>

        <!-- AI Content -->
        <div class="flex-1 overflow-y-auto p-6 space-y-4">
          <!-- Context Card -->
          <div
            class="bg-white rounded-xl p-4 border border-gray-100 minimal-artwork-subtle hover-lift"
          >
            <div class="flex items-start space-x-3">
              <div
                class="w-8 h-8 bg-[var(--accent-primary)] rounded-full flex items-center justify-center flex-shrink-0"
              >
                <i class="w-4 h-4 text-white" data-lucide="lightbulb"></i>
              </div>
              <div>
                <h4 class="font-medium text-[var(--text-primary)] mb-1">
                  Key Insight
                </h4>
                <p class="text-sm text-[var(--text-muted)]">
                  Integration by parts is the reverse of the product rule. When
                  you see a product that doesn't fit basic patterns, think
                  LIATE!
                </p>
              </div>
            </div>
          </div>

          <!-- Memory Connection -->
          <div class="bg-[var(--accent-tertiary)]/30 rounded-xl p-4">
            <div class="flex items-center space-x-2 mb-2">
              <i class="w-4 h-4 text-[var(--highlight)]" data-lucide="link"></i>
              <h4 class="font-medium text-[var(--text-primary)]">
                Connects To
              </h4>
            </div>
            <p class="text-sm text-[var(--text-muted)] mb-2">
              Week 2: Product Rule for Derivatives
            </p>
            <button
              class="text-xs bg-[var(--accent-primary)] text-white px-3 py-1 rounded-full hover:bg-[var(--highlight)] transition-colors"
            >
              Review connection →
            </button>
          </div>

          <!-- Note Taking -->
          <div class="bg-white rounded-xl p-4 border border-gray-100">
            <div class="flex items-center justify-between mb-3">
              <h4 class="font-medium text-[var(--text-primary)]">
                Quick Notes
              </h4>
              <button
                class="text-[var(--accent-primary)] hover:text-[var(--highlight)] transition-colors"
              >
                <i class="w-4 h-4" data-lucide="plus"></i>
              </button>
            </div>
            <div class="space-y-2">
              <div class="bg-[var(--bg-primary)] rounded-lg p-3 text-sm">
                <div class="flex items-center justify-between mb-1">
                  <span class="text-xs text-[var(--text-muted)] geist-mono"
                    >23:18</span
                  >
                  <button
                    class="text-[var(--text-muted)] hover:text-[var(--text-primary)]"
                  >
                    <i class="w-3 h-3" data-lucide="x"></i>
                  </button>
                </div>
                <p class="text-[var(--text-secondary)]">
                  Integration by parts formula: ∫u dv = uv - ∫v du
                </p>
              </div>
            </div>
            <button
              class="w-full mt-3 text-sm text-[var(--accent-primary)] hover:text-[var(--highlight)] transition-colors"
            >
              Add note at current time
            </button>
          </div>

          <!-- Upcoming Preview -->
          <div class="bg-[var(--accent-secondary)]/30 rounded-xl p-4">
            <div class="flex items-center space-x-2 mb-2">
              <i
                class="w-4 h-4 text-[var(--highlight)]"
                data-lucide="clock"
              ></i>
              <h4 class="font-medium text-[var(--text-primary)]">Coming Up</h4>
            </div>
            <div class="space-y-2 text-sm text-[var(--text-muted)]">
              <div class="flex items-center justify-between">
                <span>LIATE rule explanation</span>
                <span class="text-xs geist-mono">~2 min</span>
              </div>
              <div class="flex items-center justify-between">
                <span>Worked example</span>
                <span class="text-xs geist-mono">~5 min</span>
              </div>
              <div class="flex items-center justify-between">
                <span>Practice problem</span>
                <span class="text-xs geist-mono">~8 min</span>
              </div>
            </div>
          </div>
        </div>

        <!-- AI Input -->
        <div class="p-6 border-t border-[var(--accent-tertiary)]/50 bg-white">
          <div class="relative mb-4">
            <textarea
              class="w-full bg-[var(--bg-primary)] border border-[var(--bg-secondary)] rounded-lg p-3 pr-12 text-sm resize-none focus:outline-none focus:bg-white focus:border-[var(--accent-primary)]/50 focus:ring-4 focus:ring-[var(--accent-primary)]/10 transition-all duration-300"
              rows="3"
              placeholder="Ask questions about this section, request examples, or get clarification..."
            ></textarea>
            <button
              class="absolute bottom-3 right-3 w-8 h-8 bg-[var(--accent-primary)] rounded-full flex items-center justify-center text-white hover:bg-[var(--highlight)] transition-colors"
            >
              <i class="w-4 h-4" data-lucide="send"></i>
            </button>
          </div>

          <!-- Quick Actions -->
          <div class="grid grid-cols-2 gap-2">
            <button
              class="sidebar-card-item p-3 text-xs text-center border border-[var(--bg-secondary)] hover:bg-[var(--bg-secondary)] transition-colors rounded-lg"
            >
              <i
                class="w-4 h-4 mx-auto mb-1 text-[var(--accent-primary)]"
                data-lucide="bookmark"
              ></i>
              Make Flashcards
            </button>
            <button
              class="sidebar-card-item p-3 text-xs text-center border border-[var(--bg-secondary)] hover:bg-[var(--bg-secondary)] transition-colors rounded-lg"
            >
              <i
                class="w-4 h-4 mx-auto mb-1 text-[var(--accent-primary)]"
                data-lucide="help-circle"
              ></i>
              Quick Quiz
            </button>
          </div>
        </div>
      </aside>
    </div>

    <script>
      lucide.createIcons();

      let isPlaying = true;
      let currentSpeed = 1;

      // Generate mini waveform
      function generateMiniWaveform() {
        const waveform = document.getElementById("miniWaveform");
        const bars = 50;

        for (let i = 0; i < bars; i++) {
          const bar = document.createElement("div");
          bar.className = "wave-bar";

          // Random heights for waveform
          const height = Math.random() * 20 + 8;
          bar.style.height = height + "px";

          // Mark some bars as played (before current position)
          if (i < bars * 0.376) {
            bar.classList.add("played");
          }

          // Mark current position
          if (i >= bars * 0.376 && i <= bars * 0.4) {
            bar.classList.add("active");
          }

          waveform.appendChild(bar);
        }
      }

      function togglePlay() {
        const playIcon = document.getElementById("playIcon");
        isPlaying = !isPlaying;

        if (isPlaying) {
          playIcon.setAttribute("data-lucide", "pause");
        } else {
          playIcon.setAttribute("data-lucide", "play");
        }
        lucide.createIcons();
      }

      function setSpeed(speed) {
        currentSpeed = speed;
        document.querySelectorAll(".speed-btn").forEach((btn) => {
          btn.classList.remove("active");
        });
        event.target.classList.add("active");
      }

      function seekAudio(event) {
        const progressContainer = event.currentTarget;
        const rect = progressContainer.getBoundingClientRect();
        const percent = (event.clientX - rect.left) / rect.width;

        document.getElementById("progressBar").style.width =
          percent * 100 + "%";
      }

      function jumpToTime(timestamp) {
        console.log(`Jumping to ${timestamp}...`);
        // Update progress bar and waveform to match timestamp
        // Scroll transcript to that position
        // Update chapter highlighting
      }

      function jumpToChapter(chapterIndex) {
        // Remove current from all chapters
        document.querySelectorAll(".chapter-item").forEach((el) => {
          el.classList.remove("current");
        });

        // Add current to selected chapter
        document
          .querySelectorAll(".chapter-item")
          [chapterIndex].classList.add("current");

        // Update progress and other UI elements
        const progressPercents = [0, 13, 37.6, 67, 86];
        if (progressPercents[chapterIndex] !== undefined) {
          document.getElementById("progressBar").style.width =
            progressPercents[chapterIndex] + "%";
        }
      }

      function skipBackward() {
        console.log("Skip backward 10 seconds");
      }

      function rewind() {
        console.log("Rewind 30 seconds");
      }

      function fastForward() {
        console.log("Fast forward 30 seconds");
      }

      function skipForward() {
        console.log("Skip forward 10 seconds");
      }

      function goBack() {
        window.location.href = "course.html";
      }

      // Enhanced click handlers with feedback
      const interactiveElements = document.querySelectorAll(
        'button, [tabindex="0"]'
      );
      interactiveElements.forEach((element) => {
        element.addEventListener("click", function (e) {
          this.style.transform = "scale(0.98)";
          setTimeout(() => {
            this.style.transform = "";
          }, 150);
        });

        element.addEventListener("keydown", function (e) {
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            this.click();
          }
        });
      });

      // Auto-scroll transcript to keep current line visible
      function scrollToCurrentTranscript() {
        const currentLine = document.querySelector(".transcript-line.current");
        const container = document.getElementById("transcriptContainer");

        if (currentLine && container) {
          const containerRect = container.getBoundingClientRect();
          const lineRect = currentLine.getBoundingClientRect();

          if (
            lineRect.bottom > containerRect.bottom ||
            lineRect.top < containerRect.top
          ) {
            currentLine.scrollIntoView({ behavior: "smooth", block: "center" });
          }
        }
      }

      // Search functionality
      document
        .getElementById("transcriptSearch")
        .addEventListener("input", function (e) {
          const searchTerm = e.target.value.toLowerCase();
          const transcriptLines = document.querySelectorAll(".transcript-line");

          transcriptLines.forEach((line) => {
            const text = line.textContent.toLowerCase();
            if (searchTerm && text.includes(searchTerm)) {
              line.style.backgroundColor = "rgba(169, 194, 185, 0.2)";
            } else {
              line.style.backgroundColor = "";
            }
          });
        });

      // Initialize
      generateMiniWaveform();

      // Simulate real-time waveform updates
      setInterval(() => {
        const bars = document.querySelectorAll(".wave-bar.active");
        bars.forEach((bar) => {
          const height = Math.random() * 24 + 8;
          bar.style.height = height + "px";
        });
      }, 300);

      // Keyboard shortcuts
      document.addEventListener("keydown", function (e) {
        if (e.key === " " && !e.target.matches("input, textarea")) {
          e.preventDefault();
          togglePlay();
        }
      });

      // Auto-scroll transcript every few seconds (simulation)
      setInterval(() => {
        scrollToCurrentTranscript();
      }, 5000);
    </script>
  </body>
</html>
