<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>StudyMind - AI Quiz</title>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Geist:wght@100..900&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Geist+Mono:wght@100..900&family=Geist:wght@100..900&display=swap"
      rel="stylesheet"
    />
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>
    <style type="text/tailwindcss">
      :root {
        --bg-primary: #fdfbf6;
        --bg-secondary: #f0e5d8;
        --text-primary: #2d3748;
        --text-secondary: #4a5568;
        --text-muted: #718096;
        --text-subtle: #a0aec0;
        --accent-primary: #a9c2b9;
        --accent-secondary: #d3b8ae;
        --accent-tertiary: #e6cba5;
        --highlight: #8b9d8f;
        --success: #68d391;
        --warning: #f6ad55;
        --info: #63b3ed;
        --paper-white: #ffffff;
        --paper-shadow: rgba(45, 55, 72, 0.08);
      }

      body {
        font-family: "Geist", sans-serif;
        background-color: var(--bg-primary);
        color: var(--text-primary);
        min-height: 100vh;
      }
      
      .geist-mono {
        font-family: "Geist Mono", monospace;
      }

      .quiz-container {
        display: flex;
        height: 100vh;
        overflow: hidden;
      }

      /* Main Quiz Panel */
      .quiz-panel {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: var(--bg-primary);
        overflow: hidden;
      }

      .quiz-header {
        background: white;
        padding: 16px 32px;
        border-bottom: 1px solid var(--accent-tertiary);
        box-shadow: 0 1px 4px rgba(169, 194, 185, 0.08);
      }

      .quiz-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 12px;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .ai-insight {
        background: rgba(169, 194, 185, 0.1);
        border: 1px solid rgba(169, 194, 185, 0.2);
        border-radius: 8px;
        padding: 4px 8px;
        font-size: 11px;
        color: var(--highlight);
        font-weight: 500;
        margin-left: auto;
      }

      .quiz-meta {
        color: var(--text-muted);
        font-size: 12px;
        display: flex;
        gap: 16px;
        margin-bottom: 12px;
        flex-wrap: wrap;
      }

      .quiz-progress {
        background: var(--bg-secondary);
        border-radius: 6px;
        height: 6px;
        overflow: hidden;
        margin-top: 12px;
      }

      .progress-fill {
        height: 100%;
        background: var(--accent-primary);
        width: 40%;
        transition: width 0.5s cubic-bezier(0.65, 0, 0.35, 1);
        border-radius: 4px;
      }

      .progress-text {
        margin-top: 6px;
        font-size: 12px;
        color: var(--text-muted);
        font-weight: 500;
      }

      /* Quiz Main Area with Navigation */
      .quiz-main-area {
        flex: 1;
        display: flex;
        overflow: hidden;
        margin-left: 0;
      }

      /* Floating Navigation Toolbar */
      .quiz-nav-toolbar {
        position: fixed;
        left: 24px;
        top: 50%;
        transform: translateY(-50%);
        width: 60px;
        background: white;
        border: 1px solid var(--accent-tertiary);
        border-radius: 1rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 16px;
        padding: 16px 0;
        box-shadow: 0 8px 32px rgba(169, 194, 185, 0.15);
        z-index: 50;
        backdrop-filter: blur(8px);
        background: rgba(255, 255, 255, 0.95);
      }

      .nav-btn {
        background: var(--bg-secondary);
        border: 1px solid var(--accent-tertiary);
        color: var(--text-primary);
        width: 40px;
        height: 40px;
        border-radius: 50%;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
      }

      .nav-btn:hover {
        background: var(--accent-tertiary);
        transform: translateX(2px);
        box-shadow: 0 4px 12px rgba(169, 194, 185, 0.2);
      }

      .nav-btn.primary {
        background: var(--accent-primary);
        color: white;
        border-color: var(--accent-primary);
      }

      .nav-btn.primary:hover {
        background: var(--highlight);
        border-color: var(--highlight);
      }

      .nav-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }

      .nav-tooltip {
        position: absolute;
        left: 52px;
        background: var(--text-primary);
        color: white;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 11px;
        white-space: nowrap;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.2s ease;
        z-index: 100;
      }

      .nav-tooltip::before {
        content: '';
        position: absolute;
        left: -4px;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-top: 4px solid transparent;
        border-bottom: 4px solid transparent;
        border-right: 4px solid var(--text-primary);
      }

      .nav-btn:hover .nav-tooltip {
        opacity: 1;
      }

      .quiz-progress-indicator {
        text-align: center;
        margin: 20px 0;
      }

      .current-question {
        font-size: 14px;
        font-weight: 600;
        color: var(--accent-primary);
        margin-bottom: 4px;
      }

      .total-questions {
        font-size: 11px;
        color: var(--text-muted);
      }

      /* Quiz Content */
      .quiz-content {
        flex: 1;
        padding: 32px 32px 32px 120px;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        max-width: 900px;
        margin: 0 auto;
        position: relative;
        min-height: 100%;
        padding-top: 40px;
      }

      .minimal-artwork-subtle {
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23a9c2b9' fill-opacity='0.07'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-repeat: repeat;
      }

      .question-card {
        background: white;
        border-radius: 1.5rem;
        padding: 32px;
        margin-bottom: 24px;
        border: 1px solid rgba(169, 194, 185, 0.15);
        box-shadow: 0 4px 16px rgba(169, 194, 185, 0.1);
        transition: all 0.3s ease;
      }

      .question-card:hover {
        box-shadow: 0 8px 24px rgba(169, 194, 185, 0.15);
      }

      .question-header {
        display: flex;
        align-items: center;
        gap: 16px;
        margin-bottom: 24px;
      }

      .question-number {
        background: var(--accent-primary);
        color: white;
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 18px;
        box-shadow: 0 4px 12px rgba(169, 194, 185, 0.3);
      }

      .question-type {
        background: var(--bg-secondary);
        color: var(--highlight);
        padding: 8px 16px;
        border-radius: 1rem;
        font-size: 12px;
        font-weight: 600;
        border: 1px solid var(--accent-tertiary);
      }

      .question-context {
        background: rgba(169, 194, 185, 0.1);
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 24px;
        border-left: 3px solid var(--accent-primary);
      }

      .context-label {
        font-size: 12px;
        color: var(--accent-primary);
        font-weight: 600;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .context-text {
        font-size: 14px;
        color: var(--text-secondary);
        line-height: 1.5;
      }

      .question-text {
        font-size: 20px;
        line-height: 1.5;
        margin-bottom: 24px;
        font-weight: 500;
        color: var(--text-primary);
      }

      /* Answer Options */
      .answer-options {
        display: flex;
        flex-direction: column;
        gap: 12px;
      }

      .answer-option {
        background: var(--bg-secondary);
        border: 2px solid var(--accent-tertiary);
        border-radius: 12px;
        padding: 16px 20px;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 16px;
      }

      .answer-option:hover {
        border-color: var(--accent-primary);
        background: rgba(169, 194, 185, 0.1);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(169, 194, 185, 0.15);
      }

      .answer-option.selected {
        border-color: var(--accent-primary);
        background: rgba(169, 194, 185, 0.15);
        box-shadow: 0 4px 12px rgba(169, 194, 185, 0.2);
      }

      .answer-option.correct {
        border-color: var(--success);
        background: rgba(104, 211, 145, 0.1);
        box-shadow: 0 4px 12px rgba(104, 211, 145, 0.2);
      }

      .answer-option.incorrect {
        border-color: #ef4444;
        background: rgba(239, 68, 68, 0.1);
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.15);
      }

      .option-letter {
        background: var(--accent-primary);
        color: white;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        flex-shrink: 0;
        font-size: 14px;
      }

      .answer-option.correct .option-letter {
        background: var(--success);
      }

      .answer-option.incorrect .option-letter {
        background: #ef4444;
      }

      .option-text {
        font-size: 16px;
        line-height: 1.4;
        color: var(--text-primary);
      }


      /* AI Tutor Panel */
      .ai-tutor-panel {
        width: 400px;
        background: var(--bg-secondary);
        border-left: 1px solid var(--accent-tertiary);
        display: flex;
        flex-direction: column;
      }

      .tutor-header {
        background: var(--accent-primary);
        padding: 20px;
        color: white;
        position: relative;
        overflow: hidden;
      }

      .minimal-artwork-bg {
        background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23e6cba5' fill-opacity='0.1'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-repeat: repeat;
        background-size: 100px 100px;
      }

      .tutor-status {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;
      }

      .tutor-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
      }

      .tutor-content {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
      }

      .performance-card {
        background: white;
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 16px;
        border: 1px solid var(--accent-tertiary);
        box-shadow: 0 2px 8px rgba(169, 194, 185, 0.1);
      }

      .performance-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
      }

      .performance-icon {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: var(--accent-primary);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
      }

      .performance-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
      }

      .stat-item {
        text-align: center;
      }

      .stat-value {
        font-size: 18px;
        font-weight: 600;
        color: var(--accent-primary);
      }

      .stat-label {
        font-size: 12px;
        color: var(--text-muted);
      }

      .hint-card {
        background: rgba(245, 158, 11, 0.1);
        border: 1px solid rgba(245, 158, 11, 0.2);
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 12px;
        border-left: 3px solid var(--warning);
      }

      .hint-header {
        color: var(--warning);
        font-size: 12px;
        font-weight: 600;
        margin-bottom: 6px;
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .hint-text {
        font-size: 14px;
        color: var(--text-secondary);
        line-height: 1.4;
      }

      .explanation-card {
        background: rgba(104, 211, 145, 0.1);
        border: 1px solid rgba(104, 211, 145, 0.2);
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 12px;
        border-left: 3px solid var(--success);
      }

      .explanation-header {
        color: var(--success);
        font-size: 12px;
        font-weight: 600;
        margin-bottom: 6px;
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .tutor-input {
        padding: 20px;
        border-top: 1px solid var(--accent-tertiary);
        background: white;
      }

      .input-container {
        position: relative;
      }

      .chat-input {
        width: 100%;
        background: var(--bg-primary);
        border: 1px solid var(--bg-secondary);
        border-radius: 12px;
        padding: 12px 50px 12px 16px;
        color: var(--text-primary);
        font-size: 14px;
        resize: none;
        min-height: 44px;
        transition: all 0.3s ease;
      }

      .chat-input:focus {
        outline: none;
        border-color: var(--accent-primary);
        box-shadow: 0 0 0 3px rgba(169, 194, 185, 0.1);
        background: white;
      }

      .send-btn {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        background: var(--accent-primary);
        border: none;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: white;
        transition: all 0.2s ease;
      }

      .send-btn:hover {
        background: var(--highlight);
        transform: translateY(-50%) scale(1.05);
      }

      /* Navigation */
      .nav-link-active {
        color: var(--text-primary);
        font-weight: 600;
        border-bottom-width: 2px;
        border-color: var(--accent-primary);
        padding-bottom: 0.25rem;
      }

      .nav-link {
        color: var(--text-muted);
        font-weight: 500;
        transition: color 0.3s ease;
      }

      .nav-link:hover {
        color: var(--text-secondary);
      }

      /* Animation for correct/incorrect feedback */
      @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
      }

      @keyframes pulse-success {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.02); }
      }

      .shake {
        animation: shake 0.5s ease-in-out;
      }

      .pulse-success {
        animation: pulse-success 0.6s ease-in-out;
      }

      /* Enhanced focus states */
      .focus-ring:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(169, 194, 185, 0.3);
        border-radius: 8px;
      }

      /* Responsive */
      @media (max-width: 1024px) {
        .ai-tutor-panel {
          width: 320px;
        }
      }

      @media (max-width: 768px) {
        .quiz-container {
          flex-direction: column;
        }
        .quiz-main-area {
          flex-direction: column;
        }
        .quiz-nav-toolbar {
          position: fixed;
          bottom: 20px;
          left: 50%;
          transform: translateX(-50%);
          top: auto;
          width: auto;
          height: 60px;
          flex-direction: row;
          padding: 0 16px;
          border-radius: 2rem;
          gap: 12px;
        }
        .quiz-progress-indicator {
          margin: 0 12px;
        }
        .ai-tutor-panel {
          width: 100%;
          height: 40vh;
        }
        .quiz-content {
          padding: 20px;
          padding-left: 20px;
          padding-bottom: 100px;
        }
        .question-card {
          padding: 24px;
        }
        .nav-tooltip {
          left: 50%;
          top: -40px;
          transform: translateX(-50%);
        }
        .nav-tooltip::before {
          left: 50%;
          top: 100%;
          transform: translateX(-50%);
          border-left: 4px solid transparent;
          border-right: 4px solid transparent;
          border-top: 4px solid var(--text-primary);
          border-bottom: none;
        }
      }
    </style>
  </head>
  <body class="text-[var(--text-primary)]">
    <!-- Floating Navigation Toolbar -->
    <div class="quiz-nav-toolbar">
      <button class="nav-btn" onclick="previousQuestion()">
        <i class="w-4 h-4" data-lucide="chevron-up"></i>
        <div class="nav-tooltip">Previous Question</div>
      </button>

      <div class="quiz-progress-indicator">
        <div class="current-question">3</div>
        <div class="total-questions">of 8</div>
      </div>

      <button class="nav-btn" onclick="requestHint()">
        <i class="w-4 h-4" data-lucide="lightbulb"></i>
        <div class="nav-tooltip">Get Hint</div>
      </button>

      <button class="nav-btn" onclick="skipQuestion()">
        <i class="w-4 h-4" data-lucide="skip-forward"></i>
        <div class="nav-tooltip">Skip Question</div>
      </button>

      <button class="nav-btn primary" id="nextBtn" disabled onclick="nextQuestion()">
        <i class="w-4 h-4" data-lucide="chevron-down"></i>
        <div class="nav-tooltip">Next Question</div>
      </button>
    </div>

    <div class="quiz-container">
      <div class="quiz-panel">
        <!-- Header with StudyMind Branding -->
        <header class="flex justify-between items-center p-6 bg-white border-b border-[var(--bg-secondary)]">
          <div class="flex items-center space-x-4">
            <button
              onclick="goBack()"
              class="w-10 h-10 bg-[var(--bg-secondary)] rounded-full flex items-center justify-center hover:bg-[var(--accent-tertiary)]/50 transition-colors focus-ring"
            >
              <i class="w-5 h-5 text-[var(--text-primary)]" data-lucide="arrow-left"></i>
            </button>
            <div class="flex items-center space-x-3">
              <i class="w-8 h-8" style="color: var(--accent-primary)" data-lucide="brain"></i>
              <h1 class="text-2xl font-bold" style="color: var(--text-primary)">StudyMind</h1>
            </div>
          </div>
          <nav class="flex items-center space-x-8 text-md font-medium">
            <a class="nav-link" href="/dashboard.html">Dashboard</a>
            <a class="nav-link" href="/courses.html">Courses</a>
            <a class="nav-link-active" href="#">Quiz</a>
            <a class="nav-link" href="#">Progress</a>
          </nav>
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-[var(--accent-secondary)] rounded-full flex items-center justify-center">
              <img src="/images/profile-avatar.png" alt="Avatar" class="w-6 h-6 rounded-full" />
            </div>
          </div>
        </header>

        <div class="quiz-header">
          <div class="quiz-title">
            <i class="w-6 h-6" style="color: var(--accent-primary)" data-lucide="brain-circuit"></i>
            AI-Generated Quiz: Market Structures
            <div class="ai-insight">
              Adaptive difficulty based on your performance
            </div>
          </div>
          <div class="quiz-meta">
            <span class="flex items-center"><i class="w-4 h-4 mr-1" data-lucide="clipboard-list"></i>8 questions</span>
            <span class="flex items-center"><i class="w-4 h-4 mr-1" data-lucide="clock"></i>No time limit</span>
            <span class="flex items-center"><i class="w-4 h-4 mr-1" data-lucide="target"></i>Personalized for your learning style</span>
            <span class="flex items-center"><i class="w-4 h-4 mr-1" data-lucide="lightbulb"></i>Instant explanations available</span>
          </div>
          <div class="quiz-progress">
            <div class="progress-fill"></div>
          </div>
          <div class="progress-text">
            Question <span class="geist-mono">3</span> of <span class="geist-mono">8</span> • <span class="geist-mono">62%</span> average confidence
          </div>
        </div>

        <div class="quiz-main-area">
          <!-- Quiz Content Area -->
          <div class="quiz-content minimal-artwork-subtle">
            <div class="question-card">
              <div class="question-header">
                <div class="question-number">3</div>
                <div class="question-type">Application</div>
              </div>

              <div class="question-context">
                <div class="context-label">
                  <i class="w-4 h-4" data-lucide="book-open"></i>
                  Based on: Perfect Competition (Lecture 5)
                </div>
                <div class="context-text">
                  This question tests your understanding of how firms behave in
                  perfectly competitive markets, specifically focusing on the
                  relationship between market price and firm decision-making.
                </div>
              </div>

              <div class="question-text">
                In a perfectly competitive market, if the market price falls below
                a firm's average variable cost, what should the firm do in the
                short run?
              </div>

              <div class="answer-options">
                <div class="answer-option" onclick="selectAnswer(this, false)">
                  <div class="option-letter">A</div>
                  <div class="option-text">
                    Continue producing at the same level to maintain market share
                  </div>
                </div>

                <div class="answer-option" onclick="selectAnswer(this, false)">
                  <div class="option-letter">B</div>
                  <div class="option-text">
                    Reduce production to minimize losses while staying in the
                    market
                  </div>
                </div>

                <div class="answer-option" onclick="selectAnswer(this, true)">
                  <div class="option-letter">C</div>
                  <div class="option-text">
                    Shut down production temporarily but remain in the industry
                  </div>
                </div>

                <div class="answer-option" onclick="selectAnswer(this, false)">
                  <div class="option-letter">D</div>
                  <div class="option-text">
                    Exit the industry permanently to avoid future losses
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="ai-tutor-panel">
        <div class="tutor-header">
          <div class="absolute inset-0 minimal-artwork-bg opacity-20"></div>
          <div class="relative z-10">
            <div class="tutor-status">
              <div class="tutor-avatar">
                <i class="w-5 h-5 text-white" data-lucide="brain-circuit"></i>
              </div>
              <div>
                <div style="font-weight: 600">AI Quiz Tutor</div>
                <div style="font-size: 12px; opacity: 0.8">
                  Monitoring your progress
                </div>
              </div>
            </div>
            <div style="font-size: 12px; opacity: 0.8">
              Adapting questions based on your understanding
            </div>
          </div>
        </div>

        <div class="tutor-content">
          <div class="performance-card">
            <div class="performance-header">
              <div class="performance-icon">
                <i class="w-3 h-3 text-white" data-lucide="bar-chart-3"></i>
              </div>
              <div style="font-weight: 600; color: var(--text-primary)">Current Performance</div>
            </div>
            <div class="performance-stats">
              <div class="stat-item">
                <div class="stat-value">75%</div>
                <div class="stat-label">Accuracy</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">1.2m</div>
                <div class="stat-label">Avg Time</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">Strong</div>
                <div class="stat-label">Confidence</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">Advanced</div>
                <div class="stat-label">Next Level</div>
              </div>
            </div>
          </div>

          <div class="hint-card" style="display: none" id="hintCard">
            <div class="hint-header">
              <i class="w-4 h-4" data-lucide="lightbulb"></i>
              Thinking Strategy
            </div>
            <div class="hint-text">
              Remember the shutdown rule: if P < AVC, the firm loses money on
              every unit produced. What's the logical decision when you lose
              money on each unit?
            </div>
          </div>

          <div class="explanation-card" style="display: none" id="explanationCard">
            <div class="explanation-header">
              <i class="w-4 h-4" data-lucide="check-circle"></i>
              Explanation
            </div>
            <div class="hint-text">
              Correct! When price falls below average variable cost, the firm
              should shut down temporarily. It would lose less money by paying
              only fixed costs rather than losing money on each unit produced.
            </div>
          </div>

          <div class="performance-card">
            <div style="font-weight: 600; margin-bottom: 12px; color: var(--text-primary); display: flex; align-items: center; gap: 8px;">
              <i class="w-4 h-4" style="color: var(--accent-primary)" data-lucide="target"></i>
              Strengths & Focus Areas
            </div>
            <div style="font-size: 13px; color: var(--text-secondary); line-height: 1.4; margin-bottom: 8px;">
              <span style="color: var(--success)">✓ Strong:</span> Supply & demand
              concepts, market equilibrium
            </div>
            <div style="font-size: 13px; color: var(--text-secondary); line-height: 1.4;">
              <span style="color: var(--warning)">⚡ Focus:</span> Cost analysis in
              different market structures
            </div>
          </div>

          <div class="performance-card">
            <div style="font-weight: 600; margin-bottom: 12px; color: var(--text-primary); display: flex; align-items: center; gap: 8px;">
              <i class="w-4 h-4" style="color: var(--accent-primary)" data-lucide="refresh-cw"></i>
              AI Adaptations
            </div>
            <div style="font-size: 13px; color: var(--text-secondary); line-height: 1.5;">
              • Next question will focus on cost curves<br />
              • Difficulty slightly increased based on your performance<br />
              • More application questions added to your queue
            </div>
          </div>
        </div>

        <div class="tutor-input">
          <div class="input-container">
            <textarea
              class="chat-input"
              placeholder="Ask for clarification, request examples, or get study tips..."
            ></textarea>
            <button class="send-btn">
              <i class="w-4 h-4" data-lucide="send"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <script>
      lucide.createIcons();

      let selectedOption = null;
      let isAnswered = false;

      function selectAnswer(option, isCorrect) {
        if (isAnswered) return;

        // Clear previous selections
        document.querySelectorAll(".answer-option").forEach((opt) => {
          opt.classList.remove("selected");
        });

        // Select current option
        option.classList.add("selected");
        selectedOption = option;

        // Enable next button
        document.getElementById("nextBtn").disabled = false;

        // Show immediate feedback after a short delay
        setTimeout(() => {
          showFeedback(isCorrect);
        }, 500);
      }

      function showFeedback(isCorrect) {
        isAnswered = true;

        // Show correct/incorrect styling
        document.querySelectorAll(".answer-option").forEach((opt) => {
          if (opt === selectedOption) {
            if (isCorrect) {
              opt.classList.add("correct", "pulse-success");
              document.getElementById("explanationCard").style.display = "block";
            } else {
              opt.classList.add("incorrect", "shake");
            }
          }
        });

        // If incorrect, also highlight the correct answer
        if (!isCorrect) {
          document.querySelectorAll(".answer-option").forEach((opt) => {
            if (opt.querySelector(".option-letter").textContent === "C") {
              opt.classList.add("correct");
            }
          });
        }

        // Update progress
        updateProgress();
      }

      function requestHint() {
        document.getElementById("hintCard").style.display = "block";
      }

      function nextQuestion() {
        if (!isAnswered) return;
        alert("Moving to question 4...");
      }

      function previousQuestion() {
        alert("Going back to question 2...");
      }

      function skipQuestion() {
        alert("Question skipped. Moving to next...");
      }

      function goBack() {
        window.location.href = "courses.html";
      }

      function updateProgress() {
        const progressBar = document.querySelector(".progress-fill");
        const progressText = document.querySelector(".progress-text");

        // Simulate progress update
        progressBar.style.width = "50%";
        progressText.innerHTML = `Question <span class="geist-mono">3</span> of <span class="geist-mono">8</span> • <span class="geist-mono">68%</span> average confidence`;
      }

      // Enhanced click handlers with feedback
      const interactiveElements = document.querySelectorAll('button, [tabindex="0"]');
      interactiveElements.forEach((element) => {
        element.addEventListener("click", function (e) {
          this.style.transform = "scale(0.98)";
          setTimeout(() => {
            this.style.transform = "";
          }, 150);
        });

        element.addEventListener("keydown", function (e) {
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            this.click();
          }
        });
      });

      // Simulate real-time AI adaptations
      setInterval(() => {
        const accuracy = document.querySelector(".stat-value");
        if (Math.random() > 0.98) {
          // Rare updates - keeping performance stats relatively stable
        }
      }, 3000);
    </script>
  </body>
</html>