Here is a comprehensive backend technical specification for the AI-assisted learning platform.

## **Analysis of Provided Materials**

### 1. UI Feature Extraction

A thorough review of the provided HTML mockups reveals a rich, AI-driven, and highly interactive user experience. The required features have been extracted and categorized below.

**Cross-Platform Features:**

- **Authentication**: Implied by user-specific content ("Welcome back, <PERSON>!").
- **User Roles**: Distinction between Students and Creators (implied by content creation vs. consumption).
- **AI Companion/Tutor**: A persistent sidebar/panel across all learning interfaces (video, audio, PDF, notes, quiz) providing contextual help, insights, and chat functionality.
- **Navigation**: Consistent header navigation (Dashboard, Courses, Study Tools, Progress).

**Dashboard (`dashboard.html`):**

- Personalized greeting.
- Key performance indicators (KPIs): Day Streak, Average Score, Active Courses, Study Time.
- "Continue Learning" card for the last-viewed course, showing specific lecture progress.
- "Recent Activity" feed (notes added, quizzes completed, flashcards reviewed).
- AI Assistant recommendations and learning pattern analysis.

**Courses (`courses.html`):**

- Grid view of all user-enrolled courses.
- Per-course details: Title, instructor, progress percentage, status (Active, Completed, Paused).
- Functionality: Search, Filter (by status), and Sort (by recency, progress, etc.).

**Course Detail (`course.html`):**

- Detailed progress for a single course.
- Complete lecture list with status (Completed, Next Up, Locked), type (Video, PDF, Audio), and duration.
- Direct actions on lectures: Start, Preview, Review Notes.
- Course-specific study tools (AI Quiz, Flashcards).

**Learning Interfaces (`video.html`, `audio.html`, `pdf.html`):**

- **Video/Audio**: Media player with custom controls, speed adjustment, and a synchronized, clickable live transcript.
- **PDF**: Document viewer with page thumbnails, zoom, highlighting, and focus mode.
- **Universal**: Chapter navigation, AI-powered quick actions (make flashcards, quiz me), and a contextual AI tutor.

**Study Tools (`flashcard.html`, `quiz.html`, `notes.html`):**

- **Flashcards**: AI-generated, adaptive spaced repetition system where users rate their confidence (Again, Hard, Good, Easy).
- **Quizzes**: AI-generated quizzes with adaptive difficulty, instant feedback, and detailed explanations from an AI Tutor.
- **Notes**: A rich notes editor with AI assistance for summarization, concept linking, and smart formatting. Notes can be created manually or generated by AI from highlights and lectures.

### 2. Schema Gap Analysis

Comparing the UI features against the initial `schema.ts`, several gaps in the data model are evident. The existing schema is a strong foundation, but it lacks the tables and fields to support the full range of user interactions and analytics displayed in the mockups.

**Missing Tables:**

- `userLectureProgress`: To track completion status for each lecture individually, which is a core part of the UI (`course.html`).
- `quizAttempts` & `quizAnswers`: To store a user's session with a quiz, their answers, the score, and the feedback provided.
- `aiTutorConversations`: To persist the chat history between a user and the AI tutor for each course.
- `notifications`: To power the notification bell icon seen in the header.
- `studyStreaks`: To track the "Day Streak" KPI on the dashboard.

**Required Enhancements to Existing Tables:**

- `users`: Needs a `plan` field to support "Premium Plan" display and a `lastLoginAt` for streak calculation.
- `courses`: Requires a `thumbnailUrl` for the course cards and `publishedAt` for sorting.
- `userCourses`: Must have `lastAccessedAt` to power the "Continue Learning" feature and `enrolledAt` for analytics.
- `notes`: Needs a `source` field (e.g., 'manual', 'ai_summary', 'highlight') to support filtering as seen in `notes.html`.
- `flashcards`: Requires a `source` field to distinguish between user-created and AI-generated cards.
- `userFlashcardProgress`: The current schema is good but will be heavily relied upon for the spaced repetition logic.

---

## **Backend Technical Specification**

### 3. Executive Summary

This document outlines the backend architecture for an advanced AI-assisted learning platform built on Convex. The architecture is designed to be robust, scalable, and real-time, leveraging Convex's unique features like Workflows, Workpools, and Agents to deliver a seamless and intelligent user experience.

The system orchestrates a complex content processing pipeline where creator-uploaded content (videos, audio, PDFs) is analyzed by AI in parallel to generate rich metadata, transcripts, summaries, and learning materials. For students, the platform offers a deeply integrated AI tutor with full course context, adaptive learning tools, and real-time feedback. The entire backend is designed around the provided database schema, enhanced to support all features seen in the UI mockups, with Mux handling video processing and the Vercel AI SDK serving as the interface for all Large Language Model (LLM) interactions.

### 4. System Architecture

The system is architected around a central Convex backend, acting as the single source of truth and orchestrator for all data, workflows, and AI interactions.

**Architecture Diagram (ASCII):**

```
+------------------+      +----------------+      +-----------------+
|   Client (Web)   |----->|  Convex Backend  |<---->|   Convex DB     |
+------------------+      | (Queries, Muts)  |      | (Real-time)     |
        ^                 +----------------+      +-----------------+
        |                         |       ^
 (Real-time Sync)                 |       | (Webhook)
        |                         v       |
+------------------+      +----------------+      +-----------------+
|   Vercel AI SDK  |<---->| Convex Actions,  |<---->|      Mux        |
| (LLM Inference)  |      |   Workflows,     |      | (Video, Trans.) |
+------------------+      |   Agents         |      +-----------------+
                          +----------------+
```

**Data Flow & Orchestration:**

1.  **Content Upload**: A creator uploads a video file via the client. The client gets an upload URL from a Convex mutation.
2.  **Mux Processing**: The file is uploaded directly to Mux. A Convex action initiates the Mux asset creation.
3.  **Workflow Initiation**: Upon receiving the Mux webhook for `video.asset.created`, a **Convex Workflow (`processContentWorkflow`)** is triggered.
4.  **Parallel AI Analysis (Workpools)**: The workflow spawns a **Convex Workpool** to perform parallel AI analysis tasks on the lecture content:
    - Task 1: Fetch transcript from Mux.
    - Task 2: Generate title, summary, and key concepts via Vercel AI SDK.
    - Task 3: Generate vector embeddings for the transcript chunks (RAG).
    - Task 4: Generate a default set of flashcards and quiz questions.
5.  **Data Aggregation**: As workpool tasks complete, they update the `lectures` table. The workflow waits for all essential tasks to finish.
6.  **Course Finalization**: The final step of the workflow updates the parent `courses` table with aggregated data (e.g., `lectureCount`, `totalDurationSeconds`) and sets its status to `published`.
7.  **Student Interaction**:
    - Students interact with the frontend, which calls Convex queries and mutations.
    - The AI Tutor uses a **Convex Agent** that holds conversation state and uses RAG (via a Convex action) to fetch context from `courseEmbeddings` before calling the Vercel AI SDK.
    - All database updates are pushed to clients in real-time.

### 5. Enhanced Database Schema

Based on the gap analysis, the initial schema is enhanced as follows. New tables are marked with `[NEW]` and new fields are commented.

```typescript
// convex/schema.ts
import { defineSchema, defineTable } from "convex/server";
import { authTables } from "@convex-dev/auth/server";
import { v } from "convex/values";

const schema = defineSchema({
  ...authTables,
  // =================================================================
  // USERS, ACCESS, & PAYMENTS
  // =================================================================
  users: defineTable({
    firstName: v.string(),
    lastName: v.string(),
    name: v.string(),
    email: v.string(),
    role: v.optional(
      v.union(
        v.literal("student"),
        v.literal("creator"),
        v.literal("superadmin")
      )
    ),
    profileImage: v.optional(v.string()),
    plan: v.optional(v.union(v.literal("free"), v.literal("premium"))), // [ENHANCED] For UI
    lastLoginAt: v.optional(v.number()), // [ENHANCED] For streak calculation
    lastActiveAt: v.optional(v.number()),
    status: v.optional(
      v.union(v.literal("active"), v.literal("invited"), v.literal("disabled"))
    ),
    lastAccessedResource: v.optional(v.id("resources")),
  }).index("by_email", ["email"]),

  // [NEW] Tracks daily study streaks
  studyStreaks: defineTable({
    userId: v.id("users"),
    currentStreak: v.number(),
    longestStreak: v.number(),
    lastStudiedAt: v.number(),
  }).index("by_user", ["userId"]),

  purchases: defineTable({
    /* ... no changes ... */
  }),
  subscriptions: defineTable({
    /* ... no changes ... */
  }),

  // =================================================================
  // COURSES & CONTENT
  // =================================================================
  courses: defineTable({
    creatorId: v.id("users"),
    generatedTitle: v.optional(v.string()),
    description: v.optional(v.string()),
    summary: v.optional(v.string()),
    keyConcepts: v.optional(v.array(v.string())),
    price: v.number(),
    thumbnailUrl: v.optional(v.string()), // [ENHANCED] For course cards
    publishedAt: v.optional(v.number()), // [ENHANCED] For sorting
    status: v.union(
      v.literal("processing"),
      v.literal("published"),
      v.literal("failed"),
      v.literal("archived")
    ),
    lectureCount: v.optional(v.number()),
    totalDurationSeconds: v.optional(v.number()),
  }).index("by_creator", ["creatorId"]),

  lectures: defineTable({
    courseId: v.id("courses"),
    creatorId: v.id("users"),
    generatedTitle: v.optional(v.string()),
    order: v.number(),
    type: v.union(v.literal("video"), v.literal("audio"), v.literal("pdf")),
    status: v.union(
      v.literal("pending"),
      v.literal("processing"),
      v.literal("completed"),
      v.literal("failed")
    ),
    durationSeconds: v.optional(v.number()),
    muxAssetId: v.optional(v.string()),
    muxPlaybackId: v.optional(v.string()),
    transcript: v.optional(v.string()),
    summary: v.optional(v.string()),
    insights: v.optional(v.array(v.string())),
    url: v.optional(v.string()),
    fileId: v.optional(v.id("_storage")),
  })
    .index("by_course", ["courseId"])
    .index("by_muxAssetId", ["muxAssetId"]),

  chapters: defineTable({
    /* ... no changes ... */
  }),

  // =================================================================
  // USER ENGAGEMENT & LEARNING TOOLS
  // =================================================================
  userCourses: defineTable({
    userId: v.id("users"),
    courseId: v.id("courses"),
    progress: v.number(),
    status: v.string(), // "active", "completed", "paused"
    enrolledAt: v.number(), // [ENHANCED] For analytics
    lastAccessedAt: v.optional(v.number()), // [ENHANCED] For "Continue Learning"
  }).index("by_user_and_course", ["userId", "courseId"]),

  // [NEW] Tracks per-lecture progress for a user
  userLectureProgress: defineTable({
    userId: v.id("users"),
    lectureId: v.id("lectures"),
    courseId: v.id("courses"),
    status: v.union(
      v.literal("not_started"),
      v.literal("in_progress"),
      v.literal("completed")
    ),
    progressSeconds: v.optional(v.number()),
  }).index("by_user_lecture", ["userId", "lectureId"]),

  notes: defineTable({
    userId: v.id("users"),
    courseId: v.id("courses"),
    lectureId: v.optional(v.id("lectures")),
    title: v.string(),
    content: v.string(),
    source: v.string(), // [ENHANCED] "manual", "ai_summary", "highlight"
    tags: v.array(v.string()),
  }).index("by_user_course", ["userId", "courseId"]),

  quizzes: defineTable({
    /* ... no changes ... */
  }),
  questions: defineTable({
    /* ... no changes ... */
  }),

  // [NEW] Stores a user's attempt at a quiz
  quizAttempts: defineTable({
    userId: v.id("users"),
    quizId: v.id("quizzes"),
    score: v.number(), // Percentage
    startedAt: v.number(),
    completedAt: v.number(),
  }).index("by_user_quiz", ["userId", "quizId"]),

  // [NEW] Stores a user's specific answer to a question in an attempt
  quizAnswers: defineTable({
    attemptId: v.id("quizAttempts"),
    questionId: v.id("questions"),
    userId: v.id("users"),
    selectedOptionIndex: v.number(),
    isCorrect: v.boolean(),
  }).index("by_attempt", ["attemptId"]),

  flashcardDecks: defineTable({
    /* ... no changes ... */
  }),
  flashcards: defineTable({
    deckId: v.id("flashcardDecks"),
    question: v.string(),
    answer: v.string(),
    explanation: v.optional(v.string()),
    source: v.optional(v.string()), // [ENHANCED] "ai_generated", "user_created"
  }).index("by_deck", ["deckId"]),

  userFlashcardProgress: defineTable({
    /* ... no changes ... */
  }),

  // =================================================================
  // ACTIVITY, AI TUTOR, & RAG
  // =================================================================
  userActivity: defineTable({
    /* ... no changes ... */
  }),
  aiInsights: defineTable({
    /* ... no changes ... */
  }),

  // [NEW] Stores conversation history for the AI Tutor
  aiTutorConversations: defineTable({
    userId: v.id("users"),
    courseId: v.id("courses"),
    lectureId: v.optional(v.id("lectures")),
    messages: v.array(
      v.object({
        role: v.union(v.literal("user"), v.literal("assistant")),
        content: v.string(),
      })
    ),
  }).index("by_user_course", ["userId", "courseId"]),

  courseEmbeddings: defineTable({
    /* ... no changes ... */
  }),

  // [NEW] Stores notifications for users
  notifications: defineTable({
    userId: v.id("users"),
    title: v.string(),
    body: v.string(),
    type: v.string(), // e.g., 'course_ready', 'ai_recommendation'
    isRead: v.boolean(),
    url: v.optional(v.string()),
  }).index("by_user", ["userId"]),
});

export default schema;
```

### 6. API Design (Convex Functions)

The API will be structured using Convex's function types: `query`, `mutation`, and `action`.

**Creator APIs:**

- `courses:create` (mutation): Creates a new course entry with `status: 'processing'`.
- `courses:startUpload` (mutation): Generates a Mux upload URL for a new lecture.
- `courses:updateDetails` (mutation): Allows creators to edit course price, title, etc.
- `analytics:getCreatorDashboard` (query): Aggregates sales and enrollment data for the creator.

**Student APIs:**

- `courses:listPublished` (query): Fetches all published courses for the discovery page.
- `courses:getDetails` (query): Fetches a single course with its lectures and the user's progress (`userCourses` and `userLectureProgress`).
- `courses:enroll` (mutation): Enrolls a user in a course after a successful purchase.
- `progress:updateLecture` (mutation): Updates `userLectureProgress` as a student watches a video.

**AI Tutor & Tools APIs:**

- `ai:chat` (action): The main endpoint for the AI Tutor. It takes the user's message, retrieves conversation history and RAG context, calls the Vercel AI SDK, and streams the response back.
- `ai:generateQuiz` (action): Creates a new `quizzes` entry and populates it with `questions` generated by an LLM based on a course or lecture's content.
- `ai:generateFlashcards` (action): Similar to the quiz generator, but for flashcards.
- `notes:getWithAIContext` (query): Fetches a note and simultaneously runs a query to find related concepts from the `courseEmbeddings` table.

**Mux Integration APIs (Internal Actions):**

- `mux:handleWebhook` (httpAction): The public endpoint to receive webhooks from Mux (e.g., `video.asset.ready`, `video.asset.errored`). This action will validate the webhook and trigger internal mutations or workflows.
- `workflows:processContent`: The main workflow entry point, triggered by the Mux webhook.

### 7. Workflow Patterns (Convex Workflows & Workpools)

The core of the content processing pipeline will be a Convex Workflow.

**`processContentWorkflow` Definition:**

This workflow is triggered when a Mux asset is ready.

```typescript
// convex/workflows.ts
import { workflow } from "convex/server";
import { api } from "./_generated/api";

export const processContentWorkflow = workflow("processContent", {
  args: { lectureId: "Id<lectures>", muxAssetId: "string" },
  run: async (ctx, { lectureId, muxAssetId }) => {
    // Step 1: Update lecture status
    await ctx.runMutation(api.lectures.updateStatus, {
      lectureId,
      status: "processing",
    });

    // Step 2: Run analysis tasks in parallel using a workpool
    const results = await ctx.run("analyzeContent", {
      // This is a workpool definition
      work: api.ai.analyzeLecture,
      args: [{ lectureId, muxAssetId }],
    });

    // Step 3: Wait for results and update the database
    const { summary, title, concepts, embeddings, transcript } = results[0];
    await ctx.runMutation(api.lectures.populateAIGeneratedData, {
      lectureId,
      summary,
      generatedTitle: title,
      keyConcepts: concepts,
      transcript,
    });

    // Step 4: Finalize and update course status
    await ctx.runMutation(api.courses.updateAggregateData, { lectureId });

    return { success: true };
  },
});
```

**Workpool for Parallel AI Analysis:**

The `analyzeContent` workpool executes multiple AI tasks concurrently.

```typescript
// convex/ai.ts
import { workpool, action } from "convex/server";
import { VercelAI } from '...'; // Vercel AI SDK import

export const analyzeContent = workpool({
  // The `work` function is called for each item in the pool
  work: action({
    args: { lectureId: "Id<lectures>", muxAssetId: "string" },
    handler: async (ctx, { lectureId, muxAssetId }) => {
      // 1. Fetch transcript from Mux
      const transcript = await fetchTranscript(muxAssetId);

      // 2. Generate Summary, Title, Concepts (could be one LLM call)
      const { summary, title, concepts } = await VercelAI.generateText(...);

      // 3. Generate Embeddings for transcript chunks
      const embeddings = await VercelAI.generateEmbeddings(transcript);
      // ... store embeddings in `courseEmbeddings` table ...

      return { summary, title, concepts, embeddings, transcript };
    },
  }),
});
```

### 8. AI Integration (Convex Agents & Vercel AI SDK)

**AI Tutor with Convex Agents:**

The AI Tutor uses a Convex Agent to maintain state across multiple turns of a conversation, ensuring it remembers the context without needing to re-fetch it on every request.

```typescript
// convex/agent.ts
import { Agent, action, query } from "convex/server";
import { VercelAI, streamText } from '...';
import { api } from "./_generated/api";

// Define the agent's state
type TutorState = {
  courseId: Id<"courses">;
  conversationHistory: any[]; // Vercel AI SDK message format
};

// Create the agent
const agent = new Agent<TutorState>(
  // The `run` function is the agent's main loop
  async (state, step) => {
    const { courseId, conversationHistory } = state;

    // 1. Get RAG context based on the latest user message
    const lastUserMessage = conversationHistory[conversationHistory.length - 1].content;
    const contextChunks = await step.runQuery(api.rag.search, {
      courseId,
      query: lastUserMessage,
    });

    const systemPrompt = `You are an expert tutor... Here is relevant context: ${contextChunks.join("\n")}`;

    // 2. Call the LLM via Vercel AI SDK
    const stream = await streamText({
      model: 'openai/gpt-4-turbo',
      system: systemPrompt,
      messages: conversationHistory,
    });

    // 3. Stream the response back to the client
    for await (const delta of stream) {
      step.send(delta);
    }

    // The agent will wait for the next user message
    const nextMessage = await step.waitForMessage();
    state.conversationHistory.push(nextMessage);
  }
);

// Action to start or continue a conversation with the agent
export const chat = action({
  args: { userId: "Id<users>", courseId: "Id<courses">, message: "string" },
  handler: async (ctx, { userId, courseId, message }) => {
    // Find or create an agent session for this user/course
    const sessionId = `${userId}-${courseId}`;

    // Resume the agent's execution
    const stream = await agent.resume(sessionId, {
      // Initial state if the agent is new
      initialState: { courseId, conversationHistory: [{ role: 'user', content: message }] },
      // The message to send to the waiting agent
      message: { role: 'user', content: message },
    });

    // Return the stream to the client
    return stream;
  },
});
```

### 9. Mux Integration

Mux integration is critical for the video pipeline.

1.  **Upload Initiation**: A mutation `courses:startUpload` is called. It uses the Mux Node.js SDK to create a new `upload` URL and returns it to the client.
2.  **Direct Upload**: The client uploads the file directly to the Mux-provided URL.
3.  **Webhook Handling**: A public HTTP action `mux:handleWebhook` is created in Convex.
    - URL: `https://<your-convex-url>/mux`
    - This endpoint is registered in the Mux dashboard.
    - It must validate the `Mux-Signature` header for security.
    - When a `video.asset.ready` event is received, it extracts the `asset_id` and `playback_id` and triggers the `processContentWorkflow`.
4.  **Transcript Fetching**: Inside the workflow, an action fetches the VTT transcript from `https://stream.mux.com/:playbackId/text/vtt`.

### 10. Real-time Features

Convex's built-in real-time system is used extensively:

- **Content Processing Status**: The client subscribes to a query that fetches the `status` of `lectures` and `courses`. As the workflow updates these documents, the UI automatically reflects the changes from `processing` to `published`.
- **AI Tutor Chat**: The Vercel AI SDK's streaming response is piped through Convex actions directly to the client, creating a real-time chat experience.
- **Live Progress**: As a student watches a video, the client periodically calls a mutation `progress:updateLecture`. Other parts of the UI subscribed to course progress (like the dashboard) will update in real-time.

### 11. Code Organization

The Convex project will be structured for clarity and scalability.

```
convex/
├── courses/
│   ├── mutations.ts
│   └── queries.ts
├── lectures/
│   ├── mutations.ts
│   └── queries.ts
├── ai/
│   ├── agent.ts         # AI Tutor agent logic
│   ├── actions.ts       # Actions for Vercel AI SDK calls
│   └── workpools.ts     # Content analysis workpool
├── notes/
│   ├── mutations.ts
│   └── queries.ts
├── payments/
│   └── actions.ts
├── progress/
│   └── mutations.ts
├── lib/
│   ├── mux.ts           # Mux SDK wrappers
│   └── utils.ts
├── http.ts              # For Mux webhooks
├── schema.ts
├── workflows.ts
└── auth.config.js
```

### 12. Implementation Roadmap

1.  **Phase 1: Core Content Pipeline & Consumption**

    - Implement enhanced schema.
    - Build creator upload flow with Mux integration.
    - Develop the `processContentWorkflow` with transcript fetching and basic AI analysis (title, summary).
    - Build the student video/audio/PDF consumption views (without AI sidebar).
    - Implement user enrollment and basic progress tracking.

2.  **Phase 2: AI Content Generation & RAG**

    - Integrate Vercel AI SDK.
    - Implement the full parallel workpool for generating embeddings, quizzes, and flashcards.
    - Set up the `courseEmbeddings` table and vector search query.
    - Build the AI-generated quiz and flashcard study tools.

3.  **Phase 3: Interactive AI Tutor & Real-time Features**

    - Implement the Convex Agent for the AI Tutor.
    - Build the AI sidebar UI and integrate the `ai:chat` action.
    - Implement real-time features like live transcript highlighting and collaborative notes (if planned).
    - Develop the AI-assisted notes interface.

4.  **Phase 4: Analytics & Polish**
    - Build creator and admin dashboards with analytics.
    - Implement the study streak and notification systems.
    - Refine UI/UX, optimize performance, and conduct thorough testing.

### 13. Performance Considerations

- **Indexing**: The enhanced schema includes critical indexes on foreign keys and frequently queried fields (`userId`, `courseId`, `status`). Vector indexes are specified for RAG.
- **Workpools**: Using workpools for AI analysis is key to scalability, preventing long-running actions from blocking the system and allowing for high concurrency.
- **Pagination**: All list views (courses, lectures, notes) will use Convex's built-in pagination in queries to ensure fast initial loads.
- **Data Aggregation**: For creator analytics, a separate table (`creatorAnalytics`) should be maintained and updated periodically via a scheduled function to avoid expensive real-time aggregations on large datasets.
- **Caching**: Frequently accessed, non-user-specific data (like published course details) can be cached at the application layer or using a global cache if needed, though Convex's built-in caching is often sufficient.
