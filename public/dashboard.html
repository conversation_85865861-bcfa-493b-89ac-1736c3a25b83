<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>StudyMind Dashboard</title>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Geist:wght@100..900&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Geist+Mono:wght@100..900&family=Geist:wght@100..900&display=swap"
      rel="stylesheet"
    />

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>
    <style type="text/tailwindcss">
      :root {
        --bg-primary: #fdfbf6;
        --bg-secondary: #f0e5d8;
        --text-primary: #2d3748;
        --text-secondary: #4a5568;
        --text-muted: #718096;
        --text-subtle: #a0aec0;
        --text-on-tinted: #2d3748;
        --text-on-tinted-muted: #4a5568;
        --text-on-tinted-subtle: #718096;
        --accent-primary: #a9c2b9;
        --accent-secondary: #d3b8ae;
        --accent-tertiary: #e6cba5;
        --highlight: #8b9d8f;
        --success: #68d391;
        --warning: #f6ad55;
        --info: #63b3ed;
      }
      body {
        font-family: "Geist", sans-serif;
        background-color: var(--bg-primary);
        color: var(--text-primary);
      }
      .geist-mono {
        font-family: "Geist Mono", monospace;
      }
      .animated-gradient {
        background: linear-gradient(
          -45deg,
          var(--accent-tertiary),
          var(--bg-secondary),
          var(--accent-primary),
          var(--highlight)
        );
        background-size: 400% 400%;
        animation: gradientBG 20s ease infinite;
      }
      @keyframes gradientBG {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }
      .stagger-entrance > * {
        opacity: 0;
        transform: translateY(20px);
        animation: fadeInUp 0.6s forwards;
      }
      @keyframes fadeInUp {
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      .progress-bar-fill {
        transition: width 0.7s cubic-bezier(0.65, 0, 0.35, 1);
      }
      .hover-lift {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .hover-lift:hover {
        transform: translateY(-5px) scale(1.02);
        box-shadow: 0 12px 20px -5px rgba(45, 55, 72, 0.15),
          0 6px 8px -3px rgba(45, 55, 72, 0.1);
      }
      .sidebar-card-item:hover {
        background-color: var(--bg-secondary);
        transform: translateX(2px);
      }
      .nav-link-active {
        color: var(--text-primary);
        font-weight: 600;
        border-bottom-width: 2px;
        border-color: var(--accent-primary);
        padding-bottom: 0.25rem;
      }
      .nav-link {
        color: var(--text-muted);
        font-weight: 500;
      }
      .nav-link:hover {
        color: var(--text-secondary);
        transition-duration: 300ms;
      }
      .icon-primary {
        color: var(--accent-primary);
      }
      .icon-secondary {
        color: var(--accent-secondary);
      }
      .text-highlight {
        color: var(--highlight);
        font-weight: 600;
      }
      .minimal-artwork-bg {
        background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23e6cba5' fill-opacity='0.1'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-repeat: repeat;
        background-size: 200px 200px;
      }
      .minimal-artwork-subtle {
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23a9c2b9' fill-opacity='0.07'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-repeat: repeat;
      }

      /* Enhanced button styles */
      .btn-primary {
        background: var(--accent-primary);
        color: white;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        border-radius: 0.75rem;
        transition: all 0.2s ease;
        border: none;
        cursor: pointer;
      }

      .btn-primary:hover {
        background: var(--highlight);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(169, 194, 185, 0.3);
      }

      /* Better focus states */
      .focus-ring:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(169, 194, 185, 0.3);
      }
    </style>
  </head>
  <body class="text-[var(--text-primary)]">
    <div class="flex h-screen">
      <main class="flex-1 flex flex-col">
        <header
          class="flex justify-between items-center p-8 pb-0 bg-[var(--bg-primary)] sticky top-0 z-10 border-b border-[var(--accent-tertiary)]/20"
        >
          <div class="flex items-center space-x-3">
            <i class="w-10 h-10 icon-primary" data-lucide="brain"></i>
            <h1 class="text-3xl font-bold text-[var(--text-primary)]">
              StudyMind
            </h1>
          </div>
          <nav class="flex items-center space-x-8 text-md font-medium">
            <a class="nav-link-active" href="#">Dashboard</a>
            <a class="nav-link" href="/courses.html">Courses</a>
            <a class="nav-link" href="#">Study Tools</a>
            <a class="nav-link" href="#">Progress</a>
            <a class="nav-link" href="#">Community</a>
          </nav>
          <div class="flex items-center space-x-4">
            <div class="relative">
              <i
                class="w-6 h-6 text-[var(--text-muted)] hover:text-[var(--accent-primary)] cursor-pointer transition-colors focus-ring"
                data-lucide="bell"
                tabindex="0"
              ></i>
              <span
                class="absolute -top-1.5 -right-1.5 w-2.5 h-2.5 bg-red-500 rounded-full border-2 border-[var(--bg-primary)]"
              ></span>
            </div>
            <div class="flex items-center space-x-3">
              <div
                class="w-10 h-10 bg-[var(--accent-secondary)] rounded-full flex items-center justify-center text-white text-lg font-semibold shadow-sm"
              >
                <img
                  src="/images/profile-avatar.png"
                  alt="Profile Avatar"
                  class="w-8 h-8 rounded-full"
                />
              </div>
              <div>
                <p class="text-md font-semibold text-[var(--text-primary)]">
                  Sarah Johnes
                </p>
                <p class="text-xs text-[var(--text-muted)]">Premium Plan</p>
              </div>
            </div>
          </div>
        </header>

        <div class="flex-1 p-8 pt-10 overflow-y-auto">
          <section
            class="animated-gradient p-10 rounded-2xl shadow-xl mb-10 stagger-entrance relative overflow-hidden"
          >
            <div class="absolute inset-0 minimal-artwork-bg opacity-50"></div>
            <div class="relative z-10 flex justify-between items-start">
              <div>
                <h2
                  class="text-4xl font-bold text-white mb-3"
                  style="animation-delay: 0.1s"
                >
                  Welcome back, Alex!
                </h2>
                <p
                  class="text-white/90 text-lg mb-6"
                  style="animation-delay: 0.2s"
                >
                  Let's nurture your curiosity and reach new heights today.
                </p>
              </div>
              <i
                class="w-12 h-12 text-white opacity-80"
                data-lucide="sparkles"
                style="animation-delay: 0.3s"
              ></i>
            </div>
            <div
              class="relative z-10 grid grid-cols-1 lg:grid-cols-2 lg:grid-cols-4 gap-6 mt-8 text-center"
            >
              <div
                class="bg-white/90 backdrop-blur-sm p-5 rounded-xl shadow-lg hover-lift border border-white/20"
                style="animation-delay: 0.4s"
              >
                <p
                  class="geist-mono text-4xl font-bold text-[var(--text-primary)]"
                >
                  12
                </p>
                <p class="text-sm text-[var(--text-muted)] mt-1 font-medium">
                  Day Streak
                </p>
              </div>
              <div
                class="bg-white/90 backdrop-blur-sm p-5 rounded-xl shadow-lg hover-lift border border-white/20"
                style="animation-delay: 0.5s"
              >
                <p
                  class="geist-mono text-4xl font-bold text-[var(--accent-primary)]"
                >
                  68%
                </p>
                <p class="text-sm text-[var(--text-muted)] mt-1 font-medium">
                  Avg Score
                </p>
              </div>
              <div
                class="bg-white/90 backdrop-blur-sm p-5 rounded-xl shadow-lg hover-lift border border-white/20"
                style="animation-delay: 0.6s"
              >
                <p
                  class="geist-mono text-4xl font-bold text-[var(--text-primary)]"
                >
                  3
                </p>
                <p class="text-sm text-[var(--text-muted)] mt-1 font-medium">
                  Active Courses
                </p>
              </div>
              <div
                class="bg-white/90 backdrop-blur-sm p-5 rounded-xl shadow-lg hover-lift border border-white/20"
                style="animation-delay: 0.7s"
              >
                <p
                  class="geist-mono text-4xl font-bold text-[var(--text-primary)]"
                >
                  47h
                </p>
                <p class="text-sm text-[var(--text-muted)] mt-1 font-medium">
                  Study Time
                </p>
              </div>
            </div>
          </section>

          <section class="mb-10 stagger-entrance">
            <div class="flex justify-between items-center mb-5">
              <h3
                class="text-2xl font-semibold text-[var(--text-primary)] flex items-center"
              >
                <i
                  class="w-7 h-7 mr-3 icon-primary"
                  data-lucide="play-circle"
                ></i>
                Continue Learning
              </h3>
              <a
                class="text-md font-medium text-[var(--text-secondary)] hover:text-[var(--accent-primary)] flex items-center transition-colors focus-ring"
                href="#"
              >
                View All Courses
                <i class="w-5 h-5 ml-1.5" data-lucide="arrow-right"></i>
              </a>
            </div>
            <div
              class="bg-white p-7 rounded-2xl shadow-xl hover-lift minimal-artwork-subtle border border-gray-100"
              style="animation-delay: 0.1s"
            >
              <div class="flex items-start space-x-5">
                <div class="bg-[var(--bg-secondary)] p-4 rounded-xl">
                  <i
                    class="w-10 h-10 text-[var(--accent-primary)]"
                    data-lucide="book-open-text"
                  ></i>
                </div>
                <div class="flex-1">
                  <h4
                    class="text-xl font-semibold text-[var(--text-primary)] mb-2"
                  >
                    Microeconomics Fundamentals
                  </h4>
                  <div
                    class="flex items-center space-x-5 text-sm text-[var(--text-muted)] mb-4"
                  >
                    <span class="flex items-center font-medium"
                      ><i class="w-4 h-4 mr-1.5" data-lucide="layers"></i>
                      Economics 101</span
                    >
                    <span class="flex items-center font-medium"
                      ><i class="w-4 h-4 mr-1.5" data-lucide="user-circle"></i>
                      Prof. Johnson</span
                    >
                    <span class="flex items-center font-medium"
                      ><i class="w-4 h-4 mr-1.5" data-lucide="video"></i> 24
                      lectures</span
                    >
                  </div>
                </div>
              </div>
              <div class="mt-5">
                <div class="flex justify-between items-center text-sm mb-2">
                  <span class="text-[var(--text-secondary)] font-medium"
                    >Progress: Lecture 9 of 24</span
                  >
                  <span class="geist-mono font-bold text-[var(--text-primary)]"
                    >67% Complete</span
                  >
                </div>
                <div class="w-full bg-[var(--bg-secondary)] rounded-full h-3">
                  <div
                    class="bg-[var(--accent-primary)] h-3 rounded-full progress-bar-fill"
                    style="width: 67%"
                  ></div>
                </div>
              </div>
              <div
                class="mt-6 pt-5 border-t border-[var(--bg-secondary)] flex items-center space-x-4 hover:bg-[var(--bg-primary)] p-4 -m-4 rounded-lg transition-colors cursor-pointer group focus-ring"
                tabindex="0"
              >
                <i
                  class="w-6 h-6 text-[var(--accent-primary)] group-hover:text-[var(--highlight)] transition-colors"
                  data-lucide="arrow-right-circle"
                ></i>
                <div>
                  <p class="font-semibold text-[var(--text-primary)]">
                    Next: Market Structures
                  </p>
                  <p class="text-sm text-[var(--text-muted)]">
                    Lecture 10 • 42 min
                  </p>
                </div>
              </div>
            </div>
          </section>

          <section class="stagger-entrance">
            <div class="flex justify-between items-center mb-5">
              <h3
                class="text-2xl font-semibold text-[var(--text-primary)] flex items-center"
              >
                <i class="w-7 h-7 mr-3 icon-primary" data-lucide="activity"></i>
                Recent Activity
              </h3>
              <a
                class="text-md font-medium text-[var(--text-secondary)] hover:text-[var(--accent-primary)] flex items-center transition-colors focus-ring"
                href="#"
              >
                View All
                <i class="w-5 h-5 ml-1.5" data-lucide="arrow-right"></i>
              </a>
            </div>
            <div class="space-y-4">
              <div
                class="bg-white p-5 rounded-2xl shadow-lg flex items-center justify-between hover-lift minimal-artwork-subtle border border-gray-100"
                style="animation-delay: 0.1s"
              >
                <div class="flex items-center space-x-4">
                  <div class="bg-blue-100 p-3 rounded-lg">
                    <i
                      class="w-6 h-6 text-blue-600"
                      data-lucide="file-plus-2"
                    ></i>
                  </div>
                  <div>
                    <p class="font-semibold text-[var(--text-primary)]">
                      Added notes on Supply &amp; Demand
                    </p>
                    <p class="text-sm text-[var(--text-muted)]">
                      Economics 101 • Lecture 4
                    </p>
                  </div>
                </div>
                <p class="text-xs text-[var(--text-subtle)] geist-mono">
                  2h ago
                </p>
              </div>
              <div
                class="bg-white p-5 rounded-2xl shadow-lg flex items-center justify-between hover-lift minimal-artwork-subtle border border-gray-100"
                style="animation-delay: 0.2s"
              >
                <div class="flex items-center space-x-4">
                  <div class="bg-purple-100 p-3 rounded-lg">
                    <i
                      class="w-6 h-6 text-purple-600"
                      data-lucide="check-circle"
                    ></i>
                  </div>
                  <div>
                    <p class="font-semibold text-[var(--text-primary)]">
                      Completed quiz: Market Equilibrium
                    </p>
                    <p class="text-sm text-[var(--text-muted)]">
                      Score: 85% • 8/10 correct
                    </p>
                  </div>
                </div>
                <p class="text-xs text-[var(--text-subtle)] geist-mono">
                  Yesterday
                </p>
              </div>
              <div
                class="bg-white p-5 rounded-2xl shadow-lg flex items-center justify-between hover-lift minimal-artwork-subtle border border-gray-100"
                style="animation-delay: 0.3s"
              >
                <div class="flex items-center space-x-4">
                  <div class="bg-orange-100 p-3 rounded-lg">
                    <i class="w-6 h-6 text-orange-600" data-lucide="layers"></i>
                  </div>
                  <div>
                    <p class="font-semibold text-[var(--text-primary)]">
                      Reviewed flashcards: Economic Terms
                    </p>
                    <p class="text-sm text-[var(--text-muted)]">
                      15 cards reviewed • Good retention
                    </p>
                  </div>
                </div>
                <p class="text-xs text-[var(--text-subtle)] geist-mono">
                  2d ago
                </p>
              </div>
            </div>
          </section>
        </div>
      </main>

      <aside
        class="w-96 bg-[var(--bg-secondary)] p-7 border-l border-[var(--accent-tertiary)]/50 overflow-y-auto space-y-7 hidden lg:block"
      >
        <div
          class="bg-[var(--accent-primary)] text-white p-7 rounded-2xl shadow-xl stagger-entrance relative overflow-hidden"
        >
          <div
            class="absolute inset-0 minimal-artwork-bg opacity-20"
            style="background-size: 100px 100px"
          ></div>
          <div class="relative z-10 flex items-center space-x-4 mb-4">
            <i class="w-10 h-10 text-white" data-lucide="brain-circuit"></i>
            <div>
              <h3 class="text-xl font-semibold text-white">
                AI Learning Assistant
              </h3>
              <p class="text-sm text-white/80">Your personal guide</p>
            </div>
          </div>
          <div
            class="relative z-10 bg-white/20 hover:bg-white/30 p-4 rounded-lg mb-3 cursor-pointer transition-all duration-300 ease-in-out group sidebar-card-item focus-ring"
            style="animation-delay: 0.1s"
            tabindex="0"
          >
            <div class="flex items-start space-x-3">
              <i
                class="w-5 h-5 text-white/90 mt-1 flex-shrink-0 group-hover:text-white transition-colors"
                data-lucide="sparkles"
              ></i>
              <div>
                <h4 class="text-md font-semibold text-white">
                  Personalized Recommendation
                </h4>
                <p class="text-xs text-white/80 mt-1 leading-relaxed">
                  You're excelling at supply &amp; demand. Dive deeper into
                  market structures next.
                </p>
              </div>
            </div>
          </div>
          <div
            class="relative z-10 bg-white/20 hover:bg-white/30 p-4 rounded-lg mb-3 cursor-pointer transition-all duration-300 ease-in-out group sidebar-card-item focus-ring"
            style="animation-delay: 0.2s"
            tabindex="0"
          >
            <div class="flex items-start space-x-3">
              <i
                class="w-5 h-5 text-white/90 mt-1 flex-shrink-0 group-hover:text-white transition-colors"
                data-lucide="bar-chart-3"
              ></i>
              <div>
                <h4 class="text-md font-semibold text-white">
                  Study Pattern Analysis
                </h4>
                <p class="text-xs text-white/80 mt-1 leading-relaxed">
                  Your best learning time is 2-4 PM. Good time for the next
                  lecture!
                </p>
              </div>
            </div>
          </div>
          <div
            class="relative z-10 bg-white/20 hover:bg-white/30 p-4 rounded-lg cursor-pointer transition-all duration-300 ease-in-out group sidebar-card-item focus-ring"
            style="animation-delay: 0.3s"
            tabindex="0"
          >
            <div class="flex items-start space-x-3">
              <i
                class="w-5 h-5 text-white/90 mt-1 flex-shrink-0 group-hover:text-white transition-colors"
                data-lucide="lightbulb"
              ></i>
              <div>
                <h4 class="text-md font-semibold text-white">Learning Tip</h4>
                <p class="text-xs text-white/80 mt-1 leading-relaxed">
                  You learn best with visuals. I've added more diagrams to the
                  next lecture materials.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div
          class="bg-white p-7 rounded-2xl shadow-xl stagger-entrance minimal-artwork-subtle border border-gray-100"
        >
          <h3
            class="text-xl font-semibold text-[var(--text-primary)] mb-5 flex items-center"
          >
            <i class="w-6 h-6 mr-3 icon-secondary" data-lucide="zap"></i>
            Quick Actions
          </h3>
          <div class="space-y-3">
            <button
              class="w-full flex items-center space-x-3 text-[var(--text-secondary)] hover:bg-[var(--bg-primary)] p-3.5 rounded-lg transition-colors text-sm sidebar-card-item justify-start focus-ring"
              style="animation-delay: 0.1s"
            >
              <i
                class="w-5 h-5 icon-secondary flex-shrink-0"
                data-lucide="file-question"
              ></i>
              <span class="text-left font-medium"
                >Generate quiz from recent lectures</span
              >
            </button>
            <button
              class="w-full flex items-center space-x-3 text-[var(--text-secondary)] hover:bg-[var(--bg-primary)] p-3.5 rounded-lg transition-colors text-sm sidebar-card-item justify-start focus-ring"
              style="animation-delay: 0.2s"
            >
              <i
                class="w-5 h-5 icon-secondary flex-shrink-0"
                data-lucide="layers-3"
              ></i>
              <span class="text-left font-medium"
                >Review economics flashcards (12 due)</span
              >
            </button>
            <button
              class="w-full flex items-center space-x-3 text-[var(--text-secondary)] hover:bg-[var(--bg-primary)] p-3.5 rounded-lg transition-colors text-sm sidebar-card-item justify-start focus-ring"
              style="animation-delay: 0.3s"
            >
              <i
                class="w-5 h-5 icon-secondary flex-shrink-0"
                data-lucide="message-circle-question"
              ></i>
              <span class="text-left font-medium">Ask about your courses</span>
            </button>
          </div>
        </div>

        <div
          class="bg-white p-7 rounded-2xl shadow-xl stagger-entrance minimal-artwork-subtle border border-gray-100"
        >
          <h3
            class="text-xl font-semibold text-[var(--text-primary)] mb-5 flex items-center"
          >
            <i class="w-6 h-6 mr-3 icon-primary" data-lucide="settings-2"></i>
            Study Tools
          </h3>
          <div class="grid grid-cols-2 gap-4 text-center">
            <div
              class="bg-[var(--bg-primary)] p-4 rounded-lg hover:bg-[var(--accent-tertiary)]/50 transition-colors cursor-pointer sidebar-card-item focus-ring"
              style="animation-delay: 0.1s"
              tabindex="0"
            >
              <i
                class="w-8 h-8 icon-primary mx-auto mb-1.5"
                data-lucide="clipboard-check"
              ></i>
              <p class="text-sm font-semibold text-[var(--text-primary)]">
                AI Quiz
              </p>
              <p class="text-xs text-[var(--text-muted)]">Test knowledge</p>
            </div>
            <div
              class="bg-[var(--bg-primary)] p-4 rounded-lg hover:bg-[var(--accent-tertiary)]/50 transition-colors cursor-pointer sidebar-card-item focus-ring"
              style="animation-delay: 0.2s"
              tabindex="0"
            >
              <i
                class="w-8 h-8 icon-primary mx-auto mb-1.5"
                data-lucide="copy"
              ></i>
              <p class="text-sm font-semibold text-[var(--text-primary)]">
                Flashcards
              </p>
              <p class="text-xs text-[var(--text-muted)]">Quick review</p>
            </div>
            <div
              class="bg-[var(--bg-primary)] p-4 rounded-lg hover:bg-[var(--accent-tertiary)]/50 transition-colors cursor-pointer sidebar-card-item focus-ring"
              style="animation-delay: 0.3s"
              tabindex="0"
            >
              <i
                class="w-8 h-8 icon-primary mx-auto mb-1.5"
                data-lucide="edit-3"
              ></i>
              <p class="text-sm font-semibold text-[var(--text-primary)]">
                Smart Notes
              </p>
              <p class="text-xs text-[var(--text-muted)]">AI-enhanced</p>
            </div>
            <div
              class="bg-[var(--bg-primary)] p-4 rounded-lg hover:bg-[var(--accent-tertiary)]/50 transition-colors cursor-pointer sidebar-card-item focus-ring"
              style="animation-delay: 0.4s"
              tabindex="0"
            >
              <i
                class="w-8 h-8 icon-primary mx-auto mb-1.5"
                data-lucide="trending-up"
              ></i>
              <p class="text-sm font-semibold text-[var(--text-primary)]">
                Progress
              </p>
              <p class="text-xs text-[var(--text-muted)]">Track learning</p>
            </div>
          </div>
        </div>

        <div
          class="bg-white p-7 rounded-2xl shadow-xl stagger-entrance minimal-artwork-subtle border border-gray-100"
        >
          <h3
            class="text-xl font-semibold text-[var(--text-primary)] mb-5 flex items-center"
          >
            <i
              class="w-6 h-6 mr-3 text-[var(--accent-secondary)]"
              data-lucide="flame"
            ></i>
            Study Streak
          </h3>
          <div class="text-center mb-5">
            <p
              class="geist-mono text-5xl font-bold text-[var(--accent-primary)] mb-1"
            >
              12
            </p>
            <p class="text-md text-[var(--text-secondary)] font-medium">
              Days in a row!
            </p>
            <p class="text-sm text-[var(--text-muted)] mt-1">
              Keep up the amazing work! 🎉
            </p>
          </div>
          <div class="grid grid-cols-7 gap-1.5 text-center text-xs geist-mono">
            <span
              class="p-2.5 rounded-md bg-[var(--bg-primary)] text-[var(--text-subtle)]"
              >M</span
            >
            <span
              class="p-2.5 rounded-md bg-[var(--bg-primary)] text-[var(--text-subtle)]"
              >T</span
            >
            <span
              class="p-2.5 rounded-md bg-[var(--bg-primary)] text-[var(--text-subtle)]"
              >W</span
            >
            <span
              class="p-2.5 rounded-md bg-[var(--bg-primary)] text-[var(--text-subtle)]"
              >T</span
            >
            <span
              class="p-2.5 rounded-md bg-[var(--accent-tertiary)] text-[var(--text-primary)] font-semibold"
              >F</span
            >
            <span
              class="p-2.5 rounded-md bg-[var(--bg-primary)] text-[var(--text-subtle)]"
              >S</span
            >
            <span
              class="p-2.5 rounded-md bg-[var(--bg-primary)] text-[var(--text-subtle)]"
              >S</span
            >
            <span
              class="p-2.5 rounded-md bg-[var(--accent-tertiary)] text-[var(--text-primary)] font-semibold"
              >M</span
            >
            <span
              class="p-2.5 rounded-md bg-[var(--accent-tertiary)] text-[var(--text-primary)] font-semibold"
              >T</span
            >
            <span
              class="p-2.5 rounded-md bg-[var(--accent-tertiary)] text-[var(--text-primary)] font-semibold"
              >W</span
            >
            <span
              class="p-2.5 rounded-md bg-[var(--accent-tertiary)] text-[var(--text-primary)] font-semibold"
              >T</span
            >
            <span
              class="p-2.5 rounded-md bg-[var(--accent-tertiary)] text-[var(--text-primary)] font-semibold"
              >F</span
            >
            <span
              class="p-2.5 rounded-md bg-[var(--accent-primary)] text-white font-bold ring-2 ring-[var(--accent-primary)]/50 shadow-lg"
              >S</span
            >
            <span
              class="p-2.5 rounded-md bg-[var(--bg-primary)] text-[var(--text-subtle)]"
              >S</span
            >
          </div>
        </div>
      </aside>
    </div>

    <script>
      lucide.createIcons();

      // Enhanced staggered animations
      const staggeredItems = document.querySelectorAll(".stagger-entrance");
      staggeredItems.forEach((item) => {
        const children = Array.from(item.children);
        children.forEach((child, index) => {
          child.style.animationDelay = `${index * 0.075}s`;
        });
      });

      // Add loading states for interactive elements
      function addLoadingState(element) {
        element.style.opacity = "0.7";
        element.style.pointerEvents = "none";
        const loadingSpinner = document.createElement("div");
        loadingSpinner.className = "loading-spinner";
        loadingSpinner.innerHTML =
          '<i data-lucide="loader-2" class="w-4 h-4 animate-spin"></i>';
        element.appendChild(loadingSpinner);
        lucide.createIcons();
      }

      function removeLoadingState(element) {
        element.style.opacity = "1";
        element.style.pointerEvents = "auto";
        const spinner = element.querySelector(".loading-spinner");
        if (spinner) spinner.remove();
      }

      // Enhanced click handlers with feedback
      const interactiveElements = document.querySelectorAll(
        '[tabindex="0"], button, a'
      );
      interactiveElements.forEach((element) => {
        element.addEventListener("click", function (e) {
          // Add subtle scale animation on click
          this.style.transform = "scale(0.98)";
          setTimeout(() => {
            this.style.transform = "";
          }, 150);
        });

        // Enhanced keyboard navigation
        element.addEventListener("keydown", function (e) {
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            this.click();
          }
        });
      });

      // Progress bar animation on load
      setTimeout(() => {
        const progressBar = document.querySelector(".progress-bar-fill");
        if (progressBar) {
          progressBar.style.width = "0%";
          setTimeout(() => {
            progressBar.style.width = "67%";
          }, 300);
        }
      }, 1000);

      // Add CSS for loading spinner
      const style = document.createElement("style");
      style.textContent = `
        .loading-spinner {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background: rgba(255, 255, 255, 0.9);
          border-radius: 50%;
          padding: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        .animate-spin {
          animation: spin 1s linear infinite;
        }

        /* Enhanced focus styles */
        .focus-ring:focus-visible {
          outline: none;
          box-shadow: 0 0 0 3px rgba(169, 194, 185, 0.3);
          border-radius: 8px;
        }

        /* Smooth transitions for all interactive elements */
        button, a, [tabindex="0"] {
          transition: all 0.2s ease;
        }
      `;
      document.head.appendChild(style);

      // Accessibility improvements
      document.addEventListener("DOMContentLoaded", function () {
        // Add proper ARIA labels
        const quickActions = document.querySelectorAll(
          ".sidebar-card-item button"
        );
        quickActions.forEach((button, index) => {
          button.setAttribute("aria-label", button.textContent.trim());
        });

        // Add live region for status updates
        const liveRegion = document.createElement("div");
        liveRegion.setAttribute("aria-live", "polite");
        liveRegion.setAttribute("aria-atomic", "true");
        liveRegion.className = "sr-only";
        liveRegion.id = "status-updates";
        document.body.appendChild(liveRegion);
      });

      // Simulate real-time updates with better feedback
      setInterval(() => {
        if (Math.random() > 0.995) {
          const statusRegion = document.getElementById("status-updates");
          if (statusRegion) {
            statusRegion.textContent = "New learning insight available";
            setTimeout(() => {
              statusRegion.textContent = "";
            }, 3000);
          }
        }
      }, 5000);
    </script>
  </body>
</html>
