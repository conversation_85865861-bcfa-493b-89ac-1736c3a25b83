import { useState } from "react";
import { uploadFileWithProgressAndAbort } from "@/lib/uploader";
import { uploadMultipleFilesWithProgressAndAbort } from "@/lib/uploader";
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";

export interface FileUploadState {
  id: string;
  file: File;
  progress: number;
  error: string | null;
  result: { storageId: Id<"_storage"> } | null;
  status: "pending" | "uploading" | "processing" | "completed" | "error";
}

export const useFileUpload = () => {
  const [progress, setProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState(null);
  let uploader: { promise: Promise<any>; abort: () => void } | null = null;

  const upload = async (uploadUrl: string, file: File) => {
    setIsUploading(true);
    setProgress(0);
    setError(null);

    try {
      uploader = await uploadFileWithProgressAndAbort(
        uploadUrl,
        file,
        (percentComplete) => {
          setProgress(percentComplete);
        }
      );

      setProgress(100);
      return uploader;
    } catch (err: any) {
      setError(err.message);
      throw err;
    } finally {
      setIsUploading(false);
    }
  };

  const reset = () => {
    setProgress(0);
    setIsUploading(false);
    setError(null);
    uploader?.abort();
  };

  return {
    progress,
    isUploading,
    error,
    upload,
    reset,
  };
};

export const useMultipleFileUpload = () => {
  const [fileStates, setFileStates] = useState<FileUploadState[]>([]);
  const [isAnyUploading, setIsAnyUploading] = useState(false);
  const createUploadUrl = useMutation(api.uploader.createUploadUrl);
  let uploader: {
    promises: Promise<any>[];
    abort: (fileIndex?: number) => void;
  } | null = null;

  const upload = async (uploads: Array<{ id: string; file: File }>) => {
    // Initialize state for each file
    const initialStates: FileUploadState[] = uploads.map((upload) => ({
      id: upload.id,
      file: upload.file,
      progress: 0,
      error: null,
      result: null,
      status: "pending",
    }));

    setFileStates(initialStates);
    setIsAnyUploading(true);

    const uploadsWithUrls = await Promise.all(
      uploads.map(async (upload) => ({
        ...upload,
        uploadUrl: await createUploadUrl(),
      }))
    );

    uploadsWithUrls.forEach((upload) => {
      setFileStates((prev) =>
        prev.map((state) =>
          state.id === upload.id ? { ...state, status: "uploading" } : state
        )
      );
    });

    try {
      uploader = uploadMultipleFilesWithProgressAndAbort(
        uploadsWithUrls,
        // onProgress
        (fileIndex, percentComplete) => {
          setFileStates((prev) =>
            prev.map((state, index) =>
              index === fileIndex
                ? { ...state, progress: percentComplete }
                : state
            )
          );
        },
        // onFileComplete
        (fileIndex, result) => {
          setFileStates((prev) =>
            prev.map((state, index) =>
              index === fileIndex
                ? {
                    ...state,
                    progress: 100,
                    status: "completed",
                    result,
                  }
                : state
            )
          );
        },
        // onFileError
        (fileIndex, error) => {
          setFileStates((prev) =>
            prev.map((state, index) =>
              index === fileIndex
                ? {
                    ...state,
                    error: error.message,
                    status: "error",
                  }
                : state
            )
          );
        }
      );

      // Wait for all uploads to complete (whether successful or failed)
      const results = await Promise.allSettled(uploader.promises);

      return results;
    } catch (err) {
      // This shouldn't happen with Promise.allSettled, but just in case
      throw err;
    } finally {
      setIsAnyUploading(false);
    }
  };

  const abortFile = (fileIndex: number) => {
    if (uploader) {
      uploader.abort(fileIndex);
    }
  };

  const abortAll = () => {
    if (uploader) {
      uploader.abort();
    }
  };

  const reset = () => {
    setFileStates([]);
    setIsAnyUploading(false);
    uploader?.abort();
    uploader = null;
  };

  // Computed values for convenience
  const overallProgress =
    fileStates.length > 0
      ? fileStates.reduce((sum, state) => sum + state.progress, 0) /
        fileStates.length
      : 0;

  const completedCount = fileStates.filter(
    (state) => state.status === "completed"
  ).length;
  const errorCount = fileStates.filter((state) => state.error !== null).length;
  const successCount = fileStates.filter(
    (state) => state.status === "completed" && state.error === null
  ).length;

  return {
    fileStates,
    isAnyUploading,
    overallProgress,
    completedCount,
    errorCount,
    successCount,
    upload,
    abortFile,
    abortAll,
    reset,
  };
};
