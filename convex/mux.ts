"use node";

import Mux from "@mux/mux-node";
import { internalAction } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";

export const uploadVideo = internalAction({
  args: {
    fileId: v.string(),
    url: v.string(),
  },
  returns: v.object({
    uri: v.optional(v.string()),
    mimeType: v.optional(v.string()),
  }),
  handler: async (ctx, args) => {
    const mux = new Mux({
      tokenId: process.env.MUX_TOKEN_ID,
      tokenSecret: process.env.MUX_TOKEN_SECRET,
    });

    return {};
  },
});

export const verifySignature = internalAction({
  args: {
    body: v.string(),
    headers: v.record(v.string(), v.string()),
    secret: v.string(),
  },
  returns: v.boolean(),
  handler: async (ctx, args) => {
    const mux = new Mux({
      tokenId: process.env.MUX_TOKEN_ID,
      tokenSecret: process.env.MUX_TOKEN_SECRET,
    });

    try {
      mux.webhooks.verifySignature(args.body, args.headers, args.secret);
      return true;
    } catch (error) {
      return false;
    }
  },
});

export const handleWebhook = internalAction({
  args: {
    payload: v.any(),
  },
  returns: v.boolean(),
  handler: async (ctx, args) => {
    const { payload } = args;
    console.log("Webhook payload:", args.payload);

    switch (payload.type) {
      case "video.asset.created":
        console.log("Video created:", payload.data);
        break;
      case "video.asset.ready":
        console.log("Video ready:", payload.data);
        break;
      case "video.asset.deleted":
        console.log("Video deleted:", payload.data);
        break;
      case "video.asset.track.ready":
        await ctx.runAction(internal.mux.analyzeVideo, {
          trackId: payload.data.id,
          assetId: payload.data.asset_id,
          language: payload.data.language_code,
        });
        break;
      case "video.asset.track.deleted":
        console.log("Video track deleted:", payload.data);
        break;
      case "video.asset.updated":
        console.log("Video updated:", payload.data);
        break;
      case "video.asset.non_standard_input_detected":
        console.log("Non-standard input detected:", payload.data);
        break;
      case "video.upload.asset_create":
        console.log("Video upload created:", payload.data);
        break;
      case "video.upload.created":
        console.log("Video upload created:", payload.data);
        break;
      default:
        console.log("Unknown webhook type:", payload.type);
    }

    return true;
  },
});

export const analyzeVideo = internalAction({
  args: {
    assetId: v.string(),
    trackId: v.string(),
    language: v.string(),
  },
  returns: v.union(v.string(), v.null()),
  handler: async (ctx, args) => {
    const mux = new Mux({
      tokenId: process.env.MUX_TOKEN_ID,
      tokenSecret: process.env.MUX_TOKEN_SECRET,
    });

    const asset = await mux.video.assets.retrieve(args.assetId);

    if (!asset.playback_ids) {
      return null;
    }

    const captions = await Promise.all(
      asset.playback_ids?.map(async (playbackId) => {
        const captions = await fetch(
          `https://stream.mux.com/${playbackId.id}/text/${args.trackId}.vtt`
        );
        const text = await captions.text();

        return text;
      })
    );

    console.log("transcript", captions);
    const chapters = await ctx.runAction(internal.gemini.generateChapters, {
      transcript: captions.join("\n"),
    });

    console.log("chapters", chapters);

    return captions.join("\n");
  },
});
