import { mutation } from "../_generated/server";
import { v } from "convex/values";

export const create = mutation({
  args: {
    // Define note creation arguments
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Note creation logic
  },
});

export const update = mutation({
  args: {
    // Define note update arguments
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Note update logic
  },
});

export const remove = mutation({
  args: {
    // Define note removal arguments
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Note removal logic
  },
});