import { query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

export const currentUser = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    const user = await ctx.db.get(userId);

    return {
      ...user,
      streak: "12",
      avgScore: "68%",
      activeCourses: 3,
      studyTime: "47h",
    };
  },
});
