/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as ai_actions from "../ai/actions.js";
import type * as ai_agent from "../ai/agent.js";
import type * as ai_workpools from "../ai/workpools.js";
import type * as auth from "../auth.js";
import type * as courses_mutations from "../courses/mutations.js";
import type * as courses_queries from "../courses/queries.js";
import type * as gemini from "../gemini.js";
import type * as http from "../http.js";
import type * as lectures_mutations from "../lectures/mutations.js";
import type * as lectures_queries from "../lectures/queries.js";
import type * as lib_mux from "../lib/mux.js";
import type * as lib_utils from "../lib/utils.js";
import type * as mux from "../mux.js";
import type * as notes_mutations from "../notes/mutations.js";
import type * as notes_queries from "../notes/queries.js";
import type * as payments_actions from "../payments/actions.js";
import type * as pdfs from "../pdfs.js";
import type * as progress_mutations from "../progress/mutations.js";
import type * as resender from "../resender.js";
import type * as users from "../users.js";
import type * as workflows from "../workflows.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  "ai/actions": typeof ai_actions;
  "ai/agent": typeof ai_agent;
  "ai/workpools": typeof ai_workpools;
  auth: typeof auth;
  "courses/mutations": typeof courses_mutations;
  "courses/queries": typeof courses_queries;
  gemini: typeof gemini;
  http: typeof http;
  "lectures/mutations": typeof lectures_mutations;
  "lectures/queries": typeof lectures_queries;
  "lib/mux": typeof lib_mux;
  "lib/utils": typeof lib_utils;
  mux: typeof mux;
  "notes/mutations": typeof notes_mutations;
  "notes/queries": typeof notes_queries;
  "payments/actions": typeof payments_actions;
  pdfs: typeof pdfs;
  "progress/mutations": typeof progress_mutations;
  resender: typeof resender;
  users: typeof users;
  workflows: typeof workflows;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
