import { Resend } from "resend";
import { internalAction } from "./_generated/server";
import { v } from "convex/values";

export const sendEmail = internalAction({
  args: {
    email: v.string(),
    subject: v.string(),
    html: v.string(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const resend = new Resend(process.env.RESEND_API_KEY!);

    const data = await resend.emails.send({
      from: "<EMAIL>",
      to: args.email,
      subject: args.subject,
      html: args.html,
    });

    console.log("Email sent", data);
  },
});
