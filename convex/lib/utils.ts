import { v } from "convex/values";

export const generateId = () => {
  // Generate unique ID utility
};

export const validateEmail = (email: string): boolean => {
  // Email validation utility
  return true;
};

export const formatDate = (date: Date): string => {
  // Date formatting utility
  return date.toISOString();
};

export const slugify = (text: string): string => {
  // Text slugification utility
  return text.toLowerCase().replace(/\s+/g, '-');
};