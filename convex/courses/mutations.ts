import { mutation } from "../_generated/server";
import { v } from "convex/values";

export const create = mutation({
  args: {
    // Define course creation arguments
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Course creation logic
  },
});

export const update = mutation({
  args: {
    // Define course update arguments
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Course update logic
  },
});

export const remove = mutation({
  args: {
    // Define course removal arguments
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Course removal logic
  },
});
