import { httpRouter } from "convex/server";
import { auth } from "./auth";
import { httpAction } from "./_generated/server";
import { Id } from "./_generated/dataModel";
import { internal } from "./_generated/api";

const http = httpRouter();

http.route({
  path: "/mux-webhook",
  method: "POST",
  handler: httpAction(async (ctx, req) => {
    const body = await req.json();
    const headers: Record<string, string> = {};

    for (const [key, value] of req.headers.entries()) {
      headers[key] = value;
    }

    const isValid = await ctx.runAction(internal.mux.verifySignature, {
      body: JSON.stringify(body),
      headers,
      secret: process.env.MUX_WEBHOOK_SECRET!,
    });

    if (isValid) {
      await ctx.runAction(internal.mux.handleWebhook, {
        payload: body,
      });
    }

    return new Response(null, { status: 202 });
  }),
});

auth.addHttpRoutes(http);

export default http;
