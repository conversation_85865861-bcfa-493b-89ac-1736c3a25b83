import { mutation } from "../_generated/server";
import { v } from "convex/values";

export const updateProgress = mutation({
  args: {
    // Define progress update arguments
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Update progress logic
  },
});

export const markComplete = mutation({
  args: {
    // Define completion marking arguments
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Mark item complete logic
  },
});

export const resetProgress = mutation({
  args: {
    // Define progress reset arguments
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Reset progress logic
  },
});