import { convexAuth } from "@convex-dev/auth/server";
import { Password } from "@convex-dev/auth/providers/Password";
import { DataModel } from "./_generated/dataModel";

export const { auth, signIn, signOut, store, isAuthenticated } = convexAuth({
  providers: [
    Password<DataModel>({
      profile(params) {
        return {
          email: params.email as string,
          firstName: params.firstName as string,
          lastName: params.lastName as string,
          name: `${params.firstName} ${params.lastName}`,
          invitationCode: params.invitationCode as string,
          flow: params.flow as "signUp" | "signIn",
        };
      },
    }),
  ],
  callbacks: {
    async createOrUpdateUser(ctx, args) {
      /**
       * In order for a user to exist, it should also have the auth account setup
       * At that point, we're 100% sure the user went through the signup flow
       */
      if (args.existingUserId) {
        return args.existingUserId;
      }

      /**
       * In case we invited a user, but they didn't sign up, we need to update their account
       */
      const existingUser = await ctx.db
        .query("users")
        .withIndex("by_email", (q) => q.eq("email", args.profile.email))
        .unique();

      let userId = existingUser?._id;

      /**
       * Most likely created via invitation flow!
       */
      if (userId) {
        console.log("patching", userId);

        await ctx.db.patch(userId, {
          firstName: args.profile.firstName,
          lastName: args.profile.lastName,
          name: args.profile.name,
          email: args.profile.email,
          lastActiveAt: Date.now(),
          status: "active",
        });
      } else {
        console.log("inserting", args.profile.email);

        userId = await ctx.db.insert("users", {
          firstName: args.profile.firstName,
          lastName: args.profile.lastName,
          name: args.profile.name,
          email: args.profile.email,
          status: "active",
          role: "student",
          lastActiveAt: Date.now(),
        });

        console.log("inserted userId-->", userId);
        console.log("Final userId-->", userId);
      }

      // This can never happen!
      if (!userId) {
        throw new Error("We couldn't create a user for some reason");
      }

      return userId;
    },
  },
});
