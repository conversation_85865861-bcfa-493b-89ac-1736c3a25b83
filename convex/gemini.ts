import { internalAction } from "./_generated/server";
import { createGoogleGenerativeAI } from "@ai-sdk/google";
import { generateObject } from "ai";
import { z } from "zod";

import { v } from "convex/values";

export const generateChapters = internalAction({
  args: {
    transcript: v.string(),
  },
  returns: v.array(
    v.object({
      title: v.string(),
      time: v.string(),
    })
  ),
  handler: async (ctx, args: { transcript: string }) => {
    const google = createGoogleGenerativeAI({
      apiKey: process.env.GEMINI_API_KEY,
    });

    const userMessage = `
      Analyze this VTT transcript and generate chapters:

      ${args.transcript}

      Generate chapters that capture the main topics and natural flow of this content. Focus on creating meaningful navigation points that viewers would want to jump to.
    `;

    const result = await generateObject({
      model: google("models/gemini-2.5-flash-preview-04-17"),
      system: `You are a video chapter generation expert. Your task is to analyze a VTT (WebVTT) transcript and create meaningful chapters for video navigation.

        INSTRUCTIONS:
        1. Analyze the provided VTT transcript to understand content flow and topic changes
        2. Create 3-12 chapters that represent logical content sections
        3. Choose chapter break points at natural topic transitions, not mid-sentence
        4. Each chapter should be at least 30 seconds long
        5. Chapter titles should be concise, descriptive, and engaging (max 60 characters)
        6. Convert VTT timestamps (HH:MM:SS.mmm) to HH:MM:SS format
        7. Ensure chapters are in chronological order
        8. Focus on major topics, not minor details

        CHAPTER TITLE GUIDELINES:
        - Use title case (e.g., "Introduction to Machine Learning")
        - Be specific and descriptive
        - Avoid generic titles like "Part 1" or "Section A"
        - Include key topics or names when relevant
        - Keep under 60 characters

        TIMESTAMP CONVERSION:
        - Convert HH:MM:SS.mmm to HH:MM:SS
        - Example: 00:02:30.500 = 00:02:30
        - Use the start time of the segment where the new topic begins

        Return only valid JSON matching the provided schema.
      `,
      messages: [
        {
          role: "user",
          content: userMessage,
        },
      ],
      temperature: 0.3,
      topP: 0.9,
      // frequencyPenalty: 0.1,
      // presencePenalty: 0.1,
      schema: z.array(
        z.object({
          title: z.string({
            description: "Descriptive chapter title (max 60 characters)",
          }),
          time: z.string({
            description:
              "Chapter start time in HH:MM:SS format (e.g. 00:02:30 for 2 minutes and 30 seconds)",
          }),
        })
      ),
    });

    return result.object;
  },
});
