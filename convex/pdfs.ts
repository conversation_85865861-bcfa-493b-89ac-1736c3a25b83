import { internalAction } from "./_generated/server";
import { v } from "convex/values";
import { GoogleGenAI } from "@google/genai";

const ai = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });

export const uploadPdf = internalAction({
  args: {
    fileId: v.string(),
    url: v.string(),
  },
  returns: v.object({
    uri: v.optional(v.string()),
    mimeType: v.optional(v.string()),
  }),
  handler: async (ctx, args) => {
    const pdfBuffer = await fetch(args.url).then((response) =>
      response.arrayBuffer()
    );

    const fileBlob = new Blob([pdfBuffer], { type: "application/pdf" });
    console.log("Uplaoding pdf......");
    const file = await ai.files.upload({
      file: fileBlob,
      config: {
        displayName: "FashionColors.pdf",
        name: `${args.fileId}`,
      },
    });
    console.log("Pdf uploaded, processing");

    if (!file) {
      throw new Error("Failed to upload file");
    }

    // Wait for the file to be processed.
    let getFile = await ai.files.get({ name: file.name ?? args.fileId });
    while (getFile.state === "PROCESSING") {
      getFile = await ai.files.get({ name: file.name ?? args.fileId });
      console.log(`current file status: ${getFile.state}`);
      console.log("File is still processing, retrying in 5 seconds");

      await new Promise((resolve) => {
        setTimeout(resolve, 5000);
      });
    }

    if (file.state === "FAILED") {
      throw new Error("File processing failed.");
    }
    console.log("File processed");

    return {
      uri: file.uri,
      mimeType: file.mimeType,
    };
  },
});
