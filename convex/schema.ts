import { defineSchema, defineTable } from "convex/server";
import { authTables } from "@convex-dev/auth/server";
import { v } from "convex/values";

const schema = defineSchema({
  ...authTables,
  // =================================================================
  // USERS, ACCESS, & PAYMENTS
  // =================================================================

  users: defineTable({
    firstName: v.string(),
    lastName: v.string(),
    name: v.string(),
    email: v.string(),
    role: v.optional(
      v.union(
        v.literal("student"),
        v.literal("creator"),
        v.literal("superadmin") // internal purposes
      )
    ),
    profileImage: v.optional(v.string()),
    lastLoginAt: v.optional(v.number()),
    lastActiveAt: v.optional(v.number()),
    status: v.optional(
      v.union(v.literal("active"), v.literal("invited"), v.literal("disabled"))
    ),
    lastAccessedResource: v.optional(v.id("resources")),
    muxAccountId: v.optional(v.string()), // For content creators
  })
    .index("by_email", ["email"])
    .index("by_muxAccountId", ["muxAccountId"]),

  // Tracks daily study streaks
  studyStreaks: defineTable({
    userId: v.id("users"),
    currentStreak: v.number(),
    longestStreak: v.number(),
    lastStudiedAt: v.number(),
  }).index("by_user", ["userId"]),

  // From schema 1: Tracks one-time course purchases.
  purchases: defineTable({
    userId: v.id("users"),
    courseId: v.id("courses"),
    amount: v.number(),
    purchaseDate: v.number(),
    stripePaymentIntentId: v.optional(v.string()),
  }).index("by_user_and_course", ["userId", "courseId"]),

  // From schema 1: Tracks subscription status.
  subscriptions: defineTable({
    userId: v.id("users"),
    planId: v.string(), // e.g., "basic", "premium"
    status: v.string(), // e.g., "active", "canceled"
    currentPeriodEnd: v.number(),
    stripeSubscriptionId: v.optional(v.string()),
  }).index("by_user", ["userId"]),

  // =================================================================
  // COURSES & CONTENT
  // =================================================================

  courses: defineTable({
    creatorId: v.id("users"),
    generatedTitle: v.optional(v.string()), // AI-generated
    description: v.optional(v.string()), // AI-generated
    summary: v.optional(v.string()), // AI-generated
    keyConcepts: v.optional(v.array(v.string())), // AI-generated
    price: v.number(),
    thumbnailUrl: v.optional(v.string()), // For course cards
    publishedAt: v.optional(v.number()), // For sorting
    // Status is critical for the workflow and UI to track the overall state.
    status: v.union(
      v.literal("processing"), // Workflow is active
      v.literal("published"), // Workflow completed
      v.literal("failed"), // Workflow failed
      v.literal("archived")
    ),
    // Aggregate data, updated by the final step of the workflow.
    lectureCount: v.optional(v.number()),
    totalDurationSeconds: v.optional(v.number()),
  }).index("by_creator", ["creatorId"]),

  lectures: defineTable({
    courseId: v.id("courses"),
    creatorId: v.id("users"),
    generatedTitle: v.optional(v.string()), // AI-generated
    order: v.number(),
    type: v.union(v.literal("video"), v.literal("audio"), v.literal("pdf")),
    // Status tracks the progress of each *individual* task in the work pool.
    status: v.union(
      v.literal("pending"), // Waiting to be processed by the work pool
      v.literal("processing"), // Actively being processed
      v.literal("completed"), // Analysis successful
      v.literal("failed")
    ),
    // --- Data & AI content ---
    durationSeconds: v.optional(v.number()),
    muxAssetId: v.optional(v.string()),
    muxPlaybackId: v.optional(v.string()),
    transcript: v.optional(v.string()),
    summary: v.optional(v.string()),
    insights: v.optional(v.array(v.string())),
    url: v.optional(v.string()), // For external content other than MUX
    fileId: v.optional(v.id("_storage")), // For internally uploaded content
    processingError: v.optional(v.string()), // If processing failed
  })
    .index("by_course", ["courseId"])
    .index("by_muxAssetId", ["muxAssetId"]),

  chapters: defineTable({
    // MERGED: Uses the more precise `startTime: v.number()` from schema 2.
    lectureId: v.id("lectures"),
    title: v.string(),
    startTime: v.number(), // in seconds
  }).index("by_lecture", ["lectureId"]),

  // NOTE: The `transcripts` table from schema 1 is omitted, as its `text` field
  // has been merged into the `lectures` table for better data locality for AI agents.

  // =================================================================
  // USER ENGAGEMENT & LEARNING TOOLS
  // =================================================================

  // From schema 1: Junction table to track enrollment and high-level progress.
  userCourses: defineTable({
    userId: v.id("users"),
    courseId: v.id("courses"),
    progress: v.number(), // Percentage completion
    status: v.string(), // "active", "completed", "paused"
    enrolledAt: v.number(), // For analytics
    lastAccessedAt: v.optional(v.number()), // For "Continue Learning"
  }).index("by_user_and_course", ["userId", "courseId"]),

  // Tracks per-lecture progress for a user
  userLectureProgress: defineTable({
    userId: v.id("users"),
    lectureId: v.id("lectures"),
    courseId: v.id("courses"),
    status: v.union(
      v.literal("not_started"),
      v.literal("in_progress"),
      v.literal("completed")
    ),
    progressSeconds: v.optional(v.number()),
  }).index("by_user_lecture", ["userId", "lectureId"]),

  // From schema 1: For student notes.
  notes: defineTable({
    userId: v.id("users"),
    courseId: v.id("courses"),
    lectureId: v.optional(v.id("lectures")),
    title: v.string(),
    content: v.string(),
    source: v.string(), // "manual", "ai_summary", "highlight"
    tags: v.array(v.string()),
    threadId: v.optional(v.id("threads")),
  }).index("by_user_course", ["userId", "courseId"]),

  // From schema 1: Quizzes and their questions.
  quizzes: defineTable({
    courseId: v.id("courses"),
    lectureId: v.optional(v.id("lectures")),
    title: v.string(),
    type: v.string(),
    threadId: v.optional(v.id("threads")),
  }).index("by_course", ["courseId"]),

  // Stores a user's attempt at a quiz
  quizAttempts: defineTable({
    userId: v.id("users"),
    quizId: v.id("quizzes"),
    score: v.number(), // Percentage
    startedAt: v.number(),
    completedAt: v.number(),
  }).index("by_user_quiz", ["userId", "quizId"]),

  // Stores a user's specific answer to a question in an attempt
  quizAnswers: defineTable({
    attemptId: v.id("quizAttempts"),
    questionId: v.id("questions"),
    userId: v.id("users"),
    selectedOptionIndex: v.number(),
    isCorrect: v.boolean(),
  }).index("by_attempt", ["attemptId"]),

  questions: defineTable({
    quizId: v.id("quizzes"),
    text: v.string(),
    type: v.string(),
    options: v.array(v.object({ text: v.string(), isCorrect: v.boolean() })),
    explanation: v.string(),
  }).index("by_quiz", ["quizId"]),

  // From schema 1: Flashcards and spaced repetition progress.
  flashcardDecks: defineTable({
    courseId: v.id("courses"),
    lectureId: v.optional(v.id("lectures")),
    title: v.string(),
    threadId: v.optional(v.id("threads")),
  }).index("by_course", ["courseId"]),

  flashcards: defineTable({
    deckId: v.id("flashcardDecks"),
    question: v.string(),
    answer: v.string(),
    explanation: v.optional(v.string()),
    source: v.optional(v.string()), // "ai_generated", "user_created"
  }).index("by_deck", ["deckId"]),

  userFlashcardProgress: defineTable({
    userId: v.id("users"),
    flashcardId: v.id("flashcards"),
    easeFactor: v.number(),
    interval: v.number(),
    dueDate: v.number(),
    lapses: v.number(),
  }).index("by_user_and_flashcard", ["userId", "flashcardId"]),

  // =================================================================
  // ACTIVITY, AI TUTOR, & RAG
  // =================================================================

  // From schema 1: A general log for user actions.
  userActivity: defineTable({
    userId: v.id("users"),
    activityType: v.string(),
    description: v.string(),
    courseId: v.optional(v.id("courses")),
    lectureId: v.optional(v.id("lectures")),
  }).index("by_user", ["userId"]),

  // From schema 1: Can be used for user-specific AI-generated feedback or study plans.
  aiInsights: defineTable({
    userId: v.id("users"),
    courseId: v.optional(v.id("courses")),
    insightType: v.string(),
    content: v.string(),
  }).index("by_user", ["userId"]),

  // Stores conversation history for the AI Tutor
  aiTutorConversations: defineTable({
    userId: v.id("users"),
    courseId: v.id("courses"),
    lectureId: v.optional(v.id("lectures")),
    messages: v.array(
      v.object({
        role: v.union(v.literal("user"), v.literal("assistant")),
        content: v.string(),
      })
    ),
  }).index("by_user_course", ["userId", "courseId"]),

  // Stores notifications for users
  notifications: defineTable({
    userId: v.id("users"),
    title: v.string(),
    body: v.string(),
    type: v.string(), // e.g., 'course_ready', 'ai_recommendation'
    isRead: v.boolean(),
    url: v.optional(v.string()),
  }).index("by_user", ["userId"]),

  // From schema 2: The core of the RAG system for the AI Tutor.
  courseEmbeddings: defineTable({
    courseId: v.id("courses"),
    lectureId: v.id("lectures"),
    textChunk: v.string(),
    embedding: v.array(v.float64()),
  })
    .index("by_course", ["courseId"])
    .vectorIndex("by_embedding", {
      vectorField: "embedding",
      dimensions: 768, // Adjust based on your embedding model, e.g., text-embedding-004
      filterFields: ["courseId"],
    }),
});

export default schema;
