# Tech Spec Extension: Mux Direct Upload Implementation

## Overview

This extension corrects the video upload flow to use Mux's direct upload functionality. Videos are uploaded directly to Mux from the client, bypassing Convex storage entirely. This approach is more efficient and leverages Mux's infrastructure for handling large video files.

## Updated Video Upload Flow

### 1. Direct Upload Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                   Video Upload Flow                         │
├─────────────────────────────────────────────────────────────┤
│  1. Client requests upload URL from Convex                  │
│  2. Convex creates Mux Direct Upload → returns upload URL   │
│  3. Client uploads video directly to Mux                    │
│  4. <PERSON><PERSON> processes video and sends webhooks to Convex        │
│  5. Convex processes webhooks and triggers AI workflows     │
└─────────────────────────────────────────────────────────────┘
```

## Updated Schema

### Additional Schema Tables

```typescript
// convex/schema.ts - Additional tables for Mux direct uploads

// Track Mux upload sessions
muxUploads: defineTable({
  courseId: v.id("courses"),
  uploadId: v.string(), // Mux upload ID
  uploadUrl: v.string(), // Direct upload URL
  assetId: v.optional(v.string()), // Populated after upload completes
  status: v.union(
    v.literal("waiting"), // Waiting for upload
    v.literal("asset_created"), // Asset created in Mux
    v.literal("ready"), // Processing complete
    v.literal("errored"),
    v.literal("cancelled"),
    v.literal("timed_out")
  ),
  fileName: v.string(),
  createdBy: v.id("users"),
  expiresAt: v.number(), // Upload URL expiration
}).index("by_upload_id", ["uploadId"])
  .index("by_course", ["courseId"]),
```

## Updated Implementation

### 1. Create Direct Upload Endpoint

```typescript
// convex/muxUploads.ts
import { mutation } from "./_generated/server";
import { v } from "convex/values";
import { muxClient } from "./mux/client";

export const createDirectUpload = mutation({
  args: {
    courseId: v.id("courses"),
    fileName: v.string(),
    fileSize: v.number(), // For validation
  },
  handler: async (ctx, { courseId, fileName, fileSize }) => {
    const userId = await getUserId(ctx);

    // Validate file size (e.g., max 5GB)
    if (fileSize > 5 * 1024 * 1024 * 1024) {
      throw new Error("File size exceeds 5GB limit");
    }

    // Validate user has permission to upload to this course
    const course = await ctx.db.get(courseId);
    if (!course || course.creatorId !== userId) {
      throw new Error("Unauthorized");
    }

    // Create Mux direct upload
    const upload = await muxClient.video.uploads.create({
      cors_origin: process.env.NEXT_PUBLIC_APP_URL,
      new_asset_settings: {
        playback_policy: ["public"],
        mp4_support: "standard",
        master_access: "temporary",
        // Enable automatic transcription
        generated_subtitles: [
          {
            language_code: "en",
            name: "English",
          },
        ],
        // Pass through identifiers for webhook handling
        passthrough: JSON.stringify({
          courseId,
          fileName,
          userId,
        }),
      },
      // Set timeout for upload (e.g., 24 hours)
      timeout: 86400,
    });

    // Store upload record
    await ctx.db.insert("muxUploads", {
      courseId,
      uploadId: upload.id,
      uploadUrl: upload.url,
      status: "waiting",
      fileName,
      createdBy: userId,
      expiresAt: Date.now() + 24 * 60 * 60 * 1000, // 24 hours
    });

    return {
      uploadId: upload.id,
      uploadUrl: upload.url,
    };
  },
});

// Clean up expired uploads
export const cleanupExpiredUploads = internalMutation({
  handler: async (ctx) => {
    const now = Date.now();
    const expired = await ctx.db
      .query("muxUploads")
      .filter((q) => q.lt(q.field("expiresAt"), now))
      .filter((q) => q.eq(q.field("status"), "waiting"))
      .collect();

    for (const upload of expired) {
      await ctx.db.patch(upload._id, { status: "timed_out" });
    }
  },
});
```

### 2. Updated Mux Webhook Handler

```typescript
// convex/webhooks.ts
import { httpAction } from "./_generated/server";
import { internal } from "./_generated/api";
import Webhooks from "@mux/webhooks";

const webhooks = new Webhooks.default(process.env.MUX_WEBHOOK_SECRET!);

export const muxWebhook = httpAction(async (ctx, request) => {
  try {
    // Get raw body for signature verification
    const body = await request.text();
    const signature = request.headers.get("mux-signature") || "";

    // Verify webhook signature
    const event = webhooks.verifyHeader(body, signature);

    // Store raw event
    await ctx.runMutation(internal.webhooks.storeMuxEvent, {
      muxEventId: event.id,
      type: event.type,
      assetId: event.data?.asset_id,
      data: JSON.stringify(event.data),
      status: "pending",
    });

    // Route to appropriate handler based on event type
    switch (event.type) {
      case "video.upload.asset_created":
        await ctx.runAction(internal.muxHandlers.handleAssetCreated, {
          uploadId: event.data.id,
          assetId: event.data.asset_id,
          passthrough: event.data.passthrough,
        });
        break;

      case "video.asset.ready":
        await ctx.runAction(internal.muxHandlers.handleAssetReady, {
          assetId: event.data.id,
          duration: event.data.duration,
          aspectRatio: event.data.aspect_ratio,
          playbackIds: event.data.playback_ids,
          passthrough: event.data.passthrough,
        });
        break;

      case "video.asset.created":
        // Asset created but not yet ready
        await ctx.runAction(internal.muxHandlers.handleAssetProcessing, {
          assetId: event.data.id,
          passthrough: event.data.passthrough,
        });
        break;

      case "video.asset.updated":
        // Check if subtitles are ready
        const tracks = event.data.tracks || [];
        const subtitleTrack = tracks.find(
          (t: any) => t.type === "text" && t.text_type === "subtitles"
        );

        if (subtitleTrack && subtitleTrack.status === "ready") {
          await ctx.runAction(internal.muxHandlers.handleSubtitlesReady, {
            assetId: event.data.id,
            trackId: subtitleTrack.id,
            playbackId: event.data.playback_ids?.[0]?.id,
            passthrough: event.data.passthrough,
          });
        }
        break;

      case "video.upload.cancelled":
      case "video.upload.errored":
      case "video.asset.errored":
        await ctx.runAction(internal.muxHandlers.handleError, {
          type: event.type,
          uploadId: event.data.id,
          assetId: event.data.asset_id,
          error: event.data.error,
          passthrough: event.data.passthrough,
        });
        break;
    }

    // Mark event as processed
    await ctx.runMutation(internal.webhooks.markEventProcessed, {
      muxEventId: event.id,
    });

    return new Response("OK", { status: 200 });
  } catch (error) {
    console.error("Webhook processing error:", error);
    return new Response("Webhook processing failed", { status: 400 });
  }
});
```

### 3. Mux Event Handlers

```typescript
// convex/muxHandlers.ts
import { internalAction, internalMutation } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";

export const handleAssetCreated = internalAction({
  args: {
    uploadId: v.string(),
    assetId: v.string(),
    passthrough: v.optional(v.string()),
  },
  handler: async (ctx, { uploadId, assetId, passthrough }) => {
    // Parse passthrough data
    const metadata = passthrough ? JSON.parse(passthrough) : {};

    // Update upload record
    await ctx.runMutation(internal.muxUploads.updateUploadStatus, {
      uploadId,
      status: "asset_created",
      assetId,
    });

    // Create lecture record
    const lectureId = await ctx.runMutation(internal.lectures.createLecture, {
      courseId: metadata.courseId,
      creatorId: metadata.userId,
      generatedTitle: metadata.fileName, // Temporary title
      order: await getNextLectureOrder(ctx, metadata.courseId),
      type: "video",
      status: "processing",
      muxAssetId: assetId,
    });

    // Start processing workflow
    await ctx.runAction(internal.workflows.startVideoProcessing, {
      lectureId,
      assetId,
      courseId: metadata.courseId,
    });
  },
});

export const handleAssetReady = internalAction({
  args: {
    assetId: v.string(),
    duration: v.number(),
    aspectRatio: v.optional(v.string()),
    playbackIds: v.array(
      v.object({
        id: v.string(),
        policy: v.string(),
      })
    ),
    passthrough: v.optional(v.string()),
  },
  handler: async (ctx, { assetId, duration, playbackIds, passthrough }) => {
    const metadata = passthrough ? JSON.parse(passthrough) : {};

    // Update lecture with video details
    await ctx.runMutation(internal.lectures.updateLectureVideoDetails, {
      muxAssetId: assetId,
      durationSeconds: duration,
      muxPlaybackId: playbackIds[0]?.id,
      status: "completed",
    });

    // Update upload status
    const upload = await ctx.runQuery(internal.muxUploads.getUploadByAssetId, {
      assetId,
    });

    if (upload) {
      await ctx.runMutation(internal.muxUploads.updateUploadStatus, {
        uploadId: upload.uploadId,
        status: "ready",
      });
    }

    // Check if all course videos are processed
    await ctx.runAction(internal.courses.checkCourseCompletion, {
      courseId: metadata.courseId,
    });
  },
});

export const handleSubtitlesReady = internalAction({
  args: {
    assetId: v.string(),
    trackId: v.string(),
    playbackId: v.string(),
    passthrough: v.optional(v.string()),
  },
  handler: async (ctx, { assetId, trackId, playbackId }) => {
    // Fetch transcript from Mux
    const transcriptUrl = `https://stream.mux.com/${playbackId}/text/${trackId}.vtt`;

    try {
      const response = await fetch(transcriptUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch transcript: ${response.statusText}`);
      }

      const vttContent = await response.text();
      const transcript = parseVTTToPlainText(vttContent);

      // Get lecture
      const lecture = await ctx.runQuery(
        internal.lectures.getLectureByMuxAssetId,
        {
          muxAssetId: assetId,
        }
      );

      if (!lecture) {
        throw new Error(`Lecture not found for asset ${assetId}`);
      }

      // Update lecture with transcript
      await ctx.runMutation(internal.lectures.updateLectureTranscript, {
        lectureId: lecture._id,
        transcript,
      });

      // Trigger AI processing for this lecture
      await ctx.runAction(internal.ai.processLectureContent, {
        lectureId: lecture._id,
        transcript,
      });

      // Generate embeddings for RAG
      await ctx.runAction(internal.ai.generateLectureEmbeddings, {
        lectureId: lecture._id,
        transcript,
      });
    } catch (error) {
      console.error("Error processing subtitles:", error);
      await ctx.runMutation(internal.lectures.updateLectureError, {
        muxAssetId: assetId,
        error: error.message,
      });
    }
  },
});

export const handleError = internalAction({
  args: {
    type: v.string(),
    uploadId: v.optional(v.string()),
    assetId: v.optional(v.string()),
    error: v.optional(
      v.object({
        type: v.string(),
        message: v.string(),
      })
    ),
    passthrough: v.optional(v.string()),
  },
  handler: async (ctx, { type, uploadId, assetId, error }) => {
    // Update upload status if we have uploadId
    if (uploadId) {
      await ctx.runMutation(internal.muxUploads.updateUploadStatus, {
        uploadId,
        status: "errored",
        error: error?.message,
      });
    }

    // Update lecture status if we have assetId
    if (assetId) {
      await ctx.runMutation(internal.lectures.updateLectureError, {
        muxAssetId: assetId,
        error: error?.message || `Error type: ${type}`,
      });
    }

    // Notify creator of the error
    const metadata = passthrough ? JSON.parse(passthrough) : {};
    if (metadata.userId) {
      await ctx.runMutation(internal.notifications.createNotification, {
        userId: metadata.userId,
        title: "Video Processing Error",
        body: `There was an error processing your video: ${
          error?.message || type
        }`,
        type: "error",
      });
    }
  },
});

// Helper function to parse VTT to plain text
function parseVTTToPlainText(vttContent: string): string {
  // Remove VTT header
  const lines = vttContent.split("\n");
  const textLines: string[] = [];

  let isTextLine = false;
  for (const line of lines) {
    // Skip header and timecodes
    if (line.startsWith("WEBVTT") || line.includes("-->")) {
      isTextLine = false;
      continue;
    }

    // Skip empty lines
    if (line.trim() === "") {
      isTextLine = true;
      continue;
    }

    // Collect text lines
    if (isTextLine) {
      textLines.push(line.trim());
    }
  }

  return textLines.join(" ");
}
```

### 4. Updated Course Creation Flow

```typescript
// convex/courses.ts
import { mutation } from "./_generated/server";
import { v } from "convex/values";

export const createCourse = mutation({
  args: {
    price: v.number(),
    files: v.array(
      v.object({
        fileName: v.string(),
        fileType: v.string(),
        fileSize: v.number(),
      })
    ),
  },
  handler: async (ctx, { price, files }) => {
    const userId = await getUserId(ctx);

    // Create course record
    const courseId = await ctx.db.insert("courses", {
      creatorId: userId,
      price,
      status: "processing",
      publishedAt: Date.now(),
      lectureCount: 0,
      totalDurationSeconds: 0,
    });

    // Process each file based on type
    const uploadPromises = files.map(async (file) => {
      if (file.fileType.startsWith("video/")) {
        // For videos, create Mux direct upload
        return await createDirectUpload(ctx, {
          courseId,
          fileName: file.fileName,
          fileSize: file.fileSize,
        });
      } else {
        // For non-video files, handle differently
        // Audio and PDF files might still go through Convex storage
        return await handleNonVideoFile(ctx, {
          courseId,
          file,
        });
      }
    });

    const uploads = await Promise.all(uploadPromises);

    return {
      courseId,
      uploads: uploads.filter(Boolean),
    };
  },
});

// Helper to handle non-video files
async function handleNonVideoFile(ctx: any, { courseId, file }: any) {
  // This would handle audio and PDF files
  // Implementation depends on your requirements
  // Could use Convex storage or other services
  return null;
}
```

### 5. Client-Side Upload Implementation

```typescript
// Example Next.js component for video upload
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";

export function VideoUploader({ courseId }: { courseId: string }) {
  const createUpload = useMutation(api.muxUploads.createDirectUpload);

  const handleVideoUpload = async (file: File) => {
    try {
      // Step 1: Request upload URL from Convex
      const { uploadUrl } = await createUpload({
        courseId,
        fileName: file.name,
        fileSize: file.size,
      });

      // Step 2: Upload directly to Mux
      const response = await fetch(uploadUrl, {
        method: "PUT",
        body: file,
        headers: {
          "Content-Type": file.type,
        },
      });

      if (!response.ok) {
        throw new Error("Upload failed");
      }

      // Step 3: Upload complete - Mux will process and send webhooks
      console.log("Upload successful! Processing will begin shortly.");
    } catch (error) {
      console.error("Upload error:", error);
    }
  };

  return (
    <div>
      <input
        type="file"
        accept="video/*"
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) handleVideoUpload(file);
        }}
      />
    </div>
  );
}
```

### 6. Monitoring Upload Progress

```typescript
// convex/queries.ts
import { query } from "./_generated/server";
import { v } from "convex/values";

export const getCourseUploadStatus = query({
  args: { courseId: v.id("courses") },
  handler: async (ctx, { courseId }) => {
    const uploads = await ctx.db
      .query("muxUploads")
      .withIndex("by_course", (q) => q.eq("courseId", courseId))
      .collect();

    const lectures = await ctx.db
      .query("lectures")
      .withIndex("by_course", (q) => q.eq("courseId", courseId))
      .collect();

    return {
      uploads: uploads.map((u) => ({
        fileName: u.fileName,
        status: u.status,
        uploadId: u.uploadId,
        error: u.error,
      })),
      lectures: lectures.map((l) => ({
        title: l.generatedTitle,
        status: l.status,
        duration: l.durationSeconds,
        hasTranscript: !!l.transcript,
      })),
      overallProgress: calculateOverallProgress(uploads, lectures),
    };
  },
});

function calculateOverallProgress(uploads: any[], lectures: any[]): number {
  const totalFiles = uploads.length;
  if (totalFiles === 0) return 100;

  const completedLectures = lectures.filter(
    (l) => l.status === "completed"
  ).length;
  return Math.round((completedLectures / totalFiles) * 100);
}
```

## Key Improvements

1. **Direct Upload**: Videos go straight from client to Mux, reducing server load
2. **Webhook-Driven**: All processing is triggered by Mux webhooks, ensuring reliability
3. **Progress Tracking**: Clear status tracking from upload through processing
4. **Error Handling**: Comprehensive error handling for upload and processing failures
5. **Automatic Transcription**: Leverages Mux's built-in transcription capabilities

## Benefits of This Approach

- **Scalability**: No video data passes through your servers
- **Reliability**: Mux handles retries and processing
- **Cost-Effective**: No storage costs for video files in Convex
- **Performance**: Faster uploads using Mux's optimized infrastructure
- **Simplicity**: Less code to maintain for video handling
