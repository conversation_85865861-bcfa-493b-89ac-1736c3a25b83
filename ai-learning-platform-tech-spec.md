# AI-Assisted Agentic Learning Platform - Backend Technical Specification

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Schema Updates](#schema-updates)
4. [Content Creator Workflow](#content-creator-workflow)
5. [Course Consumer Experience](#course-consumer-experience)
6. [AI Agent System](#ai-agent-system)
7. [Implementation Details](#implementation-details)
8. [Security & Performance](#security-performance)

## Overview

This platform revolutionizes online learning by leveraging AI agents throughout the content creation and consumption lifecycle. The system uses Convex for backend infrastructure, Mux for video processing, and Vercel AI SDK for intelligent features.

### Key Technologies

- **Convex**: Backend-as-a-Service with real-time capabilities
- **Mux**: Video hosting, encoding, and streaming
- **Vercel AI SDK**: AI model integration
- **Next.js 15**: Frontend framework
- **TypeScript**: Type-safe development

### Core Features

- Automated content processing and enrichment
- AI-powered tutoring and personalization
- Adaptive learning recommendations
- Real-time progress tracking
- Interactive learning tools (quizzes, flashcards, notes)

## Architecture

### Component Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                      Frontend (Next.js 15)                  │
├─────────────────────────────────────────────────────────────┤
│                      Convex Backend                         │
│  ┌─────────────┐  ┌──────────────┐  ┌──────────────────┐    │
│  │  Workflows  │  │  Work Pools  │  │   AI Agents      │    │
│  └─────────────┘  └──────────────┘  └──────────────────┘    │
│  ┌─────────────┐  ┌──────────────┐  ┌──────────────────┐    │
│  │ Aggregations│  │  Real-time   │  │   Embeddings     │    │
│  └─────────────┘  └──────────────┘  └──────────────────┘    │
├─────────────────────────────────────────────────────────────┤
│               External Services                             │
│  ┌─────────────┐  ┌──────────────┐  ┌──────────────────┐    │
│  │     Mux     │  │  OpenAI/     │  │     Stripe       │    │
│  │             │  │  Anthropic   │  │                  │    │
│  └─────────────┘  └──────────────┘  └──────────────────┘    │
└─────────────────────────────────────────────────────────────┘
```

## Schema Updates

### Proposed Schema Additions

```typescript
// convex/schema.ts - Additions to existing schema

// Track workflow execution for courses
courseWorkflows: defineTable({
  courseId: v.id("courses"),
  workflowId: v.string(), // Workflow component ID
  status: v.union(
    v.literal("running"),
    v.literal("completed"),
    v.literal("failed"),
    v.literal("cancelled")
  ),
  startedAt: v.number(),
  completedAt: v.optional(v.number()),
  error: v.optional(v.string()),
}).index("by_course", ["courseId"]),

// Track individual file uploads and processing
courseFiles: defineTable({
  courseId: v.id("courses"),
  fileId: v.id("_storage"),
  fileName: v.string(),
  fileType: v.string(), // MIME type
  fileSize: v.number(),
  status: v.union(
    v.literal("uploaded"),
    v.literal("processing"),
    v.literal("processed"),
    v.literal("failed")
  ),
  muxUploadId: v.optional(v.string()), // For direct uploads to Mux
  processingJobId: v.optional(v.string()), // Work pool job ID
}).index("by_course", ["courseId"]),

// Store AI-generated learning paths
learningPaths: defineTable({
  userId: v.id("users"),
  courseId: v.id("courses"),
  currentLectureId: v.id("lectures"),
  recommendedNextLectureId: v.optional(v.id("lectures")),
  learningStyle: v.string(), // e.g., "visual", "auditory"
  optimalTimeOfDay: v.optional(v.string()), // e.g., "afternoon"
  generatedAt: v.number(),
}).index("by_user_course", ["userId", "courseId"]),

// Track learning analytics
learningAnalytics: defineTable({
  userId: v.id("users"),
  weekStartDate: v.number(),
  hoursStudied: v.number(),
  lecturesCompleted: v.number(),
  quizzesTaken: v.number(),
  averageQuizScore: v.number(),
  retentionRate: v.number(), // Based on quiz performance over time
}).index("by_user_week", ["userId", "weekStartDate"]),

// Mux webhook events
muxWebhookEvents: defineTable({
  muxEventId: v.string(),
  type: v.string(),
  assetId: v.optional(v.string()),
  data: v.string(), // JSON stringified event data
  processedAt: v.optional(v.number()),
  status: v.union(
    v.literal("pending"),
    v.literal("processed"),
    v.literal("failed")
  ),
}).index("by_mux_event", ["muxEventId"]),
```

## Content Creator Workflow

### 1. Course Creation Flow

```typescript
// convex/courses.ts
import { mutation } from "./_generated/server";
import { v } from "convex/values";
import { workflow } from "./workflow";

export const createCourse = mutation({
  args: {
    price: v.number(),
    files: v.array(
      v.object({
        fileId: v.id("_storage"),
        fileName: v.string(),
        fileType: v.string(),
        fileSize: v.number(),
      })
    ),
  },
  handler: async (ctx, { price, files }) => {
    // Create course record
    const courseId = await ctx.db.insert("courses", {
      creatorId: await getUserId(ctx),
      price,
      status: "processing",
      publishedAt: Date.now(),
    });

    // Store file references
    for (const file of files) {
      await ctx.db.insert("courseFiles", {
        courseId,
        ...file,
        status: "uploaded",
      });
    }

    // Start the main course processing workflow
    const workflowId = await workflow.start(
      ctx,
      internal.workflows.processCourse,
      { courseId }
    );

    // Track workflow
    await ctx.db.insert("courseWorkflows", {
      courseId,
      workflowId,
      status: "running",
      startedAt: Date.now(),
    });

    return courseId;
  },
});
```

### 2. Main Course Processing Workflow

```typescript
// convex/workflows.ts
import { WorkflowManager } from "@convex-dev/workflow";
import { Workpool } from "@convex-dev/workpool";
import { components } from "./_generated/api";
import { internal } from "./_generated/api";

export const workflow = new WorkflowManager(components.workflow);

// Define work pools for different file types
export const videoPool = new Workpool(components.videoWorkpool, {
  maxParallelism: 3,
  defaultRetryBehavior: {
    maxAttempts: 3,
    initialBackoffMs: 1000,
    base: 2,
  },
});

export const audioPool = new Workpool(components.audioWorkpool, {
  maxParallelism: 5,
});

export const pdfPool = new Workpool(components.pdfWorkpool, {
  maxParallelism: 10,
});

export const processCourse = workflow.define({
  args: { courseId: v.id("courses") },
  handler: async (step, { courseId }) => {
    // Step 1: Get all files for the course
    const files = await step.runQuery(internal.courses.getCourseFiles, {
      courseId,
    });

    // Step 2: Process files in parallel by type
    const processingJobs = await Promise.all(
      files.map(async (file) => {
        if (file.fileType.startsWith("video/")) {
          return await step.runAction(
            internal.workflows.enqueueVideoProcessing,
            { courseId, fileId: file._id }
          );
        } else if (file.fileType.startsWith("audio/")) {
          return await step.runAction(
            internal.workflows.enqueueAudioProcessing,
            { courseId, fileId: file._id }
          );
        } else if (file.fileType === "application/pdf") {
          return await step.runAction(internal.workflows.enqueuePdfProcessing, {
            courseId,
            fileId: file._id,
          });
        }
      })
    );

    // Step 3: Wait for all processing to complete
    await step.runAction(internal.workflows.waitForProcessing, {
      jobIds: processingJobs.filter(Boolean),
    });

    // Step 4: Generate course metadata
    await step.runAction(internal.ai.generateCourseMetadata, { courseId });

    // Step 5: Generate embeddings for RAG
    await step.runAction(internal.ai.generateCourseEmbeddings, { courseId });

    // Step 6: Update course status
    await step.runMutation(internal.courses.updateCourseStatus, {
      courseId,
      status: "published",
    });

    // Step 7: Notify creator
    await step.runAction(internal.notifications.notifyCreator, {
      courseId,
      message: "Your course is ready!",
    });
  },
});
```

### 3. Video Processing Workflow

```typescript
// convex/videoProcessing.ts
import { internalAction, internalMutation } from "./_generated/server";
import { Mux } from "@mux/mux-node";
import { v } from "convex/values";

const mux = new Mux({
  tokenId: process.env.MUX_TOKEN_ID!,
  tokenSecret: process.env.MUX_TOKEN_SECRET!,
});

export const processVideo = internalAction({
  args: {
    courseId: v.id("courses"),
    fileId: v.id("courseFiles"),
    storageId: v.id("_storage"),
  },
  handler: async (ctx, { courseId, fileId, storageId }) => {
    try {
      // Step 1: Get file URL
      const fileUrl = await ctx.storage.getUrl(storageId);
      if (!fileUrl) throw new Error("File not found");

      // Step 2: Create Mux asset with captions
      const asset = await mux.video.assets.create({
        input: fileUrl,
        playback_policy: ["public"],
        mp4_support: "standard",
        master_access: "temporary",
        generate_subtitles: {
          language_code: "en",
          name: "English",
          transcription_vocabulary: [], // Add domain-specific terms
        },
      });

      // Step 3: Create lecture record
      const lectureId = await ctx.runMutation(internal.lectures.createLecture, {
        courseId,
        type: "video",
        status: "processing",
        muxAssetId: asset.id,
        muxPlaybackId: asset.playback_ids?.[0]?.id,
      });

      // Step 4: Update file status
      await ctx.runMutation(internal.courses.updateFileStatus, {
        fileId,
        status: "processed",
      });

      return { lectureId, assetId: asset.id };
    } catch (error) {
      await ctx.runMutation(internal.courses.updateFileStatus, {
        fileId,
        status: "failed",
        error: error.message,
      });
      throw error;
    }
  },
});

// Mux webhook handler
export const handleMuxWebhook = internalAction({
  args: {
    eventId: v.string(),
    type: v.string(),
    data: v.any(),
  },
  handler: async (ctx, { eventId, type, data }) => {
    // Store webhook event
    await ctx.runMutation(internal.webhooks.storeMuxEvent, {
      eventId,
      type,
      data: JSON.stringify(data),
    });

    // Process based on event type
    switch (type) {
      case "video.asset.track.ready":
        if (data.track.type === "subtitles") {
          await ctx.runAction(internal.videoProcessing.processTranscript, {
            assetId: data.asset_id,
            trackId: data.track.id,
            playbackId: data.playback_ids[0].id,
          });
        }
        break;

      case "video.asset.ready":
        await ctx.runMutation(internal.lectures.updateLectureStatus, {
          muxAssetId: data.id,
          status: "completed",
          durationSeconds: data.duration,
        });
        break;
    }
  },
});

export const processTranscript = internalAction({
  args: {
    assetId: v.string(),
    trackId: v.string(),
    playbackId: v.string(),
  },
  handler: async (ctx, { assetId, trackId, playbackId }) => {
    // Fetch VTT transcript from Mux
    const transcriptUrl = `https://stream.mux.com/${playbackId}/text/${trackId}.vtt`;
    const response = await fetch(transcriptUrl);
    const vttContent = await response.text();

    // Parse VTT to plain text
    const transcript = parseVTT(vttContent);

    // Update lecture with transcript
    const lecture = await ctx.runQuery(
      internal.lectures.getLectureByMuxAssetId,
      { muxAssetId: assetId }
    );

    if (lecture) {
      await ctx.runMutation(internal.lectures.updateLectureTranscript, {
        lectureId: lecture._id,
        transcript,
      });

      // Trigger AI processing
      await ctx.runAction(internal.ai.processLectureContent, {
        lectureId: lecture._id,
        transcript,
      });
    }
  },
});
```

### 4. AI Content Processing

```typescript
// convex/ai.ts
import { internalAction, internalMutation } from "./_generated/server";
import { openai } from "@ai-sdk/openai";
import { generateText, generateObject } from "ai";
import { z } from "zod";
import { v } from "convex/values";

export const processLectureContent = internalAction({
  args: {
    lectureId: v.id("lectures"),
    transcript: v.string(),
  },
  handler: async (ctx, { lectureId, transcript }) => {
    // Generate summary
    const summaryResult = await generateText({
      model: openai("gpt-4o"),
      system: "You are an expert educational content analyzer.",
      prompt: `Summarize this lecture transcript in 2-3 concise paragraphs:\n\n${transcript}`,
    });

    // Generate insights
    const insightsResult = await generateObject({
      model: openai("gpt-4o"),
      schema: z.object({
        insights: z.array(z.string()).max(5),
        keyTakeaways: z.array(z.string()).max(5),
      }),
      prompt: `Extract key insights and takeaways from this lecture:\n\n${transcript}`,
    });

    // Generate chapters
    const chaptersResult = await generateObject({
      model: openai("gpt-4o"),
      schema: z.object({
        chapters: z.array(
          z.object({
            title: z.string(),
            startTime: z.number(), // in seconds
            description: z.string(),
          })
        ),
      }),
      prompt: `Create chapter markers for this video transcript. Include timestamps:\n\n${transcript}`,
    });

    // Generate title and description
    const metadataResult = await generateObject({
      model: openai("gpt-4o"),
      schema: z.object({
        title: z.string(),
        description: z.string().max(200),
      }),
      prompt: `Generate a compelling title and brief description for this lecture:\n\n${transcript.substring(
        0,
        1000
      )}`,
    });

    // Update lecture with AI-generated content
    await ctx.runMutation(internal.lectures.updateLectureAIContent, {
      lectureId,
      generatedTitle: metadataResult.object.title,
      summary: summaryResult.text,
      insights: insightsResult.object.insights,
    });

    // Create chapters
    for (const chapter of chaptersResult.object.chapters) {
      await ctx.runMutation(internal.chapters.createChapter, {
        lectureId,
        ...chapter,
      });
    }
  },
});

export const generateCourseMetadata = internalAction({
  args: { courseId: v.id("courses") },
  handler: async (ctx, { courseId }) => {
    // Get all lectures for the course
    const lectures = await ctx.runQuery(internal.lectures.getLecturesByCourse, {
      courseId,
    });

    // Aggregate summaries
    const aggregatedContent = lectures
      .map((l) => l.summary || "")
      .filter(Boolean)
      .join("\n\n");

    // Generate course-level metadata
    const result = await generateObject({
      model: openai("gpt-4o"),
      schema: z.object({
        title: z.string(),
        description: z.string(),
        summary: z.string(),
        keyConcepts: z.array(z.string()).max(10),
      }),
      prompt: `Based on these lecture summaries, generate comprehensive course metadata:\n\n${aggregatedContent}`,
    });

    // Update course
    await ctx.runMutation(internal.courses.updateCourseMetadata, {
      courseId,
      generatedTitle: result.object.title,
      description: result.object.description,
      summary: result.object.summary,
      keyConcepts: result.object.keyConcepts,
      lectureCount: lectures.length,
      totalDurationSeconds: lectures.reduce(
        (sum, l) => sum + (l.durationSeconds || 0),
        0
      ),
    });
  },
});

export const generateCourseEmbeddings = internalAction({
  args: { courseId: v.id("courses") },
  handler: async (ctx, { courseId }) => {
    const lectures = await ctx.runQuery(internal.lectures.getLecturesByCourse, {
      courseId,
    });

    for (const lecture of lectures) {
      if (!lecture.transcript) continue;

      // Split transcript into chunks
      const chunks = splitIntoChunks(lecture.transcript, 500); // 500 tokens per chunk

      for (const chunk of chunks) {
        // Generate embedding
        const { embedding } = await openai
          .embedding("text-embedding-3-small")
          .doEmbed({ values: [chunk] });

        // Store embedding
        await ctx.runMutation(internal.embeddings.storeEmbedding, {
          courseId,
          lectureId: lecture._id,
          textChunk: chunk,
          embedding: embedding[0],
        });
      }
    }
  },
});

function splitIntoChunks(text: string, maxTokens: number): string[] {
  // Simple implementation - in production, use a proper tokenizer
  const words = text.split(" ");
  const chunks: string[] = [];
  let currentChunk = "";

  for (const word of words) {
    if ((currentChunk + " " + word).length > maxTokens * 4) {
      // Rough estimate
      chunks.push(currentChunk.trim());
      currentChunk = word;
    } else {
      currentChunk += " " + word;
    }
  }

  if (currentChunk) {
    chunks.push(currentChunk.trim());
  }

  return chunks;
}
```

## Course Consumer Experience

### 1. Course Page Components

```typescript
// convex/userCourses.ts
import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

export const getCourseProgress = query({
  args: { courseId: v.id("courses") },
  handler: async (ctx, { courseId }) => {
    const userId = await getUserId(ctx);

    // Get user's course enrollment
    const enrollment = await ctx.db
      .query("userCourses")
      .withIndex("by_user_and_course", (q) =>
        q.eq("userId", userId).eq("courseId", courseId)
      )
      .first();

    // Get lecture progress
    const lectureProgress = await ctx.db
      .query("userLectureProgress")
      .withIndex("by_user_lecture", (q) => q.eq("userId", userId))
      .collect();

    // Get learning analytics
    const currentWeek = getWeekStart(new Date());
    const analytics = await ctx.db
      .query("learningAnalytics")
      .withIndex("by_user_week", (q) =>
        q.eq("userId", userId).eq("weekStartDate", currentWeek)
      )
      .first();

    // Get adaptive recommendations
    const learningPath = await ctx.db
      .query("learningPaths")
      .withIndex("by_user_course", (q) =>
        q.eq("userId", userId).eq("courseId", courseId)
      )
      .first();

    return {
      enrollment,
      lectureProgress,
      analytics,
      learningPath,
    };
  },
});

export const getAdaptiveRecommendations = query({
  args: { courseId: v.id("courses") },
  handler: async (ctx, { courseId }) => {
    const userId = await getUserId(ctx);

    // Get user activity patterns
    const activities = await ctx.db
      .query("userActivity")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .order("desc")
      .take(100);

    // Analyze patterns
    const patterns = analyzeActivityPatterns(activities);

    // Get learning path
    const learningPath = await ctx.db
      .query("learningPaths")
      .withIndex("by_user_course", (q) =>
        q.eq("userId", userId).eq("courseId", courseId)
      )
      .first();

    return {
      learningStyle: learningPath?.learningStyle || "mixed",
      optimalTimeOfDay: learningPath?.optimalTimeOfDay || "anytime",
      personalizedMessage: generatePersonalizedMessage(patterns),
      nextLectureRecommendation: learningPath?.recommendedNextLectureId,
    };
  },
});
```

### 2. Video Lecture Page

```typescript
// convex/lectures.ts
import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

export const getLectureData = query({
  args: { lectureId: v.id("lectures") },
  handler: async (ctx, { lectureId }) => {
    const userId = await getUserId(ctx);
    const lecture = await ctx.db.get(lectureId);

    if (!lecture) throw new Error("Lecture not found");

    // Get chapters
    const chapters = await ctx.db
      .query("chapters")
      .withIndex("by_lecture", (q) => q.eq("lectureId", lectureId))
      .collect();

    // Get user progress
    const progress = await ctx.db
      .query("userLectureProgress")
      .withIndex("by_user_lecture", (q) =>
        q.eq("userId", userId).eq("lectureId", lectureId)
      )
      .first();

    // Get previous lecture for context
    const previousLecture = await getPreviousLecture(
      ctx,
      lecture.courseId,
      lecture.order
    );

    return {
      lecture,
      chapters: chapters.sort((a, b) => a.startTime - b.startTime),
      progress,
      previousLecture,
      muxPlaybackUrl: lecture.muxPlaybackId
        ? `https://stream.mux.com/${lecture.muxPlaybackId}.m3u8`
        : null,
    };
  },
});

export const updateProgress = mutation({
  args: {
    lectureId: v.id("lectures"),
    progressSeconds: v.number(),
    completed: v.boolean(),
  },
  handler: async (ctx, { lectureId, progressSeconds, completed }) => {
    const userId = await getUserId(ctx);
    const lecture = await ctx.db.get(lectureId);

    if (!lecture) throw new Error("Lecture not found");

    // Upsert progress
    const existing = await ctx.db
      .query("userLectureProgress")
      .withIndex("by_user_lecture", (q) =>
        q.eq("userId", userId).eq("lectureId", lectureId)
      )
      .first();

    if (existing) {
      await ctx.db.patch(existing._id, {
        progressSeconds,
        status: completed ? "completed" : "in_progress",
      });
    } else {
      await ctx.db.insert("userLectureProgress", {
        userId,
        lectureId,
        courseId: lecture.courseId,
        progressSeconds,
        status: completed ? "completed" : "in_progress",
      });
    }

    // Update course progress
    await updateCourseProgress(ctx, userId, lecture.courseId);

    // Track activity
    await ctx.db.insert("userActivity", {
      userId,
      activityType: completed ? "lecture_completed" : "lecture_progress",
      description: `Progress: ${Math.round(progressSeconds)}s`,
      courseId: lecture.courseId,
      lectureId,
    });
  },
});
```

### 3. AI Tutor Agent

```typescript
// convex/aiTutor.ts
import { Agent, createTool } from "@convex-dev/agent";
import { openai } from "@ai-sdk/openai";
import { z } from "zod";
import { components } from "./_generated/api";
import { internal } from "./_generated/api";

// Define the AI Tutor Agent
export const tutorAgent = new Agent(components.agent, {
  chat: openai.chat("gpt-4o"),
  textEmbedding: openai.embedding("text-embedding-3-small"),
  instructions: `You are an expert AI tutor helping students learn effectively.
    You have access to the course content, student progress, and can create
    personalized learning materials. Always be encouraging and adaptive to
    the student's learning style.`,
  tools: {
    searchCourseContent: createTool({
      description: "Search course content using RAG",
      args: z.object({
        query: z.string(),
        courseId: z.string(),
      }),
      handler: async (ctx, { query, courseId }): Promise<string> => {
        const results = await ctx.runQuery(internal.rag.searchCourseContent, {
          query,
          courseId,
        });
        return JSON.stringify(results);
      },
    }),

    createQuiz: createTool({
      description: "Generate a quiz based on lecture content",
      args: z.object({
        lectureId: z.string(),
        numQuestions: z.number().min(1).max(10),
        difficulty: z.enum(["easy", "medium", "hard"]),
      }),
      handler: async (ctx, args): Promise<string> => {
        const quizId = await ctx.runAction(internal.ai.generateQuiz, args);
        return `Created quiz with ID: ${quizId}`;
      },
    }),

    createFlashcards: createTool({
      description: "Generate flashcards from lecture content",
      args: z.object({
        lectureId: z.string(),
        numCards: z.number().min(1).max(20),
        focusAreas: z.array(z.string()).optional(),
      }),
      handler: async (ctx, args): Promise<string> => {
        const deckId = await ctx.runAction(
          internal.ai.generateFlashcards,
          args
        );
        return `Created flashcard deck with ID: ${deckId}`;
      },
    }),

    explainConcept: createTool({
      description: "Provide detailed explanation of a concept",
      args: z.object({
        concept: z.string(),
        lectureContext: z.string().optional(),
      }),
      handler: async (ctx, { concept, lectureContext }): Promise<string> => {
        const explanation = await ctx.runAction(internal.ai.explainConcept, {
          concept,
          lectureContext,
        });
        return explanation;
      },
    }),

    findRelatedContent: createTool({
      description: "Find related lectures or concepts",
      args: z.object({
        currentLectureId: z.string(),
        topic: z.string(),
      }),
      handler: async (ctx, args): Promise<string> => {
        const related = await ctx.runQuery(
          internal.rag.findRelatedLectures,
          args
        );
        return JSON.stringify(related);
      },
    }),
  },
});

// Action to handle tutor conversations
export const chatWithTutor = internalAction({
  args: {
    courseId: v.id("courses"),
    lectureId: v.optional(v.id("lectures")),
    message: v.string(),
    threadId: v.optional(v.string()),
  },
  handler: async (ctx, { courseId, lectureId, message, threadId }) => {
    const userId = await getUserId(ctx);

    // Get context about the lecture and user progress
    const context = await buildTutorContext(ctx, userId, courseId, lectureId);

    // Continue or create thread
    const { thread, threadId: newThreadId } = threadId
      ? await tutorAgent.continueThread(ctx, { threadId })
      : await tutorAgent.createThread(ctx, {
          userId,
          metadata: { courseId, lectureId },
        });

    // Generate response with context
    const result = await thread.generateText({
      prompt: message,
      system: `Current context:\n${JSON.stringify(context, null, 2)}`,
    });

    // Save conversation
    await ctx.runMutation(internal.aiTutor.saveConversation, {
      userId,
      courseId,
      lectureId,
      threadId: newThreadId,
      message,
      response: result.text,
    });

    return {
      response: result.text,
      threadId: newThreadId,
      suggestions: await generateFollowUpSuggestions(ctx, result.text),
    };
  },
});

async function buildTutorContext(
  ctx: any,
  userId: string,
  courseId: string,
  lectureId?: string
) {
  // Get user's current progress
  const courseProgress = await ctx.runQuery(
    internal.userCourses.getCourseProgress,
    { userId, courseId }
  );

  // Get previous interactions
  const recentActivity = await ctx.runQuery(
    internal.userActivity.getRecentActivity,
    { userId, courseId, limit: 10 }
  );

  // Get lecture-specific context if provided
  let lectureContext = null;
  if (lectureId) {
    const lecture = await ctx.runQuery(
      internal.lectures.getLectureWithContext,
      { lectureId }
    );
    lectureContext = {
      title: lecture.generatedTitle,
      summary: lecture.summary,
      insights: lecture.insights,
      currentChapter: await getCurrentChapter(
        ctx,
        lectureId,
        courseProgress.progressSeconds
      ),
    };
  }

  return {
    courseProgress,
    recentActivity,
    lectureContext,
    learningStyle: courseProgress.learningPath?.learningStyle,
  };
}

// Generate contextual notes as video progresses
export const generateProgressNotes = internalAction({
  args: {
    lectureId: v.id("lectures"),
    currentTime: v.number(), // in seconds
  },
  handler: async (ctx, { lectureId, currentTime }) => {
    // Get transcript segment around current time
    const transcript = await getTranscriptSegment(
      ctx,
      lectureId,
      currentTime,
      30
    ); // 30s window

    // Generate contextual note
    const result = await generateObject({
      model: openai("gpt-4o-mini"),
      schema: z.object({
        note: z.string(),
        timestamp: z.number(),
        keyFormula: z.string().optional(),
        relatedConcept: z.string().optional(),
      }),
      prompt: `Generate a concise study note for this video segment at ${currentTime}s:\n\n${transcript}`,
    });

    return result.object;
  },
});

// RAG-powered content search
export const searchCourseContent = query({
  args: {
    query: v.string(),
    courseId: v.id("courses"),
  },
  handler: async (ctx, { query, courseId }) => {
    // Generate embedding for the query
    const { embedding } = await openai
      .embedding("text-embedding-3-small")
      .doEmbed({ values: [query] });

    // Vector search
    const results = await ctx.db
      .query("courseEmbeddings")
      .withSearchIndex("by_embedding", (q) =>
        q.search("embedding", embedding[0]).eq("courseId", courseId)
      )
      .take(5);

    // Enhance results with lecture context
    const enhancedResults = await Promise.all(
      results.map(async (result) => {
        const lecture = await ctx.db.get(result.lectureId);
        return {
          ...result,
          lectureTitle: lecture?.generatedTitle,
          lectureOrder: lecture?.order,
        };
      })
    );

    return enhancedResults;
  },
});
```

### 4. Learning Tools Generation

```typescript
// convex/learningTools.ts
import { internalAction, mutation } from "./_generated/server";
import { v } from "convex/values";
import { generateObject } from "ai";
import { openai } from "@ai-sdk/openai";
import { z } from "zod";

export const generateQuiz = internalAction({
  args: {
    lectureId: v.string(),
    numQuestions: v.number(),
    difficulty: v.string(),
  },
  handler: async (ctx, { lectureId, numQuestions, difficulty }) => {
    // Get lecture content
    const lecture = await ctx.runQuery(internal.lectures.getLectureById, {
      lectureId,
    });

    if (!lecture?.transcript) {
      throw new Error("No transcript available for quiz generation");
    }

    // Generate quiz questions
    const result = await generateObject({
      model: openai("gpt-4o"),
      schema: z.object({
        title: z.string(),
        questions: z
          .array(
            z.object({
              text: z.string(),
              type: z.literal("multiple_choice"),
              options: z
                .array(
                  z.object({
                    text: z.string(),
                    isCorrect: z.boolean(),
                  })
                )
                .length(4),
              explanation: z.string(),
              difficulty: z.enum(["easy", "medium", "hard"]),
            })
          )
          .length(numQuestions),
      }),
      prompt: `Create a ${difficulty} difficulty quiz with ${numQuestions} questions based on this lecture:
        Title: ${lecture.generatedTitle}
        Content: ${lecture.transcript}

        Make questions that test understanding, not just memorization.`,
    });

    // Create quiz in database
    const quizId = await ctx.runMutation(internal.quizzes.createQuiz, {
      courseId: lecture.courseId,
      lectureId: lecture._id,
      title: result.object.title,
      type: "ai_generated",
    });

    // Create questions
    for (const question of result.object.questions) {
      await ctx.runMutation(internal.questions.createQuestion, {
        quizId,
        ...question,
      });
    }

    return quizId;
  },
});

export const generateFlashcards = internalAction({
  args: {
    lectureId: v.string(),
    numCards: v.number(),
    focusAreas: v.optional(v.array(v.string())),
  },
  handler: async (ctx, { lectureId, numCards, focusAreas }) => {
    const lecture = await ctx.runQuery(internal.lectures.getLectureById, {
      lectureId,
    });

    if (!lecture?.transcript) {
      throw new Error("No content available for flashcard generation");
    }

    // Generate flashcards
    const result = await generateObject({
      model: openai("gpt-4o"),
      schema: z.object({
        deckTitle: z.string(),
        cards: z
          .array(
            z.object({
              question: z.string(),
              answer: z.string(),
              explanation: z.string().optional(),
              difficulty: z.number().min(1).max(5),
            })
          )
          .length(numCards),
      }),
      prompt: `Create ${numCards} flashcards from this lecture.
        ${focusAreas ? `Focus on these areas: ${focusAreas.join(", ")}` : ""}

        Lecture: ${lecture.generatedTitle}
        Content: ${lecture.transcript}

        Make cards that promote deep understanding.`,
    });

    // Create deck
    const deckId = await ctx.runMutation(internal.flashcards.createDeck, {
      courseId: lecture.courseId,
      lectureId: lecture._id,
      title: result.object.deckTitle,
    });

    // Create cards
    for (const card of result.object.cards) {
      await ctx.runMutation(internal.flashcards.createCard, {
        deckId,
        ...card,
        source: "ai_generated",
      });
    }

    return deckId;
  },
});

export const generateSmartNotes = internalAction({
  args: {
    lectureId: v.id("lectures"),
    userPrompt: v.optional(v.string()),
  },
  handler: async (ctx, { lectureId, userPrompt }) => {
    const userId = await getUserId(ctx);
    const lecture = await ctx.runQuery(internal.lectures.getLectureById, {
      lectureId,
    });

    // Get user's existing notes for context
    const existingNotes = await ctx.runQuery(
      internal.notes.getUserNotesForLecture,
      { userId, lectureId }
    );

    // Generate smart notes
    const result = await generateObject({
      model: openai("gpt-4o"),
      schema: z.object({
        title: z.string(),
        sections: z.array(
          z.object({
            heading: z.string(),
            content: z.string(),
            keyPoints: z.array(z.string()),
          })
        ),
        connections: z.array(z.string()), // Related concepts
      }),
      prompt: `Generate comprehensive study notes for this lecture.
        ${userPrompt ? `User request: ${userPrompt}` : ""}

        Lecture: ${lecture.generatedTitle}
        Content: ${lecture.transcript}
        ${
          existingNotes.length > 0
            ? `Build upon existing notes: ${JSON.stringify(existingNotes)}`
            : ""
        }

        Create well-structured notes with clear sections.`,
    });

    // Create note
    const noteId = await ctx.runMutation(internal.notes.createNote, {
      userId,
      courseId: lecture.courseId,
      lectureId,
      title: result.object.title,
      content: formatSmartNotes(result.object),
      source: "ai_summary",
      tags: result.object.connections,
    });

    return noteId;
  },
});

function formatSmartNotes(notes: any): string {
  let content = "";

  for (const section of notes.sections) {
    content += `## ${section.heading}\n\n`;
    content += `${section.content}\n\n`;
    content += `**Key Points:**\n`;
    for (const point of section.keyPoints) {
      content += `- ${point}\n`;
    }
    content += "\n";
  }

  if (notes.connections.length > 0) {
    content += `## Related Concepts\n`;
    for (const connection of notes.connections) {
      content += `- ${connection}\n`;
    }
  }

  return content;
}
```
