import { Id } from "@/convex/_generated/dataModel";

export const uploadFileWithProgressAndAbort = (
  uploadUrl: string,
  file: File,
  onProgress: (progress: number, loaded: number, total: number) => void
) => {
  let xhr: XMLHttpRequest | null = null;

  const promise = new Promise((resolve, reject) => {
    xhr = new XMLHttpRequest();

    xhr.upload.addEventListener("progress", (event) => {
      if (event.lengthComputable) {
        const percentComplete = (event.loaded / event.total) * 100;
        onProgress(percentComplete, event.loaded, event.total);
      }
    });

    xhr.addEventListener("load", () => {
      if (xhr && xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText);
          resolve(response);
        } catch (error) {
          reject(new Error("Failed to parse response"));
        }
      } else {
        reject(new Error(`Upload failed with status: ${xhr?.status}`));
      }
    });

    xhr.addEventListener("error", () => {
      reject(new Error("Upload failed"));
    });

    xhr.addEventListener("abort", () => {
      reject(new Error("Upload aborted"));
    });

    xhr.open("POST", uploadUrl);
    xhr.setRequestHeader("Content-Type", file.type);
    xhr.send(file);
  });

  // Return both promise and abort function
  return {
    promise,
    abort: () => {
      if (xhr) {
        xhr.abort();
      }
    },
  };
};

export const uploadMultipleFilesWithProgressAndAbort = (
  uploads: Array<{ uploadUrl: string; file: File }>,
  onProgress: (
    fileIndex: number,
    progress: number,
    loaded: number,
    total: number
  ) => void,
  onFileComplete: (
    fileIndex: number,
    result: { storageId: Id<"_storage"> }
  ) => void,
  onFileError: (fileIndex: number, error: Error) => void
) => {
  const xhrInstances: Array<XMLHttpRequest | null> = [];
  const promises: Array<Promise<any>> = [];

  uploads.forEach((upload, index) => {
    const xhr = new XMLHttpRequest();
    xhrInstances[index] = xhr;

    const promise = new Promise((resolve, reject) => {
      xhr.upload.addEventListener("progress", (event) => {
        if (event.lengthComputable) {
          const percentComplete = (event.loaded / event.total) * 100;
          onProgress(index, percentComplete, event.loaded, event.total);
        }
      });

      xhr.addEventListener("load", () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response: { storageId: Id<"_storage"> } = JSON.parse(
              xhr.responseText
            );
            onFileComplete(index, response);
            resolve(response);
          } catch (error) {
            const parseError = new Error("Failed to parse response");
            onFileError(index, parseError);
            reject(parseError);
          }
        } else {
          const statusError = new Error(
            `Upload failed with status: ${xhr.status}`
          );
          onFileError(index, statusError);
          reject(statusError);
        }
      });

      xhr.addEventListener("error", () => {
        const error = new Error("Upload failed");
        onFileError(index, error);
        reject(error);
      });

      xhr.addEventListener("abort", () => {
        const error = new Error("Upload aborted");
        onFileError(index, error);
        reject(error);
      });

      xhr.open("POST", upload.uploadUrl);
      xhr.setRequestHeader("Content-Type", upload.file.type);
      xhr.send(upload.file);
    });

    promises[index] = promise;
  });

  return {
    promises,
    abort: (fileIndex?: number) => {
      if (fileIndex !== undefined) {
        // Abort specific file
        if (xhrInstances[fileIndex]) {
          xhrInstances[fileIndex]!.abort();
        }
      } else {
        // Abort all files
        xhrInstances.forEach((xhr) => {
          if (xhr) {
            xhr.abort();
          }
        });
      }
    },
  };
};
