import Header from "@/components/header";
import Sidebar from "@/components/sidebar";
import { api } from "@/convex/_generated/api";
import { fetchQuery } from "convex/nextjs";
import { convexAuthNextjsToken } from "@convex-dev/auth/nextjs/server";

export default async function HomePage() {
  const user = await fetchQuery(
    api.users.currentUser,
    {},
    { token: await convexAuthNextjsToken() }
  );

  console.log("user", user);

  return (
    <div className="h-screen flex overflow-hidden bg-background">
      {/* Fixed Sidebar */}
      <div className="fixed left-0 top-0 z-30">
        <Sidebar />
      </div>

      {/* Main Content Area with Fixed Header */}
      <div className="flex-1 flex flex-col ml-72">
        {/* Fixed Top Bar */}
        <div className="fixed top-0 right-0 left-72 z-20">
          <Header />
        </div>

        {/* Scrollable Main Content */}
        <main className="flex-1 overflow-y-auto pt-20 bg-background">
          <div className="p-8 space-y-8">{/* Students UI */}</div>
        </main>
      </div>
    </div>
  );
}
