import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"

export const metadata: Metadata = {
  title: "Authentication - StudyMind",
  description: "Sign in or create an account to access StudyMind's AI-powered learning platform",
}

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-primary/10">{children}</div>
}
