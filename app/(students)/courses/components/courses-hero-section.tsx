import React from "react";
import { GraduationCap } from "lucide-react";
import { StatsCard } from "@/components/shared/stats-card";

interface CoursesHeroSectionProps {
  userName: string;
  stats: {
    activeCourses: number;
    avgCompletion: string;
    completedCourses: number;
    studyTime: string;
  };
}

export function CoursesHeroSection({ userName, stats }: CoursesHeroSectionProps) {
  return (
    <section className="animated-gradient p-6 lg:p-10 rounded-2xl shadow-xl mb-6 lg:mb-10 stagger-entrance relative overflow-hidden">
      <div className="absolute inset-0 minimal-artwork-bg opacity-50"></div>
      <div className="relative z-10 flex justify-between items-start">
        <div>
          <h2 className="text-3xl lg:text-4xl font-bold text-white mb-3">
            Your Learning Journey
          </h2>
          <p className="text-white/90 text-md lg:text-lg mb-6">
            Continue where you left off or explore new academic frontiers.
          </p>
        </div>
        <GraduationCap className="w-10 h-10 lg:w-12 lg:h-12 text-white opacity-80" />
      </div>
      <div className="relative z-10 grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mt-6 lg:mt-8">
        <StatsCard
          value={stats.activeCourses.toString()}
          label="Active Courses"
        />
        <StatsCard
          value={stats.avgCompletion}
          label="Avg Completion"
          valueClassName="text-[var(--accent-primary)]"
        />
        <StatsCard
          value={stats.completedCourses.toString()}
          label="Completed"
        />
        <StatsCard
          value={stats.studyTime}
          label="Study Time"
        />
      </div>
    </section>
  );
}