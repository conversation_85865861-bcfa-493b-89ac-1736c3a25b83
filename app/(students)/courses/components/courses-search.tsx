"use client";

import React from "react";
import { Search } from "lucide-react";

interface CoursesSearchProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  sortBy: string;
  setSortBy: (sort: string) => void;
}

export function CoursesSearch({ 
  searchTerm, 
  setSearchTerm, 
  sortBy, 
  setSortBy 
}: CoursesSearchProps) {
  return (
    <div className="flex flex-col lg:flex-row justify-between items-stretch lg:items-center mb-6 lg:mb-10 gap-4">
      <div className="relative flex-1 max-w-lg">
        <div className="relative">
          <input
            type="text"
            className="w-full bg-white/70 backdrop-blur-sm border border-[var(--accent-primary)]/20 rounded-2xl px-6 py-4 pl-14 text-[var(--text-primary)] placeholder-[var(--text-muted)] focus:outline-none focus:bg-white focus:border-[var(--accent-primary)]/50 focus:ring-4 focus:ring-[var(--accent-primary)]/10 transition-all duration-300 shadow-sm hover:shadow-md"
            placeholder="Search courses, instructors, or topics..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <Search className="absolute left-5 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--accent-primary)]/60" />
        </div>
      </div>
      <div className="lg:ml-8">
        <select
          className="w-full lg:w-auto bg-white/70 backdrop-blur-sm border border-[var(--accent-primary)]/20 rounded-2xl px-6 py-4 text-[var(--text-primary)] focus:outline-none focus:bg-white focus:border-[var(--accent-primary)]/50 focus:ring-4 focus:ring-[var(--accent-primary)]/10 cursor-pointer transition-all duration-300 shadow-sm hover:shadow-md appearance-none bg-no-repeat bg-right pr-12"
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value)}
          style={{
            backgroundImage: `url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="%23a9c2b9" viewBox="0 0 16 16"><path d="M8 10.5L4 6.5h8L8 10.5z"/></svg>')`,
            backgroundPosition: 'right 16px center',
          }}
        >
          <option value="recent">Recently Accessed</option>
          <option value="progress">Progress</option>
          <option value="alphabetical">Alphabetical</option>
          <option value="completion">Completion Date</option>
        </select>
      </div>
    </div>
  );
}