"use client";

import React from "react";
import Link from "next/link";

interface CourseCardProps {
  course: {
    _id: string;
    title: string;
    description: string;
    thumbnailUrl?: string;
    progress: number;
    resourceCount: number;
    status: 'active' | 'completed' | 'paused';
    instructor: string;
    category: string;
  };
}

const getCourseIcon = (category: string, status: string) => {
  const icons = {
    economics: "📊",
    physics: "⚛️", 
    mathematics: "∫",
    "computer-science": "💻",
    chemistry: "🧪",
    general: "📚"
  };
  
  return icons[category as keyof typeof icons] || icons.general;
};

const getGradientClass = (category: string) => {
  const gradients = {
    economics: "bg-[var(--accent-primary)]",
    physics: "bg-gradient-to-br from-orange-400 to-red-500",
    mathematics: "bg-gradient-to-br from-blue-400 to-blue-600",
    "computer-science": "bg-gradient-to-br from-purple-500 to-pink-500",
    chemistry: "bg-gradient-to-br from-green-400 to-green-600",
    general: "bg-[var(--accent-primary)]"
  };
  
  return gradients[category as keyof typeof gradients] || gradients.general;
};

const getStatusColor = (status: string) => {
  const colors = {
    active: "bg-[var(--success)]",
    completed: "bg-[var(--info)]", 
    paused: "bg-[var(--warning)]"
  };
  
  return colors[status as keyof typeof colors] || colors.active;
};

const getProgressColor = (status: string) => {
  if (status === 'completed') return 'bg-[var(--success)]';
  if (status === 'paused') return 'bg-[var(--warning)]';
  return 'bg-[var(--accent-primary)]';
};

const getActionButton = (status: string, progress: number) => {
  if (status === 'completed') {
    return {
      text: "View Certificate",
      className: "w-full py-3 text-sm font-semibold bg-[var(--bg-secondary)] text-[var(--text-primary)] rounded-xl hover:bg-[var(--accent-tertiary)]/50 transition-colors"
    };
  }
  
  if (status === 'paused') {
    return {
      text: "Resume Course",
      className: "w-full py-3 text-sm font-semibold bg-[var(--warning)] text-white rounded-xl hover:bg-[var(--warning)]/80 transition-colors"
    };
  }
  
  return {
    text: "Continue Learning",
    className: "btn-primary w-full py-3 text-sm font-semibold"
  };
};

export function CourseCard({ course }: CourseCardProps) {

  const courseIcon = getCourseIcon(course.category, course.status);
  const gradientClass = getGradientClass(course.category);
  const statusColor = getStatusColor(course.status);
  const progressColor = getProgressColor(course.status);
  const actionButton = getActionButton(course.status, course.progress);

  const lectureProgress = Math.floor((course.progress / 100) * course.resourceCount);
  const totalLectures = course.resourceCount || 1;

  return (
    <Link
      href={`/courses/${course._id}`}
      className="course-card hover-lift minimal-artwork-subtle group cursor-pointer bg-white border border-[var(--accent-primary)]/15 rounded-2xl p-8 transition-all duration-300 ease-in-out relative overflow-hidden shadow-sm min-h-[320px] flex flex-col justify-between"
    >
      {/* Hero Course Icon */}
      <div className="flex justify-center mb-6">
        <div
          className={`w-20 h-20 ${gradientClass} rounded-2xl flex items-center justify-center text-3xl shadow-lg group-hover:scale-105 transition-transform duration-300 ${course.status === 'paused' ? 'opacity-60' : ''}`}
        >
          {courseIcon}
        </div>
      </div>

      {/* Course Info */}
      <div className="text-center mb-6">
        <h3 className="text-xl font-bold text-[var(--text-primary)] mb-2 group-hover:text-[var(--accent-primary)] transition-colors">
          {course.title}
        </h3>
        <p className="text-sm text-[var(--text-muted)] mb-4">
          {course.instructor}
        </p>

        {/* Progress Section */}
        <div className="mb-4">
          <div className="flex justify-between items-center text-sm mb-2">
            {course.status === 'completed' ? (
              <span className="text-[var(--text-secondary)]">
                All <span className="geist-mono">{totalLectures}</span> lectures completed
              </span>
            ) : (
              <span className="text-[var(--text-secondary)]">
                Lecture <span className="geist-mono">{lectureProgress}</span> of{" "}
                <span className="geist-mono">{totalLectures}</span>
              </span>
            )}
            <span className={`geist-mono font-bold ${course.status === 'completed' ? 'text-[var(--success)]' : course.status === 'paused' ? 'text-[var(--warning)]' : 'text-[var(--accent-primary)]'}`}>
              {Math.round(course.progress)}%
            </span>
          </div>
          <div className="w-full bg-[var(--bg-secondary)] rounded-full h-2">
            <div
              className={`${progressColor} h-2 rounded-full transition-all duration-700`}
              style={{ width: `${course.progress}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Single Action */}
      <div className="text-center">
        <button className={actionButton.className}>
          {actionButton.text}
        </button>
      </div>

      {/* Status Badge */}
      <div className={`absolute top-4 right-4 ${statusColor} text-white px-3 py-1 rounded-full text-xs font-semibold opacity-0 group-hover:opacity-100 transition-opacity capitalize`}>
        {course.status}
      </div>
    </Link>
  );
}