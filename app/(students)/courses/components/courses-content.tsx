"use client";

import React, { useState, useMemo } from "react";
import { CourseCard } from "./course-card";
import { CoursesSearch } from "./courses-search";

interface Course {
  _id: string;
  title: string;
  description: string;
  thumbnailUrl?: string;
  progress: number;
  resourceCount: number;
  status: 'active' | 'completed' | 'paused';
  instructor: string;
  category: string;
}

interface CoursesContentProps {
  courses: Course[];
}

export function CoursesContent({ courses }: CoursesContentProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("recent");

  // Filter and sort courses
  const filteredAndSortedCourses = useMemo(() => {
    let filtered = courses;

    // Apply search filter
    if (searchTerm.trim()) {
      const search = searchTerm.toLowerCase();
      filtered = courses.filter(
        (course) =>
          course.title.toLowerCase().includes(search) ||
          course.instructor.toLowerCase().includes(search) ||
          course.description.toLowerCase().includes(search)
      );
    }

    // Apply sorting
    const sorted = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case "alphabetical":
          return a.title.localeCompare(b.title);
        case "progress":
          return b.progress - a.progress;
        case "completion":
          // Completed courses first, then by progress
          if (a.status === "completed" && b.status !== "completed") return -1;
          if (b.status === "completed" && a.status !== "completed") return 1;
          return b.progress - a.progress;
        case "recent":
        default:
          // Keep original order (most recent first)
          return 0;
      }
    });

    return sorted;
  }, [courses, searchTerm, sortBy]);

  return (
    <div>
      <CoursesSearch
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        sortBy={sortBy}
        setSortBy={setSortBy}
      />

      {/* Courses Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8 stagger-entrance">
        {filteredAndSortedCourses.map((course, index) => (
          <div
            key={course._id}
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <CourseCard course={course} />
          </div>
        ))}
      </div>

      {/* No results message */}
      {filteredAndSortedCourses.length === 0 && (
        <div className="text-center py-12">
          <div className="text-[var(--text-muted)] text-lg mb-2">
            {searchTerm.trim() ? "No courses found" : "No courses available"}
          </div>
          {searchTerm.trim() && (
            <p className="text-[var(--text-muted)] text-sm">
              Try adjusting your search terms or filters
            </p>
          )}
        </div>
      )}
    </div>
  );
}