import React from "react";
import { fetchQuery } from "convex/nextjs";
import { api } from "@/convex/_generated/api";
import { convexAuthNextjsToken } from "@convex-dev/auth/nextjs/server";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { CoursesHeroSection } from "./components/courses-hero-section";
import { CoursesContent } from "./components/courses-content";

// Server-side data fetching using Convex
async function getCoursesData() {
  const user = await fetchQuery(
    api.users.currentUser,
    {},
    {
      token: await convexAuthNextjsToken(),
    }
  );

  if (!user) {
    // Handle unauthenticated state
    return {
      userStats: {
        activeCourses: 0,
        avgCompletion: "0%",
        completedCourses: 0,
        studyTime: "0h",
      },
      userName: "Guest",
      courses: [],
    };
  }

  // Fetch user's courses using existing query
  const courses = await fetchQuery(
    api.learnings.getAllCourses,
    {},
    {
      token: await convexAuthNextjsToken(),
    }
  );

  // Calculate stats from courses data
  const activeCourses = courses.filter(course => course.progress < 100).length;
  const completedCourses = courses.filter(course => course.progress >= 100).length;
  const avgCompletion = courses.length > 0 
    ? Math.round(courses.reduce((acc, course) => acc + course.progress, 0) / courses.length)
    : 0;

  const userStats = {
    activeCourses,
    avgCompletion: `${avgCompletion}%`,
    completedCourses,
    studyTime: user.studyTime || "47h", // Mock data from currentUser
  };

  const userName = user.firstName || user.name || "Student";

  // Transform courses data to match component expectations
  const transformedCourses = courses.map(course => ({
    _id: course._id,
    title: course.title,
    description: course.description || "Course description not available",
    thumbnailUrl: course.thumbnailUrl,
    progress: course.progress || 0,
    resourceCount: course.resourceCount || 0,
    status: course.progress >= 100 ? 'completed' as const : 
            course.progress > 0 ? 'active' as const : 'paused' as const,
    instructor: "Prof. Dr. Course Instructor", // Mock data for now
    category: "general", // Mock data for now
  }));

  return {
    userStats,
    userName,
    courses: transformedCourses,
  };
}

export default async function CoursesPage() {
  const { userStats, userName, courses } = await getCoursesData();

  return (
    <DashboardLayout>
      <div className="flex-1 p-4 lg:p-8 pt-6 lg:pt-10 overflow-y-auto">
        <CoursesHeroSection userName={userName} stats={userStats} />
        <CoursesContent courses={courses} />
      </div>
    </DashboardLayout>
  );
}