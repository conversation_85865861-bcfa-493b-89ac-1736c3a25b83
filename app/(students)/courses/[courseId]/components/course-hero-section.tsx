"use client";

import React from "react";
import Link from "next/link";
import { Book<PERSON><PERSON>, Clock, Play } from "lucide-react";

interface CourseHeroSectionProps {
  course: {
    _id: string;
    title: string;
    instructor?: string;
    progress: number;
    totalLectures?: number;
    currentLecture?: number;
  };
  currentLecture?: {
    id: string;
    title: string;
    duration: string;
    type: "video" | "pdf" | "audio";
  };
  completedCount: number;
}

export function CourseHeroSection({ 
  course, 
  currentLecture, 
  completedCount 
}: CourseHeroSectionProps) {
  const getTypeColor = (type: string) => {
    switch (type) {
      case "video": return "bg-blue-100 text-blue-700";
      case "pdf": return "bg-emerald-100 text-emerald-700";
      case "audio": return "bg-purple-100 text-purple-700";
      default: return "bg-gray-100 text-gray-700";
    }
  };

  return (
    <section className="animated-gradient p-8 rounded-2xl shadow-xl mb-10 stagger-entrance relative overflow-hidden">
      <div className="absolute inset-0 minimal-artwork-bg opacity-30"></div>
      <div className="relative z-10">
        <div className="flex items-start justify-between mb-6">
          <div className="flex-1">
            <h2 
              className="text-3xl font-bold text-white mb-2"
              style={{ animationDelay: "0.1s" }}
            >
              {course.title}
            </h2>
            <p 
              className="text-white/80 text-base"
              style={{ animationDelay: "0.2s" }}
            >
              {course.instructor || "Course Instructor"} • {course.totalLectures || 24} lectures
            </p>
          </div>
          <div className="text-right" style={{ animationDelay: "0.2s" }}>
            <div className="text-white/90 text-sm mb-1">Your Progress</div>
            <div className="geist-mono text-2xl font-bold text-white">
              {course.progress}%
            </div>
          </div>
        </div>

        {/* Continue Learning Section */}
        {currentLecture && (
          <div 
            className="bg-white/15 backdrop-blur-sm rounded-xl p-6 border border-white/20"
            style={{ animationDelay: "0.3s" }}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="text-white/90 text-sm mb-1">
                  Continue Your Journey
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">
                  {currentLecture.title}
                </h3>
                <div className="flex items-center space-x-4 text-white/70 text-sm">
                  <span className="flex items-center">
                    <BookOpen className="w-4 h-4 mr-1" />
                    Lecture {course.currentLecture || 9} of {course.totalLectures || 24}
                  </span>
                  <span className="flex items-center">
                    <Clock className="w-4 h-4 mr-1" />
                    {currentLecture.duration}
                  </span>
                  <span className={`px-2 py-1 rounded-lg text-xs font-medium ${getTypeColor(currentLecture.type)}`}>
                    {currentLecture.type.toUpperCase()}
                  </span>
                </div>
              </div>
              <div className="ml-6">
                {currentLecture && (
                  <Link
                    href={`/courses/${course._id}/lectures/${currentLecture.id}`}
                    className="bg-white text-[var(--accent-primary)] px-6 py-3 rounded-xl font-semibold hover:bg-white/90 transition-all shadow-lg flex items-center"
                  >
                    <Play className="w-4 h-4 mr-2" />
                    Start Lecture
                  </Link>
                )}
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mt-4">
              <div className="w-full bg-white/20 rounded-full h-2">
                <div
                  className="bg-white h-2 rounded-full transition-all duration-700 progress-bar-fill"
                  style={{ width: `${course.progress}%` }}
                ></div>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
}