"use client";

import React from "react";
import Link from "next/link";
import { BookOpen, Calendar, Clock, Check, Play, Lock, Eye, FileText, RefreshCw } from "lucide-react";

interface Lecture {
  id: string;
  title: string;
  week: number;
  duration: string;
  type: "video" | "pdf" | "audio";
  status: "completed" | "current" | "locked";
}

interface LectureListProps {
  lectures: Lecture[];
  courseId: string;
}

export function LectureList({ lectures, courseId }: LectureListProps) {
  
  const getTypeColor = (type: string) => {
    switch (type) {
      case "video": return "bg-blue-100 text-blue-700";
      case "pdf": return "bg-emerald-100 text-emerald-700";
      case "audio": return "bg-purple-100 text-purple-700";
      default: return "bg-gray-100 text-gray-600";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed": return <Check className="w-6 h-6" />;
      case "current": return <span className="geist-mono font-bold text-sm">{lectures.findIndex(l => l.status === "current") + 1}</span>;
      case "locked": return <span className="geist-mono font-bold text-sm">{lectures.findIndex(l => l.status === "locked") + 1}</span>;
      default: return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "bg-[var(--success)]";
      case "current": return "bg-[var(--accent-primary)]";
      case "locked": return "bg-[var(--text-subtle)]";
      default: return "bg-[var(--text-subtle)]";
    }
  };

  const getContainerStyles = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-[var(--success)]/10 border border-[var(--success)]/20 hover:bg-[var(--success)]/15";
      case "current":
        return "bg-[var(--accent-primary)]/20 border-2 border-[var(--accent-primary)] hover:bg-[var(--accent-primary)]/25";
      case "locked":
        return "bg-[var(--bg-secondary)]/50 border border-[var(--text-subtle)]/20 hover:bg-[var(--bg-secondary)]/70 opacity-60";
      default:
        return "bg-[var(--bg-secondary)]/50 border border-[var(--text-subtle)]/20";
    }
  };

  const getStatusText = (status: string, index: number) => {
    switch (status) {
      case "completed":
        return (
          <span className="flex items-center text-[var(--success)]">
            <Check className="w-4 h-4 mr-1" />
            Completed
          </span>
        );
      case "current":
        return (
          <span className="flex items-center text-[var(--accent-primary)] font-semibold">
            <Play className="w-4 h-4 mr-1" />
            Next Up
          </span>
        );
      case "locked":
        return (
          <span className="flex items-center text-[var(--text-subtle)]">
            <Lock className="w-4 h-4 mr-1" />
            Locked
          </span>
        );
      default:
        return null;
    }
  };

  const handleLectureClick = (lecture: Lecture) => {
    if (lecture.status === "locked") return;
    
    // This function is now only used for the main lecture row click
    // Consider converting this to a Link wrapper as well for better UX
    console.log("Navigate to lecture:", lecture.id);
  };

  const handlePreview = (lecture: Lecture, e: React.MouseEvent) => {
    e.stopPropagation();
    // Handle preview functionality
    console.log("Preview lecture:", lecture.id);
  };

  const handleNotes = (lecture: Lecture, e: React.MouseEvent) => {
    e.stopPropagation();
    // Handle notes functionality
    console.log("Open notes for lecture:", lecture.id);
  };

  const handleReview = (lecture: Lecture, e: React.MouseEvent) => {
    e.stopPropagation();
    // Handle review functionality
    console.log("Review lecture:", lecture.id);
  };

  return (
    <div className="bg-white p-8 rounded-2xl shadow-xl stagger-entrance minimal-artwork-subtle border border-gray-100">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-[var(--text-primary)] flex items-center">
          <BookOpen className="w-7 h-7 mr-3 icon-primary" />
          Course Lectures
        </h2>
        <p className="text-[var(--text-muted)] mt-2">
          Track your progress through each lecture and continue where you left off.
        </p>
      </div>

      <div className="space-y-4">
        {lectures.map((lecture, index) => (
          <div
            key={lecture.id}
            className={`${getContainerStyles(lecture.status)} p-5 rounded-xl transition-all duration-300 cursor-pointer hover-lift group`}
            onClick={() => handleLectureClick(lecture)}
          >
            <div className="flex items-center space-x-4">
              <div className={`w-12 h-12 ${getStatusColor(lecture.status)} rounded-full flex items-center justify-center text-white shadow-sm group-hover:scale-105 transition-transform`}>
                {getStatusIcon(lecture.status)}
              </div>
              
              <div className="flex-1">
                <h3 className={`text-lg font-semibold mb-1 ${lecture.status === "locked" ? "text-[var(--text-secondary)]" : "text-[var(--text-primary)]"}`}>
                  {lecture.title}
                </h3>
                <div className="flex items-center space-x-4 text-sm text-[var(--text-muted)]">
                  <span className={`px-2 py-1 rounded-lg font-medium ${lecture.status === "locked" ? "bg-gray-100 text-gray-600" : getTypeColor(lecture.type)}`}>
                    {lecture.type.toUpperCase()}
                  </span>
                  <span className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    Week {lecture.week}
                  </span>
                  <span className="flex items-center">
                    <Clock className="w-4 h-4 mr-1" />
                    {lecture.duration}
                  </span>
                  {getStatusText(lecture.status, index)}
                </div>
              </div>

              <div className="flex space-x-2">
                {lecture.status === "current" && (
                  <>
                    <Link
                      href={`/courses/${courseId}/lectures/${lecture.id}`}
                      className="bg-[var(--accent-primary)] hover:bg-[var(--highlight)] text-white px-4 py-2 rounded-lg text-sm font-semibold transition-colors focus-ring flex items-center"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <Play className="w-4 h-4 mr-1" />
                      Start
                    </Link>
                    <button
                      className="bg-[var(--bg-secondary)] hover:bg-[var(--accent-tertiary)]/50 text-[var(--text-primary)] px-3 py-2 rounded-lg text-sm font-medium transition-colors focus-ring flex items-center"
                      onClick={(e) => handlePreview(lecture, e)}
                    >
                      <Eye className="w-4 h-4 mr-1" />
                      Preview
                    </button>
                  </>
                )}
                
                {lecture.status === "completed" && (
                  <div className="flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button
                      className="bg-[var(--bg-secondary)] hover:bg-[var(--accent-tertiary)]/50 text-[var(--text-primary)] px-3 py-2 rounded-lg text-sm font-medium transition-colors focus-ring flex items-center"
                      onClick={(e) => handleNotes(lecture, e)}
                    >
                      <FileText className="w-4 h-4 mr-1" />
                      Notes
                    </button>
                    <button
                      className="bg-[var(--bg-secondary)] hover:bg-[var(--accent-tertiary)]/50 text-[var(--text-primary)] px-3 py-2 rounded-lg text-sm font-medium transition-colors focus-ring flex items-center"
                      onClick={(e) => handleReview(lecture, e)}
                    >
                      <RefreshCw className="w-4 h-4 mr-1" />
                      Review
                    </button>
                  </div>
                )}
                
                {lecture.status === "locked" && (
                  <button
                    className="bg-[var(--text-subtle)] text-white px-4 py-2 rounded-lg text-sm font-medium cursor-not-allowed flex items-center"
                    disabled
                  >
                    <Lock className="w-4 h-4 mr-1" />
                    Locked
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}