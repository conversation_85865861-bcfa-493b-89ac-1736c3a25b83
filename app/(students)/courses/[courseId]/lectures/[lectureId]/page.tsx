import React from "react";
import { LectureLayoutWrapper } from "./components/lecture-layout-wrapper";

interface LecturePageProps {
  params: Promise<{
    courseId: string;
    lectureId: string;
  }>;
}

// Mock lecture data - in real app, this would come from Convex
async function getLectureData(courseId: string, lectureId: string) {
  // Mock data based on lectureId to demonstrate different types
  const mockLectures = {
    "market-structures": {
      id: "market-structures",
      title: "Market Structures and Competition",
      type: "video" as const,
      duration: "42 min",
      week: 3,
      instructor: "Prof. Dr. <PERSON>",
      progress: 34,
      totalDuration: 2520, // 42 minutes in seconds
      currentTime: 858, // 14:18 in seconds
    },
    "integration-techniques": {
      id: "integration-techniques", 
      title: "Advanced Calculus: Integration Techniques",
      type: "audio" as const,
      duration: "62 min",
      week: 5,
      instructor: "Dr. <PERSON>",
      progress: 37,
      totalDuration: 3720, // 62 minutes in seconds
      currentTime: 1398, // 23:18 in seconds
    },
    "perfect-competition": {
      id: "perfect-competition",
      title: "Market Structures and Competition",
      type: "pdf" as const,
      duration: "15 min read",
      week: 3,
      instructor: "Prof. Dr. <PERSON>", 
      progress: 13,
      totalPages: 15,
      currentPage: 2,
    }
  };

  const lecture = mockLectures[lectureId as keyof typeof mockLectures] || mockLectures["market-structures"];
  
  return {
    courseId,
    lecture,
  };
}

export default async function LecturePage({ params }: LecturePageProps) {
  // Await params to access properties (Next.js 15 pattern)
  const { courseId, lectureId } = await params;
  const { lecture } = await getLectureData(courseId, lectureId);

  return (
    <LectureLayoutWrapper lecture={lecture} courseId={courseId} />
  );
}
