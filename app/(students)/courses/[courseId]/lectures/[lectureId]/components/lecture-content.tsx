"use client";

import React, { useState, useRef } from "react";
import { MuxVideoPlayer } from "./video/mux-video-player";
import { ChapterNavigation } from "./video/chapter-navigation";
import { PDFViewer } from "./pdf/pdf-viewer";

interface LectureContentProps {
  lecture: {
    id: string;
    title: string;
    type: "video" | "audio" | "pdf";
    duration: string;
    progress: number;
    currentTime?: number;
    playbackId?: string;
    currentPage?: number;
    totalPages?: number;
    pdfUrl?: string;
  };
  courseId: string;
  onCurrentChapterChange?: (chapterId: string) => void;
  onJumpToTimeRefChange?: (ref: (time: number) => void) => void;
  onCurrentPageChange?: (page: number) => void;
  onJumpToPageRefChange?: (ref: (page: number) => void) => void;
  onPDFViewerRefChange?: (ref: React.RefObject<any>) => void;
}

// Mock data for chapters and transcripts
const mockChapters = [
  {
    id: "intro",
    title: "Introduction & Overview",
    startTime: 0,
    endTime: 204,
    timestamp: "0:00 - 3:24",
  },
  {
    id: "perfect-competition",
    title: "Perfect Competition",
    startTime: 205,
    endTime: 738,
    timestamp: "3:25 - 12:18",
  },
  {
    id: "monopolistic-markets",
    title: "Monopolistic Markets",
    startTime: 739,
    endTime: 1125,
    timestamp: "12:19 - 18:45",
  },
  {
    id: "oligopoly",
    title: "Oligopoly Structures",
    startTime: 1126,
    endTime: 1710,
    timestamp: "18:46 - 28:30",
  },
  {
    id: "real-world",
    title: "Real-World Examples",
    startTime: 1711,
    endTime: 2112,
    timestamp: "28:31 - 35:12",
  },
  {
    id: "summary",
    title: "Summary & Next Steps",
    startTime: 2113,
    endTime: 2535,
    timestamp: "35:13 - 42:15",
  },
];

const mockTranscripts = [
  {
    timestamp: "01:28",
    time: 88,
    title: "Introduction & Overview",
    content: "Welcome to today's lecture on market structures and competition. We'll explore how different market types affect pricing, consumer choice, and economic efficiency. This builds directly on our previous discussion of supply and demand fundamentals.",
  },
  {
    timestamp: "03:45",
    time: 225,
    title: "Perfect Competition",
    content: "Perfect competition represents an idealized market structure where many small firms compete with identical products. Key characteristics include: price-taking behavior, free entry and exit, perfect information, and homogeneous products. Let's examine how these conditions create market efficiency.",
  },
  {
    timestamp: "14:32",
    time: 872,
    title: "Monopolistic Markets",
    content: "In contrast to perfect competition, monopolistic markets feature a single seller with significant market power. The monopolist can influence price by controlling quantity. We see this in utilities, patented technologies, and markets with high barriers to entry. Consider how this affects consumer welfare...",
    isActive: true,
  },
  {
    timestamp: "19:15",
    time: 1155,
    title: "Oligopoly Structures",
    content: "Oligopolies occupy the middle ground between perfect competition and monopoly. A few large firms dominate the market, leading to strategic interdependence. Think about airlines, telecommunications, or automotive industries. Game theory becomes crucial for understanding firm behavior.",
  },
  {
    timestamp: "29:00",
    time: 1740,
    title: "Real-World Examples",
    content: "Let's apply these concepts to real markets. Amazon's marketplace shows monopolistic competition with many sellers offering differentiated products. Meanwhile, Google's search dominance exemplifies a near-monopoly. The smartphone industry demonstrates oligopolistic behavior between Apple and Samsung.",
  },
];

export function LectureContent({ 
  lecture, 
  courseId, 
  onCurrentChapterChange, 
  onJumpToTimeRefChange,
  onCurrentPageChange,
  onJumpToPageRefChange,
  onPDFViewerRefChange
}: LectureContentProps) {
  const [currentChapter, setCurrentChapter] = useState<string>("monopolistic-markets");
  const [currentProgress, setCurrentProgress] = useState<number>(lecture.progress);
  const [currentPage, setCurrentPage] = useState<number>(lecture.currentPage || 1);
  const [totalPages, setTotalPages] = useState<number>(lecture.totalPages || 15);
  const playerRef = useRef<any>(null);

  // Expose jump to time function for AI companion
  const handleJumpToTime = (time: number) => {
    if (playerRef.current?.jumpToTime) {
      playerRef.current.jumpToTime(time);
    }
  };

  // Pass jump function to parent component
  React.useEffect(() => {
    if (onJumpToTimeRefChange) {
      onJumpToTimeRefChange(handleJumpToTime);
    }
  }, [onJumpToTimeRefChange]);

  // Expose jump to page function for AI companion
  const handleJumpToPage = (page: number) => {
    if (playerRef.current?.jumpToPage) {
      playerRef.current.jumpToPage(page);
    }
  };

  // Pass page jump function to parent component
  React.useEffect(() => {
    if (onJumpToPageRefChange) {
      onJumpToPageRefChange(handleJumpToPage);
    }
  }, [onJumpToPageRefChange]);

  // Pass PDF viewer ref to parent for zoom controls
  React.useEffect(() => {
    if (onPDFViewerRefChange && lecture.type === "pdf") {
      onPDFViewerRefChange(playerRef);
    }
  }, [onPDFViewerRefChange, lecture.type]);

  const handleChapterClick = (chapterId: string) => {
    setCurrentChapter(chapterId);
    if (playerRef.current?.jumpToChapter) {
      playerRef.current.jumpToChapter(chapterId);
    }
  };

  const handleTranscriptClick = (time: number) => {
    if (playerRef.current?.jumpToTime) {
      playerRef.current.jumpToTime(time);
    }
  };

  const handleTimeUpdate = (currentTime: number) => {
    // Update transcript active state based on current time
    // This would be implemented with real transcript sync
  };

  const handleChapterChange = (chapterId: string) => {
    setCurrentChapter(chapterId);
    if (onCurrentChapterChange) {
      onCurrentChapterChange(chapterId);
    }
  };

  const handleProgressUpdate = (progress: number) => {
    setCurrentProgress(progress);
  };

  // PDF-specific handlers
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
    
    // Update progress based on page
    if (totalPages > 0) {
      const progress = (pageNumber / totalPages) * 100;
      setCurrentProgress(progress);
    }

    // Notify parent component of page change
    if (onCurrentPageChange) {
      onCurrentPageChange(pageNumber);
    }
  };

  // Handler for when navigation thumbnails are clicked
  const handlePageNavClick = (pageNumber: number) => {
    setCurrentPage(pageNumber);
    
    // Update PDF viewer to show the selected page
    if (playerRef.current?.jumpToPage) {
      playerRef.current.jumpToPage(pageNumber);
    }
    
    // Update progress based on page
    if (totalPages > 0) {
      const progress = (pageNumber / totalPages) * 100;
      setCurrentProgress(progress);
    }

    // Notify parent component of page change
    if (onCurrentPageChange) {
      onCurrentPageChange(pageNumber);
    }
  };

  const handleZoomChange = (zoom: number) => {
    // Handle zoom changes if needed
    console.log(`PDF zoom changed to: ${zoom}%`);
  };

  const renderContent = () => {
    switch (lecture.type) {
      case "video":
        return (
          <>
            {/* Progress indicator */}
            <div className="h-1 bg-[var(--bg-secondary)]">
              <div 
                className="h-full bg-[var(--accent-primary)] transition-all duration-300"
                style={{ width: `${currentProgress}%` }}
              />
            </div>
            
            {/* Video Player */}
            <div className="p-6">
              <div className="h-[60vh] max-h-[500px] min-h-[300px]">
                <MuxVideoPlayer
                  ref={playerRef}
                  lecture={lecture}
                  chapters={mockChapters}
                  onTimeUpdate={handleTimeUpdate}
                  onChapterChange={handleChapterChange}
                  onProgressUpdate={handleProgressUpdate}
                />
              </div>
            </div>

            {/* Chapter Navigation */}
            <div className="flex-shrink-0">
              <ChapterNavigation
                chapters={mockChapters}
                transcripts={mockTranscripts}
                currentChapter={currentChapter}
                onChapterClick={handleChapterClick}
                onTranscriptClick={handleTranscriptClick}
              />
            </div>
          </>
        );
      
      case "audio":
        return (
          <>
            <div className="h-1 bg-[var(--bg-secondary)]">
              <div 
                className="h-full bg-[var(--accent-primary)] transition-all duration-300"
                style={{ width: `${lecture.progress}%` }}
              />
            </div>
            <div className="flex flex-1">
              <div className="w-80 bg-white border-r border-[var(--bg-secondary)] flex flex-col">
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-6xl mb-4">🎧</div>
                    <h3 className="text-xl font-semibold mb-2">Audio Controls</h3>
                    <p className="text-[var(--text-muted)]">
                      Audio player interface will be implemented in phase 2
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex-1 bg-white flex items-center justify-center">
                <div className="text-center">
                  <div className="text-6xl mb-4">📝</div>
                  <h3 className="text-xl font-semibold mb-2">Live Transcript</h3>
                  <p className="text-[var(--text-muted)]">
                    Real-time transcript interface will be implemented in phase 2
                  </p>
                </div>
              </div>
            </div>
          </>
        );
      
      case "pdf":
        return (
          <>
            {/* Progress indicator */}
            <div className="h-1 bg-[var(--bg-secondary)]">
              <div 
                className="h-full bg-[var(--accent-primary)] transition-all duration-300"
                style={{ width: `${currentProgress}%` }}
              />
            </div>
            
            {/* PDF Viewer with Integrated Navigation */}
            <div className="flex-1 h-full min-h-0">
              <PDFViewer
                ref={playerRef}
                lecture={lecture}
                onPageChange={handlePageChange}
                onProgressUpdate={handleProgressUpdate}
                onZoomChange={handleZoomChange}
              />
            </div>
          </>
        );
      
      default:
        return (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="text-6xl mb-4">📚</div>
              <h3 className="text-xl font-semibold mb-2">Lecture Content</h3>
              <p className="text-[var(--text-muted)]">
                Content interface will be implemented based on lecture type
              </p>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="flex-1 flex flex-col h-full">
      {renderContent()}
    </div>
  );
}