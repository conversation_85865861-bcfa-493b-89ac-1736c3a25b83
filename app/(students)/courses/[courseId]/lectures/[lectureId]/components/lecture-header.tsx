"use client";

import React, { useState, useEffect } from "react";
import { Clock, ZoomIn, ZoomOut } from "lucide-react";

interface LectureHeaderProps {
  lecture: {
    id: string;
    title: string;
    type: "video" | "audio" | "pdf";
    duration: string;
    week: number;
    instructor: string;
    progress: number;
    currentPage?: number;
    totalPages?: number;
  };
  courseId: string;
  pdfViewerRef?: React.RefObject<any>;
}

export function LectureHeader({ lecture, courseId, pdfViewerRef }: LectureHeaderProps) {
  const [currentZoom, setCurrentZoom] = useState<number>(100);

  // Update zoom state when PDF viewer zoom changes
  useEffect(() => {
    if (lecture.type === "pdf" && pdfViewerRef?.current) {
      const updateZoom = () => {
        if (pdfViewerRef.current?.getZoom) {
          setCurrentZoom(pdfViewerRef.current.getZoom());
        }
      };
      
      // Update zoom every 100ms while PDF is loaded
      const interval = setInterval(updateZoom, 100);
      return () => clearInterval(interval);
    }
  }, [lecture.type, pdfViewerRef]);

  // For PDF lectures, show a minimal header with just read time and zoom controls
  if (lecture.type !== "pdf") {
    return null; // No additional header needed for video/audio
  }

  return (
    <header className="flex justify-between items-center px-6 py-3 bg-white border-b border-[var(--bg-secondary)]">
      {/* Left side - Read Time (Desktop only) */}
      <div className="hidden md:flex items-center text-sm text-[var(--text-muted)]">
        <Clock className="w-4 h-4 mr-2 flex-shrink-0" />
        <span>{lecture.duration}</span>
      </div>
      
      {/* Mobile: Empty space to push zoom controls right */}
      <div className="md:hidden flex-1"></div>
      
      {/* Right side - Zoom Controls */}
      {pdfViewerRef?.current && (
        <div className="flex items-center bg-[var(--bg-secondary)] rounded-lg p-1">
          <button
            onClick={() => pdfViewerRef.current?.zoomOut()}
            className="p-2 hover:bg-[var(--accent-tertiary)]/50 rounded-md transition-colors"
            disabled={currentZoom <= 50}
            title="Zoom out"
          >
            <ZoomOut className="w-4 h-4 text-[var(--text-primary)]" />
          </button>
          
          <button
            onClick={() => pdfViewerRef.current?.setZoom(75)}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              currentZoom === 75 ? "bg-[var(--accent-primary)] text-white" : "hover:bg-[var(--accent-tertiary)]/50"
            }`}
          >
            75%
          </button>
          
          <button
            onClick={() => pdfViewerRef.current?.resetZoom()}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              currentZoom === 100 ? "bg-[var(--accent-primary)] text-white" : "hover:bg-[var(--accent-tertiary)]/50"
            }`}
          >
            100%
          </button>
          
          <button
            onClick={() => pdfViewerRef.current?.setZoom(125)}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              currentZoom === 125 ? "bg-[var(--accent-primary)] text-white" : "hover:bg-[var(--accent-tertiary)]/50"
            }`}
          >
            125%
          </button>
          
          <button
            onClick={() => pdfViewerRef.current?.zoomIn()}
            className="p-2 hover:bg-[var(--accent-tertiary)]/50 rounded-md transition-colors"
            disabled={currentZoom >= 200}
            title="Zoom in"
          >
            <ZoomIn className="w-4 h-4 text-[var(--text-primary)]" />
          </button>
        </div>
      )}
    </header>
  );
}