"use client";

import React, { useState } from "react";
import { ChevronLeft, ChevronRight, ChevronUp, ChevronDown } from "lucide-react";
import { Document, Page, pdfjs } from "react-pdf";

// Set up PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  "pdfjs-dist/build/pdf.worker.min.mjs",
  import.meta.url
).toString();

interface PageNavigationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (pageNumber: number) => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
  pdfUrl: string;
}

export function PageNavigation({
  currentPage,
  totalPages,
  onPageChange,
  isCollapsed = false,
  onToggleCollapse,
  pdfUrl,
}: PageNavigationProps) {
  // Generate page thumbnails data
  const generateThumbnails = () => {
    const thumbnails = [];
    for (let i = 1; i <= totalPages; i++) {
      thumbnails.push({
        pageNumber: i,
        isActive: i <= currentPage,
        isCurrent: i === currentPage,
      });
    }
    return thumbnails;
  };

  const thumbnails = generateThumbnails();

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  if (isCollapsed) {
    return null;
  }

  return (
    <aside className="w-20 bg-white border-r border-[var(--bg-secondary)] flex flex-col h-full transition-all duration-500">
      {/* Toggle Button */}
      <div className="flex-shrink-0 p-3 border-b border-[var(--bg-secondary)]">
        <button
          onClick={onToggleCollapse}
          className="w-10 h-10 bg-[var(--bg-secondary)] rounded-lg flex items-center justify-center hover:bg-[var(--accent-tertiary)]/50 transition-colors focus-ring mx-auto"
          title="Hide page navigation"
        >
          <ChevronLeft className="w-4 h-4 text-[var(--text-primary)]" />
        </button>
      </div>

      {/* Page Thumbnails - Scrollable */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden p-3 space-y-3 min-h-0">
        {thumbnails.map((thumbnail) => (
          <div
            key={thumbnail.pageNumber}
            className={`page-thumbnail cursor-pointer transition-all duration-300 border-radius-0.5rem p-1 relative ${
              thumbnail.isCurrent
                ? "bg-[var(--accent-primary)]/20 border-2 border-[var(--accent-primary)]"
                : thumbnail.isActive
                ? "bg-[var(--accent-tertiary)]/10 border border-[var(--accent-tertiary)]"
                : "hover:bg-[var(--bg-secondary)] hover:scale-105"
            }`}
            onClick={() => onPageChange(thumbnail.pageNumber)}
            title={`Go to page ${thumbnail.pageNumber}`}
          >
            {/* Actual PDF Page Thumbnail */}
            <div className="thumbnail-preview w-12 h-16 bg-white border border-[var(--bg-secondary)] rounded-sm relative overflow-hidden shadow-sm">
              <Document
                file={pdfUrl}
                loading={null}
                error={null}
              >
                <Page
                  pageNumber={thumbnail.pageNumber}
                  width={48} // 12 * 4 (w-12 = 48px)
                  height={64} // 16 * 4 (h-16 = 64px)
                  renderTextLayer={false}
                  renderAnnotationLayer={false}
                  className="thumbnail-page"
                />
              </Document>
            </div>
            
            {/* Page Number */}
            <div
              className={`text-center text-xs mt-1 geist-mono ${
                thumbnail.isCurrent
                  ? "text-[var(--accent-primary)] font-semibold"
                  : "text-[var(--text-muted)]"
              }`}
            >
              {thumbnail.pageNumber}
            </div>
          </div>
        ))}
      </div>

      {/* Page Controls */}
      <div className="flex-shrink-0 p-3 border-t border-[var(--bg-secondary)] space-y-2">
        <button
          onClick={handlePreviousPage}
          disabled={currentPage <= 1}
          className="w-full h-8 bg-[var(--bg-secondary)] rounded-lg flex items-center justify-center hover:bg-[var(--accent-tertiary)]/50 transition-colors text-xs font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          title="Previous page"
        >
          <ChevronUp className="w-3 h-3" />
        </button>
        
        <div className="text-xs text-center text-[var(--text-muted)] geist-mono">
          {currentPage}/{totalPages}
        </div>
        
        <button
          onClick={handleNextPage}
          disabled={currentPage >= totalPages}
          className="w-full h-8 bg-[var(--bg-secondary)] rounded-lg flex items-center justify-center hover:bg-[var(--accent-tertiary)]/50 transition-colors text-xs font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          title="Next page"
        >
          <ChevronDown className="w-3 h-3" />
        </button>
      </div>
    </aside>
  );
}

// Floating toggle button for when navigation is collapsed
export function PageNavToggle({
  isVisible,
  onToggle,
}: {
  isVisible: boolean;
  onToggle: () => void;
}) {
  if (!isVisible) return null;

  return (
    <button
      onClick={onToggle}
      className="fixed left-3 top-1/2 transform -translate-y-1/2 z-50 bg-[var(--accent-primary)] border-none rounded-r-lg px-2 py-3 text-white cursor-pointer shadow-lg hover:bg-[var(--highlight)] transition-all duration-300 opacity-100"
      title="Show page navigation"
    >
      <ChevronRight className="w-4 h-4" />
    </button>
  );
}