"use client";

import React, {
  useState,
  useRef,
  useCallback,
  useImperativeHandle,
  forwardRef,
  useEffect,
} from "react";
import { Document, Page, pdfjs } from "react-pdf";
import { ZoomIn, ZoomOut, ChevronLeft, ChevronUp, ChevronDown } from "lucide-react";

// Set up PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  "pdfjs-dist/build/pdf.worker.min.mjs",
  import.meta.url
).toString();

interface PDFViewerProps {
  lecture: {
    id: string;
    title: string;
    type: "pdf";
    pdfUrl?: string;
    currentPage?: number;
    totalPages?: number;
  };
  onPageChange?: (pageNumber: number) => void;
  onProgressUpdate?: (progress: number) => void;
  onZoomChange?: (zoom: number) => void;
}

export interface PDFViewerRef {
  jumpToPage: (pageNumber: number) => void;
  getCurrentPage: () => number;
  getTotalPages: () => number;
  setZoom: (newZoom: number) => void;
  zoomIn: () => void;
  zoomOut: () => void;
  resetZoom: () => void;
  getZoom: () => number;
}

export const PDFViewer = forwardRef<PDFViewerRef, PDFViewerProps>(
  ({ lecture, onPageChange, onProgressUpdate, onZoomChange }, ref) => {
    const [numPages, setNumPages] = useState<number>(0);
    const [currentPage, setCurrentPage] = useState<number>(
      lecture.currentPage || 1
    );
    const [zoom, setZoom] = useState<number>(100);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [isPageNavCollapsed, setIsPageNavCollapsed] = useState<boolean>(false);
    const [loadedPages, setLoadedPages] = useState<number[]>([1]); // Track which pages are loaded
    const containerRef = useRef<HTMLDivElement>(null);

    // Mock PDF URL - in real implementation this would come from lecture.pdfUrl
    const pdfUrl =
      lecture.pdfUrl ||
      "https://harmless-jaguar-107.convex.cloud/api/storage/ee95e815-0826-4d80-9dc0-ef4a3beaf746";

    const onDocumentLoadSuccess = useCallback(
      ({ numPages }: { numPages: number }) => {
        setNumPages(numPages);
        setCurrentPage(1); // Always start at page 1
        setIsLoading(false);

        // Update progress based on first page
        if (onProgressUpdate && numPages) {
          const progress = (1 / numPages) * 100;
          onProgressUpdate(progress);
        }
      },
      [onProgressUpdate]
    );

    const handlePageChange = useCallback(
      (pageNumber: number) => {
        if (pageNumber >= 1 && pageNumber <= numPages) {
          setCurrentPage(pageNumber);

          // Ensure the target page is loaded
          if (!loadedPages.includes(pageNumber)) {
            setLoadedPages(prev => [...prev, pageNumber].sort((a, b) => a - b));
          }

          // Scroll to the specific page after a short delay to ensure it's rendered
          setTimeout(() => {
            const pageElement = document.querySelector(`[data-pdf-page="${pageNumber}"]`);
            if (pageElement) {
              pageElement.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'start',
                inline: 'nearest'
              });
            }
          }, 100);

          // Update progress
          if (onProgressUpdate && numPages) {
            const progress = (pageNumber / numPages) * 100;
            onProgressUpdate(progress);
          }

          if (onPageChange) {
            onPageChange(pageNumber);
          }
        }
      },
      [numPages, onPageChange, onProgressUpdate, loadedPages]
    );

    const handleZoomChange = useCallback(
      (newZoom: number) => {
        setZoom(newZoom);
        if (onZoomChange) {
          onZoomChange(newZoom);
        }
      },
      [onZoomChange]
    );

    const zoomIn = () => {
      const newZoom = Math.min(zoom + 25, 200);
      handleZoomChange(newZoom);
    };

    const zoomOut = () => {
      const newZoom = Math.max(zoom - 25, 50);
      handleZoomChange(newZoom);
    };

    const resetZoom = () => {
      handleZoomChange(100);
    };

    const previousPage = () => {
      if (currentPage > 1) {
        handlePageChange(currentPage - 1);
      }
    };

    const nextPage = () => {
      if (currentPage < numPages) {
        handlePageChange(currentPage + 1);
      }
    };

    const togglePageNavigation = () => {
      setIsPageNavCollapsed(!isPageNavCollapsed);
    };

    // Generate page thumbnails data
    const generateThumbnails = () => {
      const thumbnails = [];
      for (let i = 1; i <= numPages; i++) {
        thumbnails.push({
          pageNumber: i,
          isActive: i <= currentPage,
          isCurrent: i === currentPage,
        });
      }
      return thumbnails;
    };

    const thumbnails = generateThumbnails();

    // Auto-scroll thumbnail navigation to show selected page
    useEffect(() => {
      if (currentPage && numPages > 0) {
        // Find the thumbnail element for the current page
        const thumbnailElement = document.querySelector(`[data-page-number="${currentPage}"]`);
        if (thumbnailElement) {
          // Scroll the thumbnail into view within its container
          thumbnailElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest'
          });
        }
      }
    }, [currentPage, numPages]);

    // Auto-load next page when scrolling near bottom
    useEffect(() => {
      if (!numPages) return;

      const scrollContainer = containerRef.current?.querySelector('.overflow-y-auto');
      if (!scrollContainer) return;

      const handleScroll = () => {
        const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
        const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;
        
        // When user scrolls to 80% of current content, load next page
        if (scrollPercentage > 0.8) {
          const maxLoadedPage = Math.max(...loadedPages);
          const nextPage = maxLoadedPage + 1;
          
          // Load next page if it exists and isn't already loaded
          if (nextPage <= numPages && !loadedPages.includes(nextPage)) {
            setLoadedPages(prev => [...prev, nextPage].sort((a, b) => a - b));
          }
        }

        // Update current page based on which page is most visible
        const allPageElements = document.querySelectorAll('[data-pdf-page]');
        let mostVisiblePage = currentPage;
        let maxVisibility = 0;

        allPageElements.forEach((pageEl) => {
          const pageNum = parseInt(pageEl.getAttribute('data-pdf-page') || '1');
          const rect = pageEl.getBoundingClientRect();
          const containerRect = scrollContainer.getBoundingClientRect();
          
          // Calculate how much of the page is visible
          const visibleTop = Math.max(rect.top, containerRect.top);
          const visibleBottom = Math.min(rect.bottom, containerRect.bottom);
          const visibleHeight = Math.max(0, visibleBottom - visibleTop);
          const totalHeight = rect.height;
          const visibilityRatio = visibleHeight / totalHeight;

          if (visibilityRatio > maxVisibility && visibilityRatio > 0.3) {
            maxVisibility = visibilityRatio;
            mostVisiblePage = pageNum;
          }
        });

        // Update current page if it changed
        if (mostVisiblePage !== currentPage) {
          setCurrentPage(mostVisiblePage);
          
          // Update progress
          if (onProgressUpdate && numPages) {
            const progress = (mostVisiblePage / numPages) * 100;
            onProgressUpdate(progress);
          }
          
          if (onPageChange) {
            onPageChange(mostVisiblePage);
          }
        }
      };

      // Throttle scroll events
      let scrollTimeout: NodeJS.Timeout;
      const throttledHandleScroll = () => {
        if (scrollTimeout) clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(handleScroll, 100);
      };

      scrollContainer.addEventListener('scroll', throttledHandleScroll);
      
      return () => {
        scrollContainer.removeEventListener('scroll', throttledHandleScroll);
        if (scrollTimeout) clearTimeout(scrollTimeout);
      };
    }, [numPages, loadedPages, currentPage, onPageChange, onProgressUpdate]);

    // Expose methods for parent components
    useImperativeHandle(ref, () => ({
      jumpToPage: handlePageChange,
      getCurrentPage: () => currentPage,
      getTotalPages: () => numPages,
      setZoom: handleZoomChange,
      zoomIn,
      zoomOut,
      resetZoom,
      getZoom: () => zoom,
    }));

    return (
      <div className="h-full flex bg-[var(--bg-primary)]" ref={containerRef}>
        {/* Page Navigation Sidebar */}
        {!isPageNavCollapsed && (
          <aside className="w-20 bg-white border-r border-[var(--bg-secondary)] flex flex-col h-full transition-all duration-500">
            {/* Toggle Button */}
            <div className="flex-shrink-0 p-3 border-b border-[var(--bg-secondary)]">
              <button
                onClick={togglePageNavigation}
                className="w-10 h-10 bg-[var(--bg-secondary)] rounded-lg flex items-center justify-center hover:bg-[var(--accent-tertiary)]/50 transition-colors focus-ring mx-auto"
                title="Hide page navigation"
              >
                <ChevronLeft className="w-4 h-4 text-[var(--text-primary)]" />
              </button>
            </div>

            {/* Page Thumbnails - Scrollable */}
            <div className="flex-1 overflow-y-auto overflow-x-hidden p-3 space-y-3 min-h-0">
              {thumbnails.map((thumbnail) => (
                <div
                  key={thumbnail.pageNumber}
                  data-page-number={thumbnail.pageNumber}
                  className={`page-thumbnail cursor-pointer transition-all duration-300 border-radius-0.5rem p-1 relative ${
                    thumbnail.isCurrent
                      ? "bg-[var(--accent-primary)]/20 border-2 border-[var(--accent-primary)]"
                      : thumbnail.isActive
                      ? "bg-[var(--accent-tertiary)]/10 border border-[var(--accent-tertiary)]"
                      : "hover:bg-[var(--bg-secondary)] hover:scale-105"
                  }`}
                  onClick={() => handlePageChange(thumbnail.pageNumber)}
                  title={`Go to page ${thumbnail.pageNumber}`}
                >
                  {/* Actual PDF Page Thumbnail */}
                  <div className="thumbnail-preview w-12 h-16 bg-white border border-[var(--bg-secondary)] rounded-sm relative overflow-hidden shadow-sm">
                    <Document
                      file={pdfUrl}
                      loading={null}
                      error={null}
                    >
                      <Page
                        pageNumber={thumbnail.pageNumber}
                        width={48}
                        height={64}
                        renderTextLayer={false}
                        renderAnnotationLayer={false}
                        className="thumbnail-page"
                      />
                    </Document>
                  </div>
                  
                  {/* Page Number */}
                  <div
                    className={`text-center text-xs mt-1 geist-mono ${
                      thumbnail.isCurrent
                        ? "text-[var(--accent-primary)] font-semibold"
                        : "text-[var(--text-muted)]"
                    }`}
                  >
                    {thumbnail.pageNumber}
                  </div>
                </div>
              ))}
            </div>

            {/* Page Controls */}
            <div className="flex-shrink-0 p-3 border-t border-[var(--bg-secondary)] space-y-2">
              <button
                onClick={previousPage}
                disabled={currentPage <= 1}
                className="w-full h-8 bg-[var(--bg-secondary)] rounded-lg flex items-center justify-center hover:bg-[var(--accent-tertiary)]/50 transition-colors text-xs font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                title="Previous page"
              >
                <ChevronUp className="w-3 h-3" />
              </button>
              
              <div className="text-xs text-center text-[var(--text-muted)] geist-mono">
                {currentPage}/{numPages}
              </div>
              
              <button
                onClick={nextPage}
                disabled={currentPage >= numPages}
                className="w-full h-8 bg-[var(--bg-secondary)] rounded-lg flex items-center justify-center hover:bg-[var(--accent-tertiary)]/50 transition-colors text-xs font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                title="Next page"
              >
                <ChevronDown className="w-3 h-3" />
              </button>
            </div>
          </aside>
        )}

        {/* Floating Page Nav Toggle (when collapsed) */}
        {isPageNavCollapsed && (
          <button
            onClick={togglePageNavigation}
            className="fixed left-3 top-1/2 transform -translate-y-1/2 z-50 bg-[var(--accent-primary)] border-none rounded-r-lg px-2 py-3 text-white cursor-pointer shadow-lg hover:bg-[var(--highlight)] transition-all duration-300 opacity-100"
            title="Show page navigation"
          >
            <ChevronLeft className="w-4 h-4 rotate-180" />
          </button>
        )}

        {/* PDF Viewer */}
        <div className="flex-1 min-w-0 h-full">
          <div className="h-full flex flex-col">
            {/* PDF Document - Full Height */}
            <div className="flex-1 overflow-y-auto px-6 py-8 flex flex-col items-center justify-start">
              {isLoading && (
                <div className="flex items-center justify-center h-96">
                  <div className="text-center">
                    <div className="animate-spin w-8 h-8 border-4 border-[var(--accent-primary)] border-t-transparent rounded-full mx-auto mb-4"></div>
                    <p className="text-[var(--text-muted)]">
                      Loading PDF document...
                    </p>
                  </div>
                </div>
              )}

              <Document
                file={pdfUrl}
                onLoadSuccess={onDocumentLoadSuccess}
                onLoadError={(error) => {
                  console.error("Error loading PDF:", error);
                  setIsLoading(false);
                }}
                loading={null}
                className="flex flex-col items-center"
              >
                <div className="mb-4 text-center sticky top-0 z-10 bg-[var(--bg-primary)] py-2">
                  <span className="text-sm text-[var(--text-muted)] bg-[var(--bg-secondary)] px-3 py-1 rounded-full">
                    Page {currentPage || (numPages ? 1 : '--')} of {numPages || '--'}
                  </span>
                </div>
                
                {/* Render all loaded pages */}
                {loadedPages.map((pageNumber) => (
                  <div
                    key={`loaded-page-${pageNumber}`}
                    data-pdf-page={pageNumber}
                    className={`pdf-page-container mb-8 ${
                      pageNumber === currentPage ? "current-page" : ""
                    }`}
                  >
                    <Page
                      pageNumber={pageNumber}
                      scale={zoom / 100}
                      className="pdf-page shadow-lg"
                      renderTextLayer={false}
                      renderAnnotationLayer={false}
                      width={800}
                    />
                    
                    {/* Page separator for multi-page view */}
                    {pageNumber < Math.max(...loadedPages) && (
                      <div className="flex items-center justify-center mt-6 mb-2">
                        <div className="flex-1 h-px bg-[var(--bg-secondary)]"></div>
                        <span className="px-4 text-xs text-[var(--text-muted)] bg-[var(--bg-primary)]">
                          Page {pageNumber + 1}
                        </span>
                        <div className="flex-1 h-px bg-[var(--bg-secondary)]"></div>
                      </div>
                    )}
                  </div>
                ))}
                
                {/* Loading indicator for next page */}
                {loadedPages.length < numPages && (
                  <div className="flex items-center justify-center mt-8 mb-4">
                    <div className="text-center">
                      <div className="w-6 h-6 border-2 border-[var(--accent-primary)] border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                      <p className="text-sm text-[var(--text-muted)]">
                        Scroll down to load more pages...
                      </p>
                    </div>
                  </div>
                )}
              </Document>
            </div>
          </div>
        </div>
      </div>
    );
  }
);

PDFViewer.displayName = "PDFViewer";
