"use client";

import React from "react";
import {
  Brain,
  Compass,
  Clock,
  Lightbulb,
  Send,
  Bookmark,
  HelpCircle,
} from "lucide-react";

interface AILearningCompanionProps {
  lecture: {
    id: string;
    title: string;
    type: "video" | "audio" | "pdf";
    progress: number;
    currentPage?: number;
    totalPages?: number;
  };
  currentChapter?: string;
  currentPage?: number;
  onJumpToTime?: (time: number) => void;
  onJumpToPage?: (page: number) => void;
}

export function AILearningCompanion({
  lecture,
  currentChapter,
  currentPage,
  onJumpToTime,
  onJumpToPage,
}: AILearningCompanionProps) {
  const getCurrentFocus = () => {
    switch (lecture.type) {
      case "video":
        return "Market Competition - Monopolistic Structures";
      case "audio":
        return "Integration by Parts Formula";
      case "pdf":
        return `Page ${currentPage || lecture.currentPage || 1} - Perfect Competition Analysis`;
      default:
        return "Current Learning Topic";
    }
  };

  const getContextContent = () => {
    switch (lecture.type) {
      case "video":
        return {
          title: "Context Connection",
          description:
            "This builds on supply & demand concepts from Week 2. Notice how market structure affects pricing power.",
        };
      case "audio":
        return {
          title: "Key Insight",
          description:
            "Integration by parts is the reverse of the product rule. When you see a product that doesn't fit basic patterns, think LIATE!",
        };
      case "pdf":
        return {
          title: "Building on Previous Knowledge",
          description:
            "This chapter builds on supply & demand concepts from Chapter 2. Notice how market structure affects pricing power.",
        };
      default:
        return {
          title: "Learning Context",
          description:
            "AI-powered insights will help you understand connections and key concepts.",
        };
    }
  };

  const getUpcomingContent = () => {
    switch (lecture.type) {
      case "video":
        return [
          { topic: "Real-world monopoly examples", time: "~5 min" },
          { topic: "Practice problem walkthrough", time: "~8 min" },
        ];
      case "audio":
        return [
          { topic: "LIATE rule explanation", time: "~2 min" },
          { topic: "Worked example", time: "~5 min" },
          { topic: "Practice problem", time: "~8 min" },
        ];
      case "pdf":
        return [
          { topic: "Long-run equilibrium analysis", time: "Page 4", page: 4 },
          { topic: "Market efficiency discussion", time: "Page 5", page: 5 },
          { topic: "Real-world examples", time: "Page 7", page: 7 },
        ];
      default:
        return [{ topic: "Next learning topic", time: "Coming up" }];
    }
  };

  const contextContent = getContextContent();
  const upcomingContent = getUpcomingContent();

  return (
    <aside className="w-full h-full bg-[var(--bg-secondary)] border-l border-[var(--accent-tertiary)]/50 flex flex-col">
      {/* AI Header */}
      <div className="bg-[var(--accent-primary)] text-white p-6 relative overflow-hidden flex-shrink-0">
        <div className="absolute inset-0 minimal-artwork-bg opacity-20"></div>
        <div className="relative z-10">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
              <Brain className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold">Learning Companion</h3>
              <p className="text-sm text-white/80">Following along with you</p>
            </div>
          </div>
          <div className="bg-white/20 rounded-lg p-3">
            <div className="text-sm text-white/90 mb-1">Current Focus</div>
            <div className="text-xs text-white/70">{getCurrentFocus()}</div>
          </div>
        </div>
      </div>

      {/* AI Content */}
      <div className="flex-1 overflow-y-auto p-6 space-y-4">
        {/* Context Card */}
        <div className="bg-white rounded-xl p-4 border border-gray-100 minimal-artwork-subtle">
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-[var(--accent-primary)] rounded-full flex items-center justify-center flex-shrink-0">
              <Compass className="w-4 h-4 text-white" />
            </div>
            <div>
              <h4 className="font-medium text-[var(--text-primary)] mb-1">
                {contextContent.title}
              </h4>
              <p className="text-sm text-[var(--text-muted)]">
                {contextContent.description}
              </p>
            </div>
          </div>
        </div>

        {/* Previous Q&A for Video */}
        {lecture.type === "video" && (
          <div className="space-y-3">
            <div className="bg-[var(--bg-primary)] rounded-lg p-3 ml-8">
              <p className="text-sm text-[var(--text-secondary)]">
                "Can you give me an example of a monopolistic market?"
              </p>
            </div>
            <div className="bg-white rounded-lg p-3 border border-[var(--accent-primary)]/20">
              <div className="flex items-start space-x-2">
                <div className="w-6 h-6 bg-[var(--accent-primary)] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <Lightbulb className="w-3 h-3 text-white" />
                </div>
                <div>
                  <p className="text-sm text-[var(--text-primary)]">
                    Great question! Local utility companies are perfect
                    examples. The professor will cover this exact scenario in
                    about 3 minutes.
                  </p>
                  <button
                    onClick={() => onJumpToTime?.(1005)} // 16:45
                    className="mt-2 text-xs bg-[var(--accent-primary)] text-white px-2 py-1 rounded-full hover:bg-[var(--highlight)] transition-colors"
                  >
                    Jump to example →
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Progress Insight */}
        <div className="bg-[var(--highlight)]/30 rounded-xl p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Lightbulb className="w-4 h-4 text-[var(--highlight)]" />
            <h4 className="font-medium text-[var(--text-primary)]">
              Learning Progress
            </h4>
          </div>
          <p className="text-sm text-[var(--text-muted)] mb-3">
            You're {lecture.progress}% through this {lecture.type} lecture.
            {lecture.progress > 50 ? " Great progress! " : " Keep going! "}
            AI insights will help reinforce key concepts.
          </p>
          <div className="bg-[var(--bg-secondary)] rounded-full h-2">
            <div
              className="bg-[var(--accent-primary)] h-2 rounded-full transition-all duration-300"
              style={{ width: `${lecture.progress}%` }}
            />
          </div>
        </div>

        {/* Upcoming Content */}
        <div className="bg-[var(--accent-secondary)]/30 rounded-xl p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Clock className="w-4 h-4 text-[var(--highlight)]" />
            <h4 className="font-medium text-[var(--text-primary)]">
              Coming Up
            </h4>
          </div>
          <div className="space-y-2">
            {upcomingContent.map((item, index) => (
              <div
                key={index}
                className={`flex items-center justify-between text-sm ${
                  lecture.type === "pdf" && item.page && onJumpToPage
                    ? "cursor-pointer hover:bg-[var(--bg-primary)] p-2 rounded-lg transition-colors"
                    : ""
                }`}
                onClick={() => {
                  if (lecture.type === "pdf" && item.page && onJumpToPage) {
                    onJumpToPage(item.page);
                  }
                }}
              >
                <span className="text-[var(--text-muted)]">{item.topic}</span>
                <span className="text-xs text-[var(--text-subtle)] geist-mono">
                  {item.time}
                  {lecture.type === "pdf" && item.page && onJumpToPage && (
                    <span className="ml-2 text-[var(--accent-primary)]">→</span>
                  )}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Notes */}
        <div className="bg-white rounded-xl p-4 border border-gray-100">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-medium text-[var(--text-primary)]">
              Quick Notes
            </h4>
            <button className="text-[var(--accent-primary)] hover:text-[var(--highlight)] transition-colors">
              <Lightbulb className="w-4 h-4" />
            </button>
          </div>
          <div className="text-sm text-[var(--text-muted)] text-center py-4">
            AI-generated notes and insights will appear here as you progress
            through the lecture.
          </div>
        </div>
      </div>

      {/* AI Input */}
      <div className="p-6 border-t border-[var(--accent-tertiary)]/50 bg-white flex-shrink-0">
        <div className="relative">
          <textarea
            className="w-full bg-[var(--bg-primary)] border border-[var(--bg-secondary)] rounded-lg p-3 pr-12 text-sm resize-none focus:outline-none focus:bg-white focus:border-[var(--accent-primary)]/50 focus:ring-4 focus:ring-[var(--accent-primary)]/10 transition-all duration-300"
            rows={3}
            placeholder="Ask about this section, request examples, or jump to topics..."
          />
          <button className="absolute bottom-3 right-3 w-8 h-8 bg-[var(--accent-primary)] rounded-full flex items-center justify-center text-white hover:bg-[var(--highlight)] transition-colors">
            <Send className="w-4 h-4" />
          </button>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 gap-2 mt-3">
          <button className="p-2 text-xs text-center border border-[var(--bg-secondary)] hover:bg-[var(--bg-secondary)] transition-colors rounded-lg">
            <Bookmark className="w-4 h-4 mx-auto mb-1 text-[var(--accent-primary)]" />
            Make Flashcards
          </button>
          <button className="p-2 text-xs text-center border border-[var(--bg-secondary)] hover:bg-[var(--bg-secondary)] transition-colors rounded-lg">
            <HelpCircle className="w-4 h-4 mx-auto mb-1 text-[var(--accent-primary)]" />
            Quiz Me
          </button>
        </div>
      </div>
    </aside>
  );
}
