"use client";

import React, { useRef, useCallback, useEffect } from "react";
import MuxPlayer from "@mux/mux-player-react";

interface Chapter {
  id: string;
  title: string;
  startTime: number;
  endTime: number;
  timestamp: string;
}

interface MuxVideoPlayerProps {
  lecture: {
    id: string;
    title: string;
    playbackId?: string;
    currentTime?: number;
    progress: number;
  };
  chapters: Chapter[];
  onTimeUpdate?: (currentTime: number) => void;
  onChapterChange?: (chapterId: string) => void;
  onProgressUpdate?: (progress: number) => void;
  userId?: string;
}

export function MuxVideoPlayer({
  lecture,
  chapters,
  onTimeUpdate,
  onChapterChange,
  onProgressUpdate,
  userId = "demo-user",
}: MuxVideoPlayerProps) {
  const playerRef = useRef<any>(null);
  const currentChapterRef = useRef<string | null>(null);

  // Mock playback ID for demo - in real app this would come from Convex
  const playbackId = lecture.playbackId || "a4nOgmxGWg6gULfcBbAa00gXyfcwPnAFldF8RdsNyk8M";

  const handleTimeUpdate = useCallback(() => {
    if (!playerRef.current) return;

    const currentTime = playerRef.current.currentTime;
    const duration = playerRef.current.duration;

    if (onTimeUpdate) {
      onTimeUpdate(currentTime);
    }

    // Update progress
    if (duration && onProgressUpdate) {
      const progress = (currentTime / duration) * 100;
      onProgressUpdate(Math.round(progress));
    }

    // Check for chapter changes
    if (chapters.length > 0) {
      const currentChapter = chapters.find(
        (chapter) => currentTime >= chapter.startTime && currentTime < chapter.endTime
      );

      if (currentChapter && currentChapter.id !== currentChapterRef.current) {
        currentChapterRef.current = currentChapter.id;
        if (onChapterChange) {
          onChapterChange(currentChapter.id);
        }
      }
    }
  }, [chapters, onTimeUpdate, onChapterChange, onProgressUpdate]);

  const handleLoadedData = useCallback(() => {
    // Seek to stored position if available
    if (lecture.currentTime && playerRef.current) {
      playerRef.current.currentTime = lecture.currentTime;
    }
  }, [lecture.currentTime]);

  // Method to jump to specific chapter
  const jumpToChapter = useCallback((chapterId: string) => {
    const chapter = chapters.find((c) => c.id === chapterId);
    if (chapter && playerRef.current) {
      playerRef.current.currentTime = chapter.startTime;
    }
  }, [chapters]);

  // Method to jump to specific time
  const jumpToTime = useCallback((time: number) => {
    if (playerRef.current) {
      playerRef.current.currentTime = time;
    }
  }, []);

  // Expose methods to parent component
  useEffect(() => {
    if (playerRef.current) {
      playerRef.current.jumpToChapter = jumpToChapter;
      playerRef.current.jumpToTime = jumpToTime;
    }
  }, [jumpToChapter, jumpToTime]);

  return (
    <div className="learning-focus h-full relative group">
      <MuxPlayer
        ref={playerRef}
        playbackId={playbackId}
        accentColor="var(--accent-primary)"
        className="w-full h-full"
        metadata={{
          video_id: lecture.id,
          video_title: lecture.title,
          viewer_user_id: userId,
        }}
        streamType="on-demand"
        onTimeUpdate={handleTimeUpdate}
        onLoadedData={handleLoadedData}
        style={{
          width: "100%",
          height: "100%",
          borderRadius: "1rem",
          overflow: "hidden",
          boxShadow: "0 20px 32px -8px rgba(45, 55, 72, 0.15), 0 8px 16px -4px rgba(45, 55, 72, 0.1)",
        }}
      />
    </div>
  );
}