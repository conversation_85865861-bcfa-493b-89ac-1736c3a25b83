"use client";

import React, { useState } from "react";
import { List, Type } from "lucide-react";

interface Chapter {
  id: string;
  title: string;
  startTime: number;
  endTime: number;
  timestamp: string;
}

interface TranscriptItem {
  timestamp: string;
  time: number;
  title: string;
  content: string;
  isActive?: boolean;
}

interface ChapterNavigationProps {
  chapters: Chapter[];
  transcripts: TranscriptItem[];
  currentChapter?: string;
  onChapterClick: (chapterId: string) => void;
  onTranscriptClick: (time: number) => void;
}

export function ChapterNavigation({
  chapters,
  transcripts,
  currentChapter,
  onChapterClick,
  onTranscriptClick,
}: ChapterNavigationProps) {
  const [activeTab, setActiveTab] = useState<"chapters" | "transcripts">("chapters");

  return (
    <div className="bg-white border-t border-[var(--bg-secondary)] p-6">
      {/* Tabs */}
      <div className="flex items-center space-x-1 mb-6">
        <button
          onClick={() => setActiveTab("chapters")}
          className={`tab-button flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
            activeTab === "chapters" ? "tab-active" : ""
          }`}
        >
          <List className="w-4 h-4 mr-2" />
          Chapters
        </button>
        <button
          onClick={() => setActiveTab("transcripts")}
          className={`tab-button flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
            activeTab === "transcripts" ? "tab-active" : ""
          }`}
        >
          <Type className="w-4 h-4 mr-2" />
          Transcripts
        </button>
      </div>

      {/* Chapters View */}
      {activeTab === "chapters" && (
        <div className="tab-content">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-3">
            {chapters.map((chapter) => (
              <div
                key={chapter.id}
                onClick={() => onChapterClick(chapter.id)}
                className={`sidebar-item p-3 border cursor-pointer transition-all ${
                  currentChapter === chapter.id
                    ? "chapter-current border-[var(--accent-primary)] bg-[var(--accent-primary)]/15"
                    : "border-[var(--bg-secondary)] hover:bg-[var(--bg-secondary)]"
                }`}
              >
                <div className="text-sm font-medium text-[var(--text-primary)]">
                  {chapter.title}
                </div>
                <div className="text-xs text-[var(--text-muted)] mt-1">
                  {chapter.timestamp}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Transcripts View */}
      {activeTab === "transcripts" && (
        <div className="tab-content">
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {transcripts.map((item, index) => (
              <div
                key={index}
                onClick={() => onTranscriptClick(item.time)}
                className={`transcript-item cursor-pointer transition-all border-l-2 pl-4 ${
                  item.isActive
                    ? "border-[var(--accent-primary)] bg-[var(--bg-primary)] p-3 rounded-r-lg"
                    : "border-gray-200 hover:bg-[var(--bg-primary)] hover:border-[var(--accent-primary)]/50 hover:rounded-r-lg hover:p-3 hover:ml-0"
                }`}
              >
                <div className="flex items-center space-x-2 mb-2">
                  <span
                    className={`text-xs font-mono px-2 py-1 rounded ${
                      item.isActive
                        ? "bg-[var(--accent-primary)] text-white"
                        : "bg-[var(--bg-secondary)]"
                    }`}
                  >
                    {item.timestamp}
                  </span>
                  <h4 className="text-sm font-medium text-[var(--text-primary)]">
                    {item.title}
                  </h4>
                  {item.isActive && (
                    <span className="text-xs bg-[var(--accent-primary)] text-white px-2 py-1 rounded-full">
                      Currently Playing
                    </span>
                  )}
                </div>
                <p className="text-sm text-[var(--text-secondary)] leading-relaxed">
                  {item.content}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}