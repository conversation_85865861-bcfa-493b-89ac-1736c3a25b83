import React from "react";
import { fetchQuery } from "convex/nextjs";
import { api } from "@/convex/_generated/api";
import { convexAuthNextjsToken } from "@convex-dev/auth/nextjs/server";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { CourseHeroSection } from "./components/course-hero-section";
import { LectureList } from "./components/lecture-list";
import { Id } from "@/convex/_generated/dataModel";

interface CourseDetailPageProps {
  params: Promise<{
    courseId: string;
  }>;
}

// Mock lecture data - in real app, this would come from Convex
const mockLectureData = [
  {
    id: "intro",
    title: "Introduction to Microeconomics",
    week: 1,
    duration: "45 min",
    type: "video" as const,
    status: "completed" as const,
  },
  {
    id: "scarcity",
    title: "Scarcity and Resource Allocation",
    week: 1,
    duration: "30 min",
    type: "pdf" as const,
    status: "completed" as const,
  },
  {
    id: "supply-demand",
    title: "Supply and Demand Fundamentals",
    week: 2,
    duration: "52 min",
    type: "video" as const,
    status: "completed" as const,
  },
  {
    id: "equilibrium",
    title: "Market Equilibrium and Price Determination",
    week: 2,
    duration: "38 min",
    type: "audio" as const,
    status: "completed" as const,
  },
  {
    id: "market-structures",
    title: "Market Structures and Competition",
    week: 3,
    duration: "42 min",
    type: "pdf" as const,
    status: "current" as const,
  },
  {
    id: "perfect-competition",
    title: "Perfect Competition Analysis",
    week: 3,
    duration: "48 min",
    type: "video" as const,
    status: "locked" as const,
  },
  {
    id: "monopoly",
    title: "Monopoly and Market Power",
    week: 4,
    duration: "40 min",
    type: "pdf" as const,
    status: "locked" as const,
  },
  {
    id: "oligopoly",
    title: "Oligopoly and Game Theory",
    week: 4,
    duration: "55 min",
    type: "audio" as const,
    status: "locked" as const,
  },
  {
    id: "consumer-theory",
    title: "Consumer Choice Theory",
    week: 5,
    duration: "46 min",
    type: "video" as const,
    status: "locked" as const,
  },
  {
    id: "production-costs",
    title: "Production and Cost Analysis",
    week: 5,
    duration: "35 min",
    type: "pdf" as const,
    status: "locked" as const,
  },
];

// Server-side data fetching
async function getCourseData(courseId: string) {
  try {
    const course = await fetchQuery(
      api.learnings.getCourse,
      { courseId: courseId as Id<"courses"> },
      {
        token: await convexAuthNextjsToken(),
      }
    );

    if (!course) {
      // Return mock data for demo
      return {
        course: {
          _id: courseId,
          title: "Microeconomics Fundamentals",
          description: "A comprehensive introduction to microeconomic principles",
          instructor: "Prof. Dr. Michael Johnson",
          progress: 67,
          totalLectures: 24,
          currentLecture: 9,
          thumbnailUrl: null,
        },
        lectures: mockLectureData,
      };
    }

    return {
      course,
      lectures: mockLectureData, // Replace with real lecture data when available
    };
  } catch (error) {
    // Return mock data for demo
    return {
      course: {
        _id: courseId,
        title: "Microeconomics Fundamentals",
        description: "A comprehensive introduction to microeconomic principles",
        instructor: "Prof. Dr. Michael Johnson",
        progress: 67,
        totalLectures: 24,
        currentLecture: 9,
        thumbnailUrl: null,
      },
      lectures: mockLectureData,
    };
  }
}

export default async function CourseDetailPage({ params }: CourseDetailPageProps) {
  // Await params to access its properties (Next.js 15 pattern)
  const { courseId } = await params;
  const { course, lectures } = await getCourseData(courseId);

  const currentLecture = lectures.find(lecture => lecture.status === "current");
  const completedLectures = lectures.filter(lecture => lecture.status === "completed");

  return (
    <DashboardLayout>
      <div className="flex-1 p-4 lg:p-8 pt-6 lg:pt-10 overflow-y-auto">
        <CourseHeroSection
          course={course}
          currentLecture={currentLecture}
          completedCount={completedLectures.length}
        />
        <LectureList lectures={lectures} courseId={courseId} />
      </div>
    </DashboardLayout>
  );
}