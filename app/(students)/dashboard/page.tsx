import React from "react";
import { PlayCircle, Activity, ArrowRight } from "lucide-react";
import { fetchQuery } from "convex/nextjs";
import { api } from "@/convex/_generated/api";
import { convexAuthNextjsToken } from "@convex-dev/auth/nextjs/server";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { HeroSection } from "@/components/dashboard/hero-section";
import { ContinueLearningClient } from "@/components/dashboard/continue-learning-client";
import { ActivityFeedClient } from "@/components/dashboard/activity-feed-client";

// Server-side data fetching using Convex
async function getDashboardData() {
  const user = await fetchQuery(
    api.users.currentUser,
    {},
    {
      token: await convexAuthNextjsToken(),
    }
  );

  if (!user) {
    // Handle unauthenticated state
    return {
      userStats: {
        streak: "0",
        avgScore: "0%",
        activeCourses: "0",
        studyTime: "0h",
      },
      userName: "Guest",
    };
  }

  const userStats = {
    streak: user.streak.toString(),
    avgScore: user.avgScore,
    activeCourses: user.activeCourses.toString(),
    studyTime: user.studyTime,
  };

  const userName = user.firstName || user.name || "Student";

  return {
    userStats,
    userName,
  };
}

export default async function DashboardPage() {
  const { userStats, userName } = await getDashboardData();

  return (
    <DashboardLayout>
      <div className="flex-1 p-4 lg:p-8 pt-6 lg:pt-10 overflow-y-auto">
        <HeroSection userName={userName} stats={userStats} />

        <section className="mb-8 lg:mb-10">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-5 gap-3">
            <h3 className="text-xl lg:text-2xl font-semibold text-[var(--text-primary)] flex items-center">
              <PlayCircle className="w-6 h-6 lg:w-7 lg:h-7 mr-2 lg:mr-3 icon-primary" />
              Continue Learning
            </h3>
            <a
              className="text-sm lg:text-md font-medium text-[var(--text-secondary)] hover:text-[var(--accent-primary)] flex items-center transition-colors focus-ring self-start sm:self-auto"
              href="/courses"
            >
              View All Courses
              <ArrowRight className="w-4 h-4 lg:w-5 lg:h-5 ml-1.5" />
            </a>
          </div>
          <ContinueLearningClient />
        </section>

        <section>
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-5 gap-3">
            <h3 className="text-xl lg:text-2xl font-semibold text-[var(--text-primary)] flex items-center">
              <Activity className="w-6 h-6 lg:w-7 lg:h-7 mr-2 lg:mr-3 icon-primary" />
              Recent Activity
            </h3>
            <a
              className="text-sm lg:text-md font-medium text-[var(--text-secondary)] hover:text-[var(--accent-primary)] flex items-center transition-colors focus-ring self-start sm:self-auto"
              href="/activity"
            >
              View All
              <ArrowRight className="w-4 h-4 lg:w-5 lg:h-5 ml-1.5" />
            </a>
          </div>
          <ActivityFeedClient />
        </section>
      </div>
    </DashboardLayout>
  );
}
