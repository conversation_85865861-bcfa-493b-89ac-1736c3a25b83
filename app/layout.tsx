import type React from "react";
import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "geist/font/sans";
import { Geist<PERSON><PERSON> } from "geist/font/mono";
import { ConvexAuthNextjsServerProvider } from "@convex-dev/auth/nextjs/server";

import "../styles/globals.css";
import { ConvexClientProvider } from "./ConvexClientProvider";

export const metadata: Metadata = {
  title: "StudyMind - AI-Powered Learning Platform",
  description:
    "Transform your learning experience with AI-powered study tools and personalized education",
  keywords:
    "AI learning, education, study tools, personalized learning, StudyMind",
  authors: [{ name: "StudyMind Team" }],
  creator: "StudyMind.ai",
  publisher: "StudyMind.ai",
  metadataBase: new URL("https://studymind.ai"),
  openGraph: {
    title: "StudyMind - AI-Powered Learning Platform",
    description:
      "Transform your learning experience with AI-powered study tools and personalized education",
    url: "https://studymind.ai",
    siteName: "StudyMind",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "StudyMind - AI-Powered Learning Platform",
    description:
      "Transform your learning experience with AI-powered study tools and personalized education",
    creator: "@studymind_ai",
  },
  generator: "v0.dev",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ConvexAuthNextjsServerProvider>
      <html lang="en" className={`${GeistSans.variable} ${GeistMono.variable} antialiased`}>
        <body className="antialiased h-screen">
          <ConvexClientProvider>{children}</ConvexClientProvider>
        </body>
      </html>
    </ConvexAuthNextjsServerProvider>
  );
}
