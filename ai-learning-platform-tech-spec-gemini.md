## **Backend Tech Spec: AI-Assisted Learning Platform**

### **1. Overview**

This document outlines the backend architecture for an agentic AI learning platform. The backend will be built exclusively on **Convex**, utilizing its powerful components to handle complex, asynchronous tasks, AI interactions, and data aggregation.

The architecture is centered around two primary user flows:

1.  **Content Creator Flow:** An agentic workflow, managed by **Convex Workflows**, will orchestrate the entire content ingestion and processing pipeline. This includes handling file uploads, interacting with the **Mux API** for video processing, generating AI-enhanced content (summaries, chapters, etc.), and updating the course structure. We will use **Convex Workpool** to manage the concurrency of expensive AI generation tasks.
2.  **Course Consumer Flow:** A sophisticated **AI Tutor**, built with the **Convex Agent** component, will provide an interactive and personalized learning experience. The agent will leverage Retrieval-Augmented Generation (RAG) against course-specific vector embeddings. Student analytics and learning pattern recognition will be powered by the **Convex Aggregate** component for efficient, real-time data processing.

The foundation of our backend is the provided `schema.ts`, which is robust and well-suited for this application. We will adhere to this schema and suggest minor, non-breaking additions where necessary.

---

### **2. Core Component Setup**

Before implementing the specific flows, we'll configure the required Convex components in `convex/convex.config.ts`.

```typescript
// convex/convex.config.ts
import { defineApp } from "convex/server";
import workflow from "@convex-dev/workflow/convex.config";
import agent from "@convex-dev/agent/convex.config";
import workpool from "@convex-dev/workpool/convex.config";
import aggregate from "@convex-dev/aggregate/convex.config";

const app = defineApp();

// Register all necessary components
app.use(workflow);
app.use(agent);
app.use(aggregate);

// Define a dedicated workpool for AI content generation to control costs and load
app.use(workpool, { name: "aiGenerationWorkpool" });

export default app;
```

We will then instantiate managers for the workflow and workpool components in a central file.

```typescript
// convex/lib/managers.ts
import { WorkflowManager } from "@convex-dev/workflow";
import { Workpool } from "@convex-dev/workpool";
import { components } from "../_generated/api";

export const workflow = new WorkflowManager(components.workflow);

// Create a pool with max parallelism of 3 for AI tasks
export const aiGenerationPool = new Workpool(components.aiGenerationWorkpool, {
  maxParallelism: 3,
  defaultRetryBehavior: { maxAttempts: 3, initialBackoffMs: 5000 },
});
```

---

### **3. Content Creator Flow: Agentic Content Generation**

This flow outlines the automated process from a creator uploading video files to a fully-formed, AI-enriched course lecture.

#### **Step 3.1: Course Initiation and File Upload**

The process begins when the creator drops files into the UI.

1.  **Client-Side:** The frontend calls a mutation to create a `courses` record and a `lectures` record for each file being uploaded.
2.  **Backend (`convex/courses.ts`):** This mutation initializes the records with a `processing` status and generates a Mux direct upload URL for each video file.

```typescript
// convex/courses.ts
import { mutation } from "./_generated/server";
import { v } from "convex/values";
import { Mux } from "@mux/mux-node";
import { internal } from "./_generated/api";
import { workflow } from "./lib/managers";

const { MUX_TOKEN_ID, MUX_TOKEN_SECRET } = process.env;
const mux = new Mux({ tokenId: MUX_TOKEN_ID, secretKey: MUX_TOKEN_SECRET });

export const createCourseWithUploads = mutation({
  args: {
    price: v.number(),
    files: v.array(
      v.object({
        fileName: v.string(),
        type: v.union(v.literal("video"), v.literal("audio"), v.literal("pdf")),
      })
    ),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Authentication required.");

    // 1. Create the parent course
    const courseId = await ctx.db.insert("courses", {
      creatorId: identity.subject,
      price: args.price,
      status: "processing",
    });

    // 2. For each file, create a lecture and a Mux upload URL
    for (const [index, file] of args.files.entries()) {
      // Create lecture record in "pending" state
      const lectureId = await ctx.db.insert("lectures", {
        courseId,
        creatorId: identity.subject,
        order: index + 1,
        type: file.type,
        status: "pending",
      });

      // For video, generate Mux direct upload URL
      if (file.type === "video") {
        const upload = await mux.video.uploads.create({
          new_asset_settings: {
            playback_policy: ["public"],
            // Pass lectureId through to the webhook
            passthrough: lectureId,
          },
          cors_origin: "*", // Or your app's domain
        });

        // Start the processing workflow
        await workflow.start(ctx, internal.workflows.videoProcessingWorkflow, {
          lectureId,
          muxUploadId: upload.id,
        });

        // The client will use this URL to upload the file directly to Mux
        // And then patch the lecture record with the uploadId
      }
      // Add logic for audio/pdf workflows here
    }
    return { courseId };
  },
});
```

#### **Step 3.2: The `videoProcessingWorkflow`**

This workflow is the brain of the content generation process. It's durable and will orchestrate all steps from Mux processing to AI enrichment.

```typescript
// convex/workflows.ts
import { v } from "convex/values";
import { internal } from "./_generated/api";
import { workflow, aiGenerationPool } from "./lib/managers";

export const videoProcessingWorkflow = workflow.define({
    args: { lectureId: v.id("lectures"), muxUploadId: v.string() },
    handler: async (step, { lectureId, muxUploadId }) => {

      // Step 1: Wait for Mux to process the asset.
      // The Mux webhook (handleMuxWebhook) will update the lecture's status and asset IDs.
      // This step waits until that happens.
      const { playbackId, assetId, duration } = await step.waitFor(
        internal.lectures.getMuxAssetDetails, { lectureId }
      );

      // Step 2: Once the asset is ready, enqueue AI generation tasks into our workpool.
      // This controls concurrency for expensive LLM calls.
      const aiTasks = [
        "generateTitle",
        "generateSummary",
        "generateChapters",
        "generateInsights",
      ];

      await Promise.all(
        aiTasks.map(taskName =>
            aiGenerationPool.enqueueAction(
                step.context, // Use the workflow's context
                internal.ai.runVideoGeneration,
                { lectureId, playbackId, task: taskName }
            )
        )
      );

      // Step 3 (Final): Update the parent course with aggregated data.
      await step.runAction(internal.courses.updateCourse aggregates, {
        courseId: (await step.runQuery(internal.lectures.getCourseId, { lectureId }))
      });
    },
});
```

#### **Step 3.3: Handling the Mux Webhook**

We need an HTTP Action to receive webhooks from Mux, specifically the `video.asset.ready` event.

```typescript
// convex/http.ts
import { httpRouter } from "convex/server";
import { httpAction } from "./_generated/server";
import { internal } from "./_generated/api";

const http = httpRouter();

http.route({
  path: "/mux-webhook",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    // TODO: Add webhook signature verification for security
    const payload = await request.json();

    if (payload.type === "video.asset.ready") {
      const {
        passthrough: lectureId,
        id: assetId,
        playback_ids,
        duration,
      } = payload.data;
      const playbackId = playback_ids[0].id;

      // Update the lecture record. This will unblock the workflow.
      await ctx.runMutation(internal.lectures.updateMuxData, {
        lectureId,
        muxAssetId: assetId,
        muxPlaybackId: playbackId,
        durationSeconds: duration,
        status: "processing", // Now ready for AI processing
      });
    }
    return new Response(null, { status: 200 });
  }),
});

export default http;
```

#### **Step 3.4: AI Content Generation & RAG Embedding**

The actions enqueued by the workflow will fetch the transcript and use the Vercel AI SDK to generate content.

```typescript
// convex/ai.ts
import { internalAction } from "./_generated/server";
import { v } from "convex/values";
import { openai } from "@ai-sdk/openai";
import { generateText, streamText } from "ai";
import { internal } from "./_generated/api";

// Fetches VTT transcript from Mux
async function getTranscript(playbackId: string): Promise<string> {
  const trackUrl = `https://stream.mux.com/${playbackId}/text/webvtt`;
  const response = await fetch(trackUrl);
  if (!response.ok) throw new Error("Failed to fetch transcript.");
  // Basic VTT cleaning, can be improved
  return (await response.text())
    .replace(/WEBVTT\n\n/g, "")
    .replace(
      /(\d{2}:\d{2}:\d{2}\.\d{3}) --> (\d{2}:\d{2}:\d{2}\.\d{3})\n/g,
      ""
    );
}

export const runVideoGeneration = internalAction({
  args: {
    lectureId: v.id("lectures"),
    playbackId: v.string(),
    task: v.string(), // "generateTitle", "generateSummary", etc.
  },
  handler: async (ctx, { lectureId, playbackId, task }) => {
    const transcript = await getTranscript(playbackId);

    let systemPrompt = "";
    let patchData = {};

    // Logic for different AI tasks
    switch (task) {
      case "generateTitle":
        systemPrompt = `Based on the following transcript, generate a concise, engaging title for the video lecture. Max 10 words.`;
        const { text: title } = await generateText({
          model: openai("gpt-4o-mini"),
          system: systemPrompt,
          prompt: transcript,
        });
        patchData = { generatedTitle: title };
        break;
      case "generateSummary":
        systemPrompt = `Summarize the key points of this lecture transcript in a single paragraph.`;
        const { text: summary } = await generateText({
          model: openai("gpt-4o-mini"),
          system: systemPrompt,
          prompt: transcript,
        });
        patchData = { summary: summary };

        // After generating summary, create embeddings for RAG
        await ctx.scheduler.runAfter(
          0,
          internal.ai.createEmbeddingsForLecture,
          { lectureId, textToEmbed: transcript }
        );
        break;
      // ... other cases for chapters, insights
    }

    await ctx.runMutation(internal.lectures.updateAIGeneratedContent, {
      lectureId,
      data: patchData,
    });
  },
});

export const createEmbeddingsForLecture = internalAction({
  args: { lectureId: v.id("lectures"), textToEmbed: v.string() },
  handler: async (ctx, { lectureId, textToEmbed }) => {
    // Simple chunking, can be improved
    const chunks = textToEmbed.match(/.{1,1000}/g) || [];
    const lecture = await ctx.runQuery(internal.lectures.get, { lectureId });
    if (!lecture) return;

    for (const chunk of chunks) {
      const { embedding } = await generateEmbedding({
        model: openai.embedding("text-embedding-3-small"),
        value: chunk,
      });
      await ctx.runMutation(internal.lectures.storeEmbedding, {
        courseId: lecture.courseId,
        lectureId,
        textChunk: chunk,
        embedding,
      });
    }
  },
});
```

---

### **4. Course Consumer Flow: Interactive Learning**

This flow details how a student interacts with the course content and the AI Tutor.

#### **Step 4.1: Learning Analytics & Progress**

We will use the **Convex Aggregate** component to efficiently calculate student analytics without expensive queries on every page load.

```typescript
// convex/analytics.ts
import { defineAggregation } from "@convex-dev/aggregate";
import { internalAction } from "./_generated/server";
import { backfill } from "@convex-dev/aggregate/server";

// Aggregate user activity by user and day
export const { daily: dailyActivity, get: getActivity } = defineAggregation(
  "userActivityByDay",
  {
    source: "userActivity",
    by: {
      day: "day", // Group by creation time (day)
      user: "userId", // And by user
    },
    aggregators: {
      count: {
        kind: "count",
      },
      // Could also sum `duration` if userActivity logged it
    },
  }
);

// Action to backfill existing data
export const backfillUserActivity = internalAction({
  handler: (ctx) => backfill(ctx, "userActivityByDay"),
});
```

The frontend can now query `dailyActivity` to build charts showing a user's activity over time (e.g., hours/week).

#### **Step 4.2: The AI Tutor Agent**

The AI Tutor is the core of the interactive experience. We'll define it using the **Convex Agent** component.

```typescript
// convex/tutor.ts
import { Agent, createTool } from "@convex-dev/agent";
import { openai } from "@ai-sdk/openai";
import { z } from "zod";
import { v } from "convex/values";
import { action, internalQuery } from "./_generated/server";
import { Doc, Id } from "./_generated/dataModel";
import { components, internal } from "./_generated/api";

// Define the agent
export const tutorAgent = new Agent(components.agent, {
  chat: openai.chat("gpt-4o"),
  textEmbedding: openai.embedding("text-embedding-3-small"),

  // The agent's persona and instructions
  instructions: `You are an expert AI Tutor. Your goal is to help students understand course material.
    - Be encouraging and supportive.
    - Use the provided tools to answer questions and create learning aids.
    - When answering questions, first search for relevant context from the course material.
    - Always cite the specific lecture or timestamp when referencing material.`,

  // Define tools the agent can use
  tools: {
    findRelevantContext: createTool({
      description:
        "Search the course material (transcripts) to find context relevant to the user's question.",
      args: z.object({ query: z.string() }),
      handler: async (ctx, { query }): Promise<Doc<"courseEmbeddings">[]> => {
        const results = await ctx.vectorSearch(
          "courseEmbeddings",
          "by_embedding",
          {
            vector: (
              await generateEmbedding({
                model: openai.embedding("text-embedding-3-small"),
                value: query,
              })
            ).embedding,
            limit: 5,
          }
        );
        return results;
      },
    }),
    generateQuiz: createTool({
      description:
        "Create a multiple-choice quiz on a specific topic or lecture.",
      args: z.object({ topic: z.string(), lectureId: z.string().optional() }),
      handler: async (ctx, { topic, lectureId }) => {
        // This would be a more complex action that generates questions/answers
        // and saves them to the 'quizzes' and 'questions' tables.
        return `Quiz on "${topic}" has been created!`;
      },
    }),
    //... add more tools for flashcards, notes, etc.
  },
});

// Action to start a conversation with the tutor
export const startTutorConversation = action({
  args: {
    courseId: v.id("courses"),
    prompt: v.string(),
  },
  handler: async (ctx, { courseId, prompt }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    // Create a new conversation thread associated with the user and course
    const { threadId, thread } = await tutorAgent.createThread(ctx, {
      userId: identity.subject,
      // You could extend the agent's schema to store courseId on the thread
    });

    const { text } = await thread.generateText({ prompt });
    return { threadId, text };
  },
});

// Action to continue an existing conversation
export const continueTutorConversation = action({
  args: { threadId: v.string(), prompt: v.string() },
  handler: async (ctx, { threadId, prompt }) => {
    const { thread } = await tutorAgent.continueThread(ctx, { threadId });
    const { text } = await thread.generateText({ prompt });
    return text;
  },
});
```

The frontend will use the `useThreadMessages` hook from `@convex-dev/agent/react` to reactively display the conversation, providing a real-time chat experience.

---

### **5. Recommended Schema Modifications**

The provided `schema.ts` is excellent. Based on the detailed flows above, here are two minor, non-breaking suggestions for consideration:

1.  **Add `courseId` to `aiTutorConversations` `index`**: The schema has `aiTutorConversations` indexed by `by_user_course`. This is perfect for fetching all conversations for a user in a course. No changes needed here, it's well-designed.
2.  **Add `threadId` to `notes`, `quizzes`, `flashcardDecks`**: To link AI-generated learning aids back to the specific conversation that created them, we could add an optional `threadId`:
    - `threadId: v.optional(v.id("threads"))`
      This would allow for features like "Show me the part of our chat where we created this quiz."

These are minor enhancements and the current schema is fully capable of delivering the core functionality described.
