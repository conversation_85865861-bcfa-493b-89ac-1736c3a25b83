@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Warm Minimalism Color System */
  --bg-primary: #fdfbf6;
  --bg-secondary: #f0e5d8;
  --text-primary: #2d3748;
  --text-secondary: #4a5568;
  --text-muted: #718096;
  --text-subtle: #a0aec0;
  --text-on-tinted: #2d3748;
  --text-on-tinted-muted: #4a5568;
  --text-on-tinted-subtle: #718096;
  --accent-primary: #a9c2b9;
  --accent-secondary: #d3b8ae;
  --accent-tertiary: #e6cba5;
  --highlight: #8b9d8f;
  --success: #68d391;
  --warning: #f6ad55;
  --info: #63b3ed;
  
  /* Video-specific colors */
  --video-bg: #1a1c1f;
  
  /* PDF-specific colors */
  --paper-white: #ffffff;
  --paper-shadow: rgba(45, 55, 72, 0.08);
  
  /* Border radius system */
  --radius: 0.5rem; /* 8px - corresponds to rounded-lg */
}

body {
  font-family: var(--font-geist-sans), sans-serif;
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.geist-mono {
  font-family: var(--font-geist-mono), monospace;
}

/* Animation Definitions */
.animated-gradient {
  background: linear-gradient(
    -45deg,
    var(--accent-tertiary),
    var(--bg-secondary),
    var(--accent-primary),
    var(--highlight)
  );
  background-size: 400% 400%;
  animation: gradientBG 20s ease infinite;
}

@keyframes gradientBG {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.stagger-entrance > * {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.progress-bar-fill {
  transition: width 0.7s cubic-bezier(0.65, 0, 0.35, 1);
}

.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 12px 20px -5px rgba(45, 55, 72, 0.15),
    0 6px 8px -3px rgba(45, 55, 72, 0.1);
}

.sidebar-card-item:hover {
  background-color: var(--bg-secondary);
  transform: translateX(2px);
}

.nav-link-active {
  color: var(--text-primary);
  font-weight: 600;
  border-bottom-width: 2px;
  border-color: var(--accent-primary);
  padding-bottom: 0.25rem;
}

.nav-link {
  color: var(--text-muted);
  font-weight: 500;
}

.nav-link:hover {
  color: var(--text-secondary);
  transition-duration: 300ms;
}

.icon-primary {
  color: var(--accent-primary);
}

.icon-secondary {
  color: var(--accent-secondary);
}

.text-highlight {
  color: var(--highlight);
  font-weight: 600;
}

.minimal-artwork-bg {
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23e6cba5' fill-opacity='0.1'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  background-repeat: repeat;
  background-size: 200px 200px;
}

.minimal-artwork-subtle {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23a9c2b9' fill-opacity='0.07'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  background-repeat: repeat;
}

/* Enhanced button styles */
.btn-primary {
  background: var(--accent-primary);
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.btn-primary:hover {
  background: var(--highlight);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(169, 194, 185, 0.3);
}

/* Better focus states */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(169, 194, 185, 0.3);
}

.focus-ring:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px rgba(169, 194, 185, 0.3);
  border-radius: 8px;
}

/* Smooth transitions for all interactive elements */
button, a, [tabindex="0"] {
  transition: all 0.2s ease;
}

/* Video Learning Styles */
.learning-focus {
  background: var(--video-bg);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 20px 32px -8px rgba(45, 55, 72, 0.15),
    0 8px 16px -4px rgba(45, 55, 72, 0.1);
}

/* Tab Styles */
.tab-button {
  background: var(--bg-primary);
  color: var(--text-muted);
  border: 1px solid var(--bg-secondary);
}

.tab-button:hover {
  background: var(--bg-secondary);
  color: var(--text-secondary);
}

.tab-active {
  background: var(--accent-primary) !important;
  color: white !important;
  border-color: var(--accent-primary) !important;
}

/* PDF Styles */
.pdf-document {
  background: var(--paper-white);
  border-radius: 1rem;
  box-shadow: 0 8px 32px -4px var(--paper-shadow),
    0 4px 16px -2px var(--paper-shadow);
  overflow: hidden;
  max-width: 800px;
  margin: 0 auto;
  transition: all 0.3s ease;
}

.pdf-page {
  padding: 3rem;
  line-height: 1.7;
  font-size: 16px;
  color: var(--text-primary);
}

/* Page Thumbnail Styles */
.page-thumbnail {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 0.5rem;
  padding: 0.25rem;
  position: relative;
}

.page-thumbnail:hover {
  background: var(--bg-secondary);
  transform: scale(1.05);
}

.page-thumbnail.current {
  background: rgba(169, 194, 185, 0.2);
  border: 2px solid var(--accent-primary);
}

.page-thumbnail.active {
  background: rgba(169, 194, 185, 0.1);
  border: 1px solid var(--accent-tertiary);
}

/* PDF Focus Mode */
.focus-mode .pdf-container {
  width: 100%;
}

/* Zoom Controls */
.zoom-controls {
  background: var(--bg-secondary);
  border-radius: 0.75rem;
  padding: 0.5rem;
  display: flex;
  gap: 0.25rem;
}

.zoom-btn {
  background: transparent;
  border: none;
  color: var(--text-muted);
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.zoom-btn:hover {
  background: var(--accent-tertiary);
  color: var(--text-primary);
}

.zoom-btn.active {
  background: var(--accent-primary);
  color: white;
}

/* PDF Page Container Styles */
.pdf-page-container {
  transition: all 0.3s ease;
  border: 2px solid transparent;
  border-radius: 8px;
  padding: 4px;
}

.pdf-page-container.current-page {
  border-color: var(--accent-primary);
  box-shadow: 0 4px 12px rgba(169, 194, 185, 0.3);
}

.pdf-page {
  border-radius: 4px;
  overflow: hidden;
}

.tab-content {
  transition: opacity 0.3s ease;
}

/* Chapter Navigation Styles */
.chapter-current {
  background: rgba(169, 194, 185, 0.15);
  border-color: var(--accent-primary);
}

.sidebar-item {
  transition: all 0.3s ease;
  border-radius: 0.75rem;
}

.sidebar-item:hover {
  background-color: var(--bg-secondary);
  transform: translateX(4px);
}

/* Transcript Styles */
.transcript-item {
  transition: all 0.3s ease;
}

.transcript-item:hover {
  background: var(--bg-primary);
  border-radius: 0.5rem;
  padding: 0.75rem;
  margin-left: -0.75rem;
  padding-left: 1.5rem;
}
