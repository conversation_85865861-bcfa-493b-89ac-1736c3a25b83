AI-Assisted Agentic Learning Platform - Backend Technical SpecificationTable of ContentsOverviewArchitectureSchema UpdatesContent Creator WorkflowCourse Consumer ExperienceAI Agent SystemImplementation DetailsSecurity & PerformanceOverviewThis platform revolutionizes online learning by leveraging AI agents throughout the content creation and consumption lifecycle. The system uses Convex for backend infrastructure, Mux for video processing, and the Vercel AI SDK for intelligent features.This specification outlines a Mux-first, direct-upload architecture to ensure maximum efficiency, scalability, and speed for video processing.Key TechnologiesConvex: Backend-as-a-Service with real-time capabilitiesMux: Video uploading, hosting, encoding, transcription, and streamingVercel AI SDK: AI model integrationNext.js 15: Frontend frameworkTypeScript: Type-safe developmentCore FeaturesAutomated, webhook-driven content processing and enrichmentAI-powered tutoring and personalizationAdaptive learning recommendationsReal-time progress trackingInteractive learning tools (quizzes, flashcards, notes)ArchitectureThe architecture is designed around an event-driven model where the client uploads videos directly to Mux. The Convex backend acts as an orchestrator, first by authorizing uploads and then by reacting to webhook events from Mux to drive the processing pipeline.Component Architecture┌─────────────────────────────────────────────────────────────┐
│ Frontend (Next.js 15) │
│ (Uploads video files directly to Mux) │
├─────────────────────────────────────────────────────────────┤
│ Convex Backend │
│ (Authorizes uploads, orchestrates via webhooks) │
│ ┌─────────────┐ ┌──────────────┐ ┌──────────────────┐ │
│ │ Workflows │ │ Work Pools │ │ AI Agents │ │
│ └─────────────┘ └──────────────┘ └──────────────────┘ │
│ ┌─────────────┐ ┌──────────────┐ ┌──────────────────┐ │
│ │ Aggregations│ │ Real-time │ │ Embeddings │ │
│ └─────────────┘ └──────────────┘ └──────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ External Services (Mux is primary driver) │
│ ┌─────────────┐ ┌──────────────┐ ┌──────────────────┐ │
│ │ Mux │ │ OpenAI/ │ │ Stripe │ │
│ │ (Webhooks) │ │ Anthropic │ │ │ │
│ └─────────────┘ └──────────────┘ └──────────────────┘ │
└─────────────────────────────────────────────────────────────┘
Schema UpdatesThe database schema is updated to support the Mux-centric workflow, tracking Mux-specific IDs instead of internal storage IDs for videos.Proposed Schema Additions// convex/schema.ts - Additions and changes to existing schema

// Track workflow execution for courses
courseWorkflows: defineTable({
courseId: v.id("courses"),
workflowId: v.string(), // Workflow component ID
status: v.union(
v.literal("running"),
v.literal("completed"),
v.literal("failed"),
v.literal("cancelled")
),
startedAt: v.number(),
completedAt: v.optional(v.number()),
error: v.optional(v.string()),
}).index("by_course", ["courseId"]),

// UPDATED: Track individual file uploads, now Mux-aware
courseFiles: defineTable({
courseId: v.id("courses"),
fileName: v.string(),
fileType: v.string(), // MIME type
fileSize: v.number(),
status: v.union(
v.literal("pending_upload"), // Awaiting client upload to Mux
v.literal("processing"), // Mux is processing, or non-video file processing
v.literal("processed"), // Complete
v.literal("failed")
),
// Mux-specific IDs for videos
muxUploadId: v.optional(v.string()), // ID for the direct upload session
muxAssetId: v.optional(v.string()), // The resulting Mux asset ID
// Kept for non-video files (e.g., PDFs)
processingJobId: v.optional(v.string()),
}).index("by_course", ["courseId"])
.index("by_mux_asset", ["muxAssetId"]), // NEW: To find files from webhooks

// Store AI-generated learning paths
learningPaths: defineTable({
userId: v.id("users"),
courseId: v.id("courses"),
currentLectureId: v.id("lectures"),
recommendedNextLectureId: v.optional(v.id("lectures")),
learningStyle: v.string(), // e.g., "visual", "auditory"
optimalTimeOfDay: v.optional(v.string()), // e.g., "afternoon"
generatedAt: v.number(),
}).index("by_user_course", ["userId", "courseId"]),

// Track learning analytics
learningAnalytics: defineTable({
userId: v.id("users"),
weekStartDate: v.number(),
hoursStudied: v.number(),
lecturesCompleted: v.number(),
quizzesTaken: v.number(),
averageQuizScore: v.number(),
retentionRate: v.number(), // Based on quiz performance over time
}).index("by_user_week", ["userId", "weekStartDate"]),

// Mux webhook events
muxWebhookEvents: defineTable({
muxEventId: v.string(),
type: v.string(),
assetId: v.optional(v.string()),
data: v.string(), // JSON stringified event data
processedAt: v.optional(v.number()),
status: v.union(
v.literal("pending"),
v.literal("processed"),
v.literal("failed")
),
}).index("by_mux_event", ["muxEventId"]),
Content Creator WorkflowThe content creation flow is a multi-step, asynchronous process involving the client, the backend, and Mux.1. Course & Upload URL Creation (Backend + Client)The flow begins when the content creator initiates course creation from the client. The backend's first job is to generate secure, one-time upload URLs from Mux.// convex/courses.ts
import { mutation } from "./\_generated/server";
import { v } from "convex/values";
import { Mux } from "@mux/mux-node";

const mux = new Mux({
tokenId: process.env.MUX_TOKEN_ID!,
tokenSecret: process.env.MUX_TOKEN_SECRET!,
});

export const createCourseAndPrepareUploads = mutation({
args: {
price: v.number(),
files: v.array(
v.object({
fileName: v.string(),
fileType: v.string(),
fileSize: v.number(),
})
),
},
handler: async (ctx, { price, files }) => {
// 1. Create the course record
const courseId = await ctx.db.insert("courses", {
creatorId: await getUserId(ctx),
price,
status: "processing_files", // New initial status
publishedAt: Date.now(),
});

    const uploadUrls = [];

    // 2. Generate direct upload URLs for videos and record file metadata
    for (const file of files) {
      if (file.fileType.startsWith("video/")) {
        // Create a Mux direct upload URL
        const upload = await mux.video.uploads.create({
          cors_origin: "*", // TODO: Restrict to your frontend domain in production
          new_asset_settings: {
            playback_policy: ["public"],
            mp4_support: "standard",
            master_access: "temporary",
            // Request subtitles to be generated automatically
            generate_subtitles: {
              language_code: "en",
              name: "English",
            }
          },
        });

        // Store Mux IDs to map webhooks back to our course
        await ctx.db.insert("courseFiles", {
          courseId,
          ...file,
          status: "pending_upload",
          muxUploadId: upload.id,
          muxAssetId: upload.asset_id,
        });

        uploadUrls.push({ fileName: file.fileName, url: upload.url });
      } else {
        // Handle non-video files (e.g., PDFs) using the workpool pattern
        // This part of the logic can be added if PDF/Audio support is needed.
      }
    }

    // The backend now waits for the client to upload and Mux to send webhooks.
    // The main workflow is NOT started here.
    return { courseId, uploadUrls };

},
});
The client receives the uploadUrls and performs a PUT request for each file, sending the binary data directly to Mux.2. Mux Webhook Handling & Lecture Creation (Backend)The handleMuxWebhook action is the primary driver of the video processing pipeline. It listens for events from Mux and triggers the appropriate backend logic.// convex/webhooks.ts
import { internalAction, internalMutation } from "./\_generated/server";
import { v } from "convex/values";
import { internal } from "./\_generated/api";

// Mux webhook handler
export const handleMuxWebhook = internalAction({
args: {
eventId: v.string(),
type: v.string(),
data: v.any(),
},
handler: async (ctx, { eventId, type, data }) => {
// Store every webhook event for auditing
await ctx.runMutation(internal.webhooks.storeMuxEvent, { eventId, type, data: JSON.stringify(data) });

    const assetId = data.id || data.asset_id;

    switch (type) {
      case "video.asset.ready":
        // This event signals that Mux has finished encoding the video.
        const courseFile = await ctx.runQuery(
          internal.courses.getCourseFileByMuxAssetId,
          { muxAssetId: assetId }
        );

        if (courseFile) {
          // Create the lecture record in our database
          await ctx.runMutation(internal.lectures.createLecture, {
            courseId: courseFile.courseId,
            type: "video",
            status: "processing_transcript", // Waiting for subtitles
            muxAssetId: assetId,
            muxPlaybackId: data.playback_ids?.[0]?.id,
            durationSeconds: data.duration,
          });

          await ctx.runMutation(internal.courses.updateFileStatus, {
            fileId: courseFile._id,
            status: "processing", // Still waiting on transcript
          });
        }
        break;

      case "video.asset.track.ready":
        // This event signals a track (like subtitles) is ready.
        if (data.track.type === "subtitles") {
          await ctx.runAction(internal.videoProcessing.processTranscript, {
            assetId: data.asset_id,
            trackId: data.track.id,
            playbackId: data.playback_ids[0].id,
          });
        }
        break;
    }

},
}); 3. Transcript Processing & AI Enrichment (Backend)Once the transcript is ready, this action fetches it, triggers all AI processing for the lecture, and marks the file as complete.// convex/videoProcessing.ts
import { internalAction } from "./\_generated/server";
import { v } from "convex/values";
import { internal } from "./\_generated/api";

export const processTranscript = internalAction({
args: {
assetId: v.string(),
trackId: v.string(),
playbackId: v.string(),
},
handler: async (ctx, { assetId, trackId, playbackId }) => {
// Fetch VTT transcript from Mux
const transcriptUrl = `https://stream.mux.com/${playbackId}/text/${trackId}.vtt`;
const response = await fetch(transcriptUrl);
const vttContent = await response.text();

    // Parse VTT to plain text
    const transcript = parseVTT(vttContent); // Assume parseVTT utility exists

    // Find the lecture associated with this Mux asset
    const lecture = await ctx.runQuery(
      internal.lectures.getLectureByMuxAssetId, { muxAssetId: assetId }
    );

    if (lecture) {
      // Update lecture with the transcript
      await ctx.runMutation(internal.lectures.updateLectureTranscript, {
        lectureId: lecture._id,
        transcript,
      });

      // **CRITICAL STEP**: Trigger AI processing for this lecture's content
      await ctx.runAction(internal.ai.processLectureContent, {
        lectureId: lecture._id,
        transcript,
      });

      // Mark the lecture and the original file as fully processed
      await ctx.runMutation(internal.lectures.updateLectureStatus, {
        muxAssetId: assetId,
        status: "completed",
      });
      const courseFile = await ctx.runQuery(internal.courses.getCourseFileByMuxAssetId, { muxAssetId: assetId });
      if (courseFile) {
        await ctx.runMutation(internal.courses.updateFileStatus, {
            fileId: courseFile._id,
            status: "processed",
        });
      }

      // **CRITICAL STEP**: Check if the entire course is now ready
      await ctx.runAction(internal.courses.triggerCourseProcessingIfReady, {
        courseId: lecture.courseId,
      });
    }

},
}); 4. Main Course Workflow Trigger & ExecutionThe final processCourse workflow is no longer started at creation. Instead, it's triggered by the triggerCourseProcessingIfReady action, which is called every time a file for the course is successfully processed.// convex/courses.ts

export const triggerCourseProcessingIfReady = internalAction({
args: { courseId: v.id("courses") },
handler: async (ctx, { courseId }) => {
// Prevent race conditions: check if a workflow is already running
const existingWorkflow = await ctx.runQuery(
internal.workflows.findRunningWorkflow, { courseId }
);
if (existingWorkflow) return;

    // Check if all files for the course are now processed
    const allFilesProcessed = await ctx.runQuery(
      internal.courses.areAllFilesProcessed, { courseId }
    );

    if (allFilesProcessed) {
      console.log(`All files processed for course ${courseId}, starting final workflow.`);
      const workflowId = await workflow.start(
        ctx,
        internal.workflows.processCourse,
        { courseId }
      );
      // Track the workflow
      await ctx.runMutation(internal.workflows.trackCourseWorkflow, {
        courseId,
        workflowId,
      });
    }

},
});

// convex/workflows.ts
// This workflow is now much simpler, acting as a final aggregation step.

export const processCourse = workflow.define({
args: { courseId: v.id("courses") },
handler: async (step, { courseId }) => {
// The previous steps (file processing) are already done.

    // Step 1: Generate course metadata from lecture content
    await step.runAction(internal.ai.generateCourseMetadata, { courseId });

    // Step 2: Generate embeddings for RAG
    await step.runAction(internal.ai.generateCourseEmbeddings, { courseId });

    // Step 3: Update course status to "published"
    await step.runMutation(internal.courses.updateCourseStatus, {
      courseId,
      status: "published",
    });

    // Step 4: Notify creator
    await step.runAction(internal.notifications.notifyCreator, {
      courseId,
      message: "Your course is ready!",
    });

},
}); 5. AI Content ProcessingThis section details the AI-powered functions that analyze and enrich the content after the transcript is available.// convex/ai.ts
import { internalAction, internalMutation } from "./\_generated/server";
import { openai } from "@ai-sdk/openai";
import { generateText, generateObject } from "ai";
import { z } from "zod";
import { v } from "convex/values";

export const processLectureContent = internalAction({
args: {
lectureId: v.id("lectures"),
transcript: v.string(),
},
handler: async (ctx, { lectureId, transcript }) => {
// Generate summary
const summaryResult = await generateText({
model: openai("gpt-4o"),
system: "You are an expert educational content analyzer.",
prompt: `Summarize this lecture transcript in 2-3 concise paragraphs:\n\n${transcript}`,
});

    // Generate insights
    const insightsResult = await generateObject({
      model: openai("gpt-4o"),
      schema: z.object({
        insights: z.array(z.string()).max(5),
        keyTakeaways: z.array(z.string()).max(5),
      }),
      prompt: `Extract key insights and takeaways from this lecture:\n\n${transcript}`,
    });

    // Generate chapters
    const chaptersResult = await generateObject({
      model: openai("gpt-4o"),
      schema: z.object({
        chapters: z.array(
          z.object({
            title: z.string(),
            startTime: z.number(), // in seconds
            description: z.string(),
          })
        ),
      }),
      prompt: `Create chapter markers for this video transcript. Include timestamps:\n\n${transcript}`,
    });

    // Generate title and description
    const metadataResult = await generateObject({
      model: openai("gpt-4o"),
      schema: z.object({
        title: z.string(),
        description: z.string().max(200),
      }),
      prompt: `Generate a compelling title and brief description for this lecture:\n\n${transcript.substring(0, 1000)}`,
    });

    // Update lecture with AI-generated content
    await ctx.runMutation(internal.lectures.updateLectureAIContent, {
      lectureId,
      generatedTitle: metadataResult.object.title,
      summary: summaryResult.text,
      insights: insightsResult.object.insights,
    });

    // Create chapters
    for (const chapter of chaptersResult.object.chapters) {
      await ctx.runMutation(internal.chapters.createChapter, {
        lectureId,
        ...chapter,
      });
    }

},
});

export const generateCourseMetadata = internalAction({
args: { courseId: v.id("courses") },
handler: async (ctx, { courseId }) => {
// Get all lectures for the course
const lectures = await ctx.runQuery(internal.lectures.getLecturesByCourse, { courseId });

    // Aggregate summaries
    const aggregatedContent = lectures
      .map((l) => l.summary || "")
      .filter(Boolean)
      .join("\n\n");

    // Generate course-level metadata
    const result = await generateObject({
      model: openai("gpt-4o"),
      schema: z.object({
        title: z.string(),
        description: z.string(),
        summary: z.string(),
        keyConcepts: z.array(z.string()).max(10),
      }),
      prompt: `Based on these lecture summaries, generate comprehensive course metadata:\n\n${aggregatedContent}`,
    });

    // Update course
    await ctx.runMutation(internal.courses.updateCourseMetadata, {
      courseId,
      generatedTitle: result.object.title,
      description: result.object.description,
      summary: result.object.summary,
      keyConcepts: result.object.keyConcepts,
      lectureCount: lectures.length,
      totalDurationSeconds: lectures.reduce( (sum, l) => sum + (l.durationSeconds || 0), 0 ),
    });

},
});

export const generateCourseEmbeddings = internalAction({
args: { courseId: v.id("courses") },
handler: async (ctx, { courseId }) => {
const lectures = await ctx.runQuery(internal.lectures.getLecturesByCourse, { courseId });

    for (const lecture of lectures) {
      if (!lecture.transcript) continue;

      // Split transcript into chunks
      const chunks = splitIntoChunks(lecture.transcript, 500); // 500 tokens per chunk

      for (const chunk of chunks) {
        // Generate embedding
        const { embedding } = await openai
          .embedding("text-embedding-3-small")
          .doEmbed({ values: [chunk] });

        // Store embedding
        await ctx.runMutation(internal.embeddings.storeEmbedding, {
          courseId,
          lectureId: lecture._id,
          textChunk: chunk,
          embedding: embedding[0],
        });
      }
    }

},
});
Course Consumer ExperienceThis section details the queries and mutations that power the student-facing side of the platform, including progress tracking, adaptive recommendations, and the lecture viewing experience.1. Course Page & Progress Tracking// convex/userCourses.ts
import { query, mutation } from "./\_generated/server";
import { v } from "convex/values";

export const getCourseProgress = query({
args: { courseId: v.id("courses") },
handler: async (ctx, { courseId }) => {
const userId = await getUserId(ctx);

    // Get user's course enrollment and lecture progress
    const enrollment = await ctx.db.query("userCourses").withIndex("by_user_and_course", (q) => q.eq("userId", userId).eq("courseId", courseId)).first();
    const lectureProgress = await ctx.db.query("userLectureProgress").withIndex("by_user_lecture", (q) => q.eq("userId", userId)).collect();

    // Get weekly learning analytics
    const currentWeek = getWeekStart(new Date());
    const analytics = await ctx.db.query("learningAnalytics").withIndex("by_user_week", (q) => q.eq("userId", userId).eq("weekStartDate", currentWeek)).first();

    // Get adaptive recommendations
    const learningPath = await ctx.db.query("learningPaths").withIndex("by_user_course", (q) => q.eq("userId", userId).eq("courseId", courseId)).first();

    return { enrollment, lectureProgress, analytics, learningPath };

},
}); 2. Video Lecture Page// convex/lectures.ts
import { query, mutation } from "./\_generated/server";
import { v } from "convex/values";

export const getLectureData = query({
args: { lectureId: v.id("lectures") },
handler: async (ctx, { lectureId }) => {
const userId = await getUserId(ctx);
const lecture = await ctx.db.get(lectureId);
if (!lecture) throw new Error("Lecture not found");

    const chapters = await ctx.db.query("chapters").withIndex("by_lecture", (q) => q.eq("lectureId", lectureId)).collect();
    const progress = await ctx.db.query("userLectureProgress").withIndex("by_user_lecture", (q) => q.eq("userId", userId).eq("lectureId", lectureId)).first();

    return {
      lecture,
      chapters: chapters.sort((a, b) => a.startTime - b.startTime),
      progress,
      muxPlaybackUrl: lecture.muxPlaybackId
        ? `https://stream.mux.com/${lecture.muxPlaybackId}.m3u8`
        : null,
    };

},
});

export const updateProgress = mutation({
args: {
lectureId: v.id("lectures"),
progressSeconds: v.number(),
completed: v.boolean(),
},
handler: async (ctx, { lectureId, progressSeconds, completed }) => {
const userId = await getUserId(ctx);
const lecture = await ctx.db.get(lectureId);
if (!lecture) throw new Error("Lecture not found");

    // Upsert progress
    const existing = await ctx.db.query("userLectureProgress").withIndex("by_user_lecture", (q) => q.eq("userId", userId).eq("lectureId", lectureId)).first();

    if (existing) {
      await ctx.db.patch(existing._id, { progressSeconds, status: completed ? "completed" : "in_progress" });
    } else {
      await ctx.db.insert("userLectureProgress", {
        userId, lectureId, courseId: lecture.courseId, progressSeconds, status: completed ? "completed" : "in_progress",
      });
    }

    // Additional logic to update overall course progress and track activity

},
}); 3. Learning Tools GenerationThis section details how interactive learning tools like quizzes and flashcards are generated on-demand for students.// convex/learningTools.ts
import { internalAction, mutation } from "./\_generated/server";
import { v } from "convex/values";
import { generateObject } from "ai";
import { openai } from "@ai-sdk/openai";
import { z } from "zod";

export const generateQuiz = internalAction({
args: {
lectureId: v.string(),
numQuestions: v.number(),
difficulty: v.string(),
},
handler: async (ctx, { lectureId, numQuestions, difficulty }) => {
const lecture = await ctx.runQuery(internal.lectures.getLectureById, { lectureId });
if (!lecture?.transcript) {
throw new Error("No transcript available for quiz generation");
}

    const result = await generateObject({
      model: openai("gpt-4o"),
      schema: z.object({
        title: z.string(),
        questions: z.array(z.object({
          text: z.string(),
          type: z.literal("multiple_choice"),
          options: z.array(z.object({ text: z.string(), isCorrect: z.boolean() })).length(4),
          explanation: z.string(),
        })).length(numQuestions),
      }),
      prompt: `Create a ${difficulty} difficulty quiz with ${numQuestions} questions based on this lecture: ${lecture.transcript}`,
    });

    const quizId = await ctx.runMutation(internal.quizzes.createQuiz, {
      courseId: lecture.courseId, lectureId: lecture._id, title: result.object.title, type: "ai_generated",
    });

    for (const question of result.object.questions) {
      await ctx.runMutation(internal.questions.createQuestion, { quizId, ...question });
    }
    return quizId;

},
});

export const generateFlashcards = internalAction({
args: {
lectureId: v.string(),
numCards: v.number(),
},
handler: async (ctx, { lectureId, numCards }) => {
const lecture = await ctx.runQuery(internal.lectures.getLectureById, { lectureId });
if (!lecture?.transcript) {
throw new Error("No content available for flashcard generation");
}

    const result = await generateObject({
      model: openai("gpt-4o"),
      schema: z.object({
        deckTitle: z.string(),
        cards: z.array(z.object({
          question: z.string(),
          answer: z.string(),
        })).length(numCards),
      }),
      prompt: `Create ${numCards} flashcards (question/answer pairs) from this lecture transcript: ${lecture.transcript}`,
    });

    const deckId = await ctx.runMutation(internal.flashcards.createDeck, {
      courseId: lecture.courseId, lectureId: lecture._id, title: result.object.deckTitle,
    });

    for (const card of result.object.cards) {
      await ctx.runMutation(internal.flashcards.createCard, { deckId, ...card, source: "ai_generated" });
    }
    return deckId;

},
});
AI Agent SystemThe AI Tutor Agent provides interactive, conversational support to students. It uses a set of tools to access course content, generate learning materials, and provide explanations.// convex/aiTutor.ts
import { Agent, createTool } from "@convex-dev/agent";
import { openai } from "@ai-sdk/openai";
import { z } from "zod";
import { components } from "./\_generated/api";
import { internal } from "./\_generated/api";
import { v } from "convex/values";

// Define the AI Tutor Agent
export const tutorAgent = new Agent(components.agent, {
chat: openai.chat("gpt-4o"),
textEmbedding: openai.embedding("text-embedding-3-small"),
instructions: `You are an expert AI tutor helping students learn effectively.
    You have access to the course content, student progress, and can create
    personalized learning materials. Always be encouraging and adaptive to
    the student's learning style.`,
tools: {
searchCourseContent: createTool({
description: "Search course content using RAG to answer a student's question.",
args: z.object({
query: z.string(),
courseId: z.string(),
}),
handler: async (ctx, { query, courseId }): Promise<string> => {
const results = await ctx.runQuery(internal.rag.searchCourseContent, { query, courseId });
return JSON.stringify(results);
},
}),
createQuiz: createTool({
description: "Generate a quiz to test the student's knowledge on a lecture.",
args: z.object({
lectureId: z.string(),
numQuestions: z.number().min(1).max(10).default(5),
difficulty: z.enum(["easy", "medium", "hard"]).default("medium"),
}),
handler: async (ctx, args): Promise<string> => {
const quizId = await ctx.runAction(internal.learningTools.generateQuiz, args);
return `I've created a quiz for you! You can take it here: /quiz/${quizId}`;
},
}),
createFlashcards: createTool({
description: "Generate flashcards from lecture content for studying.",
args: z.object({
lectureId: z.string(),
numCards: z.number().min(1).max(20).default(10),
}),
handler: async (ctx, args): Promise<string> => {
const deckId = await ctx.runAction(internal.learningTools.generateFlashcards, args);
return `I've created a new flashcard deck for you! You can study it here: /flashcards/${deckId}`;
},
}),
explainConcept: createTool({
description: "Provide a detailed explanation of a concept, using the provided lecture as context.",
args: z.object({
concept: z.string(),
lectureContext: z.string().optional(),
}),
handler: async (ctx, { concept, lectureContext }): Promise<string> => {
const { text } = await generateText({
model: openai('gpt-4o'),
prompt: `Explain the concept "${concept}" in detail. Here is the context from the current lecture: ${lectureContext}`
});
return text;
},
}),
},
});

// Action to handle tutor conversations
export const chatWithTutor = internalAction({
args: {
courseId: v.id("courses"),
lectureId: v.optional(v.id("lectures")),
message: v.string(),
threadId: v.optional(v.string()),
},
handler: async (ctx, { courseId, lectureId, message, threadId }) => {
const userId = await getUserId(ctx);

    // Continue or create a new agent thread
    const { thread, threadId: newThreadId } = threadId
      ? await tutorAgent.continueThread(ctx, { threadId })
      : await tutorAgent.createThread(ctx, { userId, metadata: { courseId, lectureId } });

    // Generate response
    const result = await thread.generateText({ prompt: message });

    // Save conversation to the database
    await ctx.runMutation(internal.aiTutor.saveConversation, {
      userId, courseId, lectureId, threadId: newThreadId, message, response: result.text,
    });

    return { response: result.text, threadId: newThreadId };

},
});
Implementation DetailsHelper functions for querying the database will be needed to support the new workflow.// convex/courses.ts (example helpers)
import { internalQuery } from "./\_generated/server";

export const getCourseFileByMuxAssetId = internalQuery({
args: { muxAssetId: v.string() },
handler: async (ctx, { muxAssetId }) => {
return await ctx.db
.query("courseFiles")
.withIndex("by_mux_asset", (q) => q.eq("muxAssetId", muxAssetId))
.first();
},
});

export const areAllFilesProcessed = internalQuery({
args: { courseId: v.id("courses") },
handler: async (ctx, { courseId }) => {
const files = await ctx.db
.query("courseFiles")
.withIndex("by_course", (q) => q.eq("courseId", courseId))
.collect();

    if (files.length === 0) return false; // No files associated yet
    return files.every((file) => file.status === "processed" || file.status === "failed");

},
});

// convex/workflows.ts (example helper)
export const findRunningWorkflow = internalQuery({
args: { courseId: v.id("courses") },
handler: async (ctx, { courseId }) => {
return await ctx.db.query("courseWorkflows")
.withIndex("by_course", q => q.eq("courseId", courseId))
.filter(q => q.eq(q.field("status"), "running"))
.first();
}
});
Security & PerformanceSecurity: Mux direct upload URLs are secure, short-lived, and tied to a specific upload session, preventing unauthorized uploads. The webhook endpoint must be secured by verifying Mux's webhook signature to ensure it only accepts legitimate requests.Performance: This architecture significantly improves performance and user experience. The client's upload bandwidth is used for Mux, not our server. Processing begins instantly on the Mux side, and our backend is free to handle other requests. The event-driven flow ensures no unnecessary polling or waiting.
