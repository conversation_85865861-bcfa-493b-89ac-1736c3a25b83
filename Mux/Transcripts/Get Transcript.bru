meta {
  name: Get Transcript
  type: http
  seq: 1
}

get {
  url: https://stream.mux.com/:playbackId/text/:trackId\.txt
  body: none
  auth: basic
}

params:path {
  trackId: HEbwdxOs2x4pFqNiZsm02rank9fESPvJF029r01qdsMzRJebdi8rY8qqQ
  playbackId: PDnH6XF006U02fywrAsAA4mjGBLSa9sQrBPQmR1mp9F6w
}

auth:basic {
  username: 828b098c-0014-48ed-83de-96a98e4c5a09
  password: bWkUnQbz3db2Df0VlCciLFWySsExxD4qRfI0JMt+K9v0b9ipkxcQUUzwRDdcuRUi5MpvrDNxks9
}
