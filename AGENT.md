# StudyMind AI - Development Guidelines

## Project Overview

StudyMind AI is an adaptive learning platform built with Next.js 15, shadcn/ui, and Tailwind CSS. This document establishes development standards for maintaining design consistency and code quality throughout the project.

## Quick Commands

### Development

```bash
pnpm dev              # Start development server
pnpm build            # Build for production
pnpm start            # Start production server
pnpm lint             # Run ESLint
pnpm type-check       # Run TypeScript checks
```

### Testing

```bash
pnpm test             # Run all tests
pnpm test:watch       # Run tests in watch mode
pnpm test:ui          # Run component tests with UI
```

## Design System: "Warm Minimalism"

### Core Philosophy

- **Warm, approachable aesthetics** over stark minimalism
- **Gentle interactions** with subtle feedback
- **Consistent visual hierarchy** using typography and spacing
- **Accessibility-first** approach with proper contrast and focus states

### Color System (CSS Variables)

```css
:root {
  --bg-primary: #fdfbf6; /* Warm cream background */
  --bg-secondary: #f0e5d8; /* Secondary warm beige */
  --text-primary: #2d3748; /* Primary text - dark gray */
  --text-secondary: #4a5568; /* Secondary text */
  --text-muted: #718096; /* Muted text */
  --text-subtle: #a0aec0; /* Subtle text */
  --accent-primary: #a9c2b9; /* Sage green - primary accent */
  --accent-secondary: #d3b8ae; /* Warm pink - secondary accent */
  --accent-tertiary: #e6cba5; /* Cream - tertiary accent */
  --highlight: #8b9d8f; /* Darker sage for highlights */
  --success: #68d391; /* Success green */
  --warning: #f6ad55; /* Warning orange */
  --info: #63b3ed; /* Info blue */
}
```

### Typography Scale

- **Primary Font**: Geist (clean, modern sans-serif)
- **Monospace Font**: Geist Mono (for code, data, metrics)
- **Scale**: 12px, 14px, 16px, 18px, 20px, 24px, 32px, 48px
- **Line Heights**: 1.2 (tight), 1.4 (normal), 1.6 (relaxed)

### Spacing Scale (Tailwind)

- **Micro**: 1, 2, 3, 4 (4px, 8px, 12px, 16px)
- **Small**: 5, 6, 8 (20px, 24px, 32px)
- **Medium**: 10, 12, 16 (40px, 48px, 64px)
- **Large**: 20, 24, 32 (80px, 96px, 128px)

### Border Radius Standards

- **Small**: 0.5rem (8px) - buttons, inputs
- **Medium**: 0.75rem (12px) - cards, smaller components
- **Large**: 1rem (16px) - main cards
- **XL**: 1.5rem (24px) - hero sections, large containers

## Component Architecture

### Directory Structure

```
app/                          # Next.js 15 app directory
├── (auth)/                   # Auth route group
├── dashboard/                # Dashboard pages
├── courses/                  # Course pages
├── quiz/                     # Quiz pages
├── flashcards/               # Flashcard pages
├── globals.css               # Global styles
└── layout.tsx                # Root layout

components/
├── ui/                       # shadcn/ui primitives
│   ├── button.tsx
│   ├── card.tsx
│   ├── input.tsx
│   └── ...
├── layout/                   # Layout components
│   ├── navigation.tsx
│   ├── sidebar.tsx
│   └── header.tsx
├── dashboard/                # Dashboard-specific
├── courses/                  # Course-specific
├── quiz/                     # Quiz-specific (redesigned)
├── flashcards/               # Flashcard-specific
└── shared/                   # Cross-feature components
    ├── ai-assistant.tsx
    ├── progress-bar.tsx
    └── stats-card.tsx

lib/
├── utils.ts                  # Utility functions
├── validations.ts            # Zod schemas
├── api.ts                    # API client
└── constants.ts              # App constants

hooks/
├── use-quiz.ts               # Quiz state management
├── use-progress.ts           # Progress tracking
└── use-ai-assistant.ts       # AI interaction
```

### Component Naming Conventions

- **PascalCase** for component files: `AiAssistant.tsx`
- **kebab-case** for UI primitives: `progress-bar.tsx`
- **Descriptive names** that indicate purpose: `QuizQuestionCard`, `CourseProgressWidget`

### Component Composition Patterns

- **Compound components** for complex UI: `<Card><Card.Header><Card.Content></Card>`
- **Render props** for flexible content: `<DataFetcher render={({data}) => <UI />} />`
- **Custom hooks** for stateful logic: `useQuizSession()`

## Styling Guidelines

### Tailwind CSS Conventions

```tsx
// ✅ Good: Logical grouping and consistent spacing
<div className="flex items-center justify-between p-6 bg-white rounded-xl shadow-lg border border-gray-100">

// ❌ Avoid: Random order and inconsistent spacing
<div className="shadow-lg border justify-between rounded-xl flex bg-white border-gray-100 items-center p-6">
```

### CSS Variable Usage

```tsx
// ✅ Preferred: Use CSS variables for brand colors
<div className="bg-[var(--bg-primary)] text-[var(--text-primary)]">

// ✅ Alternative: Use Tailwind for utilities
<div className="p-4 rounded-lg hover:scale-105 transition-transform">
```

### Animation Standards

```css
/* Gentle, purposeful animations */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 12px 20px -5px rgba(45, 55, 72, 0.15);
}

/* Smooth state transitions */
.fade-in {
  animation: fadeInUp 0.6s forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
```

## State Management

### React State Patterns

- **useState** for local component state
- **useReducer** for complex state logic
- **Context** for theme, user preferences
- **Custom hooks** for reusable stateful logic

### Data Fetching

- **Server Components** for initial data loading
- **Client Components** for interactive features
- **React Query/SWR** for client-side data management
- **Optimistic updates** for better UX

## Accessibility Guidelines

### Focus Management

```tsx
// ✅ Proper focus indicators
<button className="focus:outline-none focus:ring-4 focus:ring-[var(--accent-primary)]/30 focus:border-[var(--accent-primary)]">
```

### Semantic HTML

- Use semantic elements: `<nav>`, `<main>`, `<section>`, `<article>`
- Proper heading hierarchy: h1 → h2 → h3
- ARIA labels for dynamic content
- Alt text for images

### Keyboard Navigation

- Tab order follows logical flow
- Escape key closes modals/dropdowns
- Arrow keys for navigation where appropriate
- Enter/Space for activation

## Component Standards

### Button Variants

```tsx
// Primary action button
<Button variant="primary" size="lg">Continue Learning</Button>

// Secondary action
<Button variant="secondary" size="md">View Details</Button>

// Destructive action
<Button variant="destructive" size="sm">Delete</Button>
```

### Card Components

```tsx
// Standard card layout
<Card className="hover-lift">
  <Card.Header>
    <Card.Title>Course Title</Card.Title>
    <Card.Badge>Active</Card.Badge>
  </Card.Header>
  <Card.Content>
    <ProgressBar value={67} />
  </Card.Content>
  <Card.Footer>
    <Button>Continue</Button>
  </Card.Footer>
</Card>
```

### Icon Usage

- **Lucide React** for all icons
- **Consistent sizing**: 16px (w-4 h-4), 20px (w-5 h-5), 24px (w-6 h-6)
- **Semantic meaning**: Use icons that enhance understanding
- **Color consistency**: Follow accent color system

## Performance Guidelines

### Code Splitting

- **Route-based splitting** with Next.js dynamic imports
- **Component-level splitting** for heavy components
- **Lazy loading** for below-the-fold content

### Optimization

- **Image optimization** with Next.js Image component
- **Font optimization** with Next.js font loading
- **Bundle analysis** with webpack-bundle-analyzer

## Testing Strategy

### Component Testing

```tsx
import { render, screen } from "@testing-library/react";
import { QuizQuestionCard } from "./quiz-question-card";

test("displays question text correctly", () => {
  render(<QuizQuestionCard question="What is economics?" />);
  expect(screen.getByText("What is economics?")).toBeInTheDocument();
});
```

### Integration Testing

- **User interactions** with React Testing Library
- **API integration** with MSW (Mock Service Worker)
- **Accessibility testing** with jest-axe

### E2E Testing

- **Critical user journeys** with Playwright
- **Cross-browser testing** for compatibility
- **Performance testing** with Lighthouse CI

## Migration Strategy

### HTML to Next.js Conversion

1. **Extract reusable components** from existing HTML
2. **Convert inline styles** to Tailwind classes
3. **Implement proper TypeScript** interfaces
4. **Add proper error boundaries** and loading states
5. **Maintain existing functionality** while improving UX

### Quiz Page Redesign Priority

- **Color system alignment** with warm minimalism
- **Consistent spacing and typography**
- **Gentle animations** instead of harsh feedback
- **Proper component architecture**
- **Accessibility improvements**

## Code Quality

### TypeScript Standards

```tsx
// ✅ Proper typing for props
interface QuizQuestionProps {
  question: string;
  options: string[];
  onAnswer: (answer: number) => void;
  difficulty: "easy" | "medium" | "hard";
}

// ✅ Proper typing for state
type QuizState = {
  currentQuestion: number;
  answers: Record<number, number>;
  timeRemaining: number;
  status: "idle" | "active" | "completed";
};
```

### Error Handling

- **Error boundaries** for component crashes
- **Loading states** for async operations
- **Empty states** for no data scenarios
- **Proper error messages** for user feedback

## Git Workflow

### Branch Naming

- `feature/quiz-redesign` - New features
- `fix/navigation-mobile` - Bug fixes
- `refactor/card-components` - Code improvements
- `docs/component-guidelines` - Documentation

### Commit Messages

```
feat(quiz): redesign question cards with warm color system

- Replace dark theme with warm minimalism aesthetic
- Update colors to use CSS variable system
- Add gentle hover animations
- Improve accessibility with proper focus states

Closes #123
```

## Deployment

### Environment Variables

```bash
NEXT_PUBLIC_APP_URL=https://studymind.ai
DATABASE_URL=postgresql://...
OPENAI_API_KEY=sk-...
```

### Build Process

1. **Type checking** - `pnpm type-check`
2. **Linting** - `pnpm lint`
3. **Testing** - `pnpm test`
4. **Building** - `pnpm build`
5. **Deployment** - Automatic with Vercel

---

## Getting Started

1. **Read this guide** thoroughly
2. **Set up development environment** with Node.js 22+
3. **Install dependencies** with `pnpm install`
4. **Start development server** with `pnpm dev`
5. **Follow component patterns** established here
6. **Test thoroughly** before committing
7. **Maintain design consistency** across all features

Remember: **Consistency is key**. When in doubt, refer to existing implementations in dashboard, courses, or flashcards components for guidance.
