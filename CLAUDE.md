# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm start` - Start production server
- `pnpm lint` - Run linting

**Package Manager:** This project uses PNPM as the package manager. ALWAYS use `pnpm` commands instead of `npm` for installing packages and running scripts.

**Note:** No test suite is currently configured in this project.

## Code Generation

Do not generate any code until you have:

- carefully considered the existing context of the project
- proposed a plan for the code being written
- received clarification and approval to proceed

Writing code is never an immediate response, but rather a final course after due consideration and planning.

Separation of concerns is essential. Do not, for example, put network code into UI components.

## Architecture Overview

StudyMind is an AI-powered learning platform that transforms the way students learn and engage with educational content. This is a Next.js 15 application with React 19, using the App Router pattern. The backend is built with Convex, a backend-as-a-service platform that provides a serverless environment.

### Core Features

- **Multi-tenant Institute System**: Support for educational institutions with domain-based access
- **User Management**: Students, instructors, admins, and superadmins with role-based permissions
- **Course & Resource Management**: Support for PDFs, videos, audio, markdown, and external links
- **AI-Powered Learning**: Chat with course materials, auto-generated quizzes, and flashcards
- **Progress Tracking**: Detailed analytics on learning progress and resource completion
- **Real-time Chat**: AI assistant for each resource using extracted content

### Key Architecture Patterns

**UI Architecture:**

- Radix UI components as foundation with custom styling
- Shadcn/ui design system
- Tailwind CSS with CSS variables for theming (dark mode support via class strategy)
- Component composition pattern with clear separation between domain-specific components and UI primitives

### Key Technologies

- **Framework:** Next.js 15 with App Router
- **Backend:** Convex (backend-as-a-service)
- **Authentication:** @convex-dev/auth with multi-provider support
- **AI Integration:** Google Gemini via @ai-sdk/google and @google/genai
- **UI:** Radix UI + Tailwind CSS + shadcn/ui design system
- **Forms:** react-hook-form + zod validation
- **File Handling:** react-pdf for PDF rendering, file upload system
- **Icons:** Lucide React
- **Typography:** Geist sans and Geist mono fonts
- **Email:** Resend for transactional emails
- **Charts:** Recharts for data visualization
- **Deployment:** Vercel

### Build Configuration

- TypeScript and ESLint errors are ignored during builds (`next.config.mjs`)
- Images are unoptimized for static export compatibility
- Canvas alias disabled for PDF.js compatibility
- Development indicators disabled
- Project auto-deploys to Vercel on repository updates

### Development Workflow

- **Backend:** Convex provides serverless backend functions and real-time data sync
- **Authentication:** Multi-provider auth with email/password and OAuth support
- **File Storage:** Convex file storage for course materials and user uploads
- **AI Processing:** Automatic content extraction from PDFs and documents for AI chat
- **Deployment:** Vercel automatically deploys from repository updates

## Convex Backend Guidelines

This project uses Convex as the backend-as-a-service platform. Follow these patterns when working with Convex:

### Function Syntax

- **Always use the new function syntax** for Convex functions:
  ```typescript
  import { query } from "./_generated/server";
  import { v } from "convex/values";
  export const f = query({
    args: {},
    returns: v.null(),
    handler: async (ctx, args) => {
      // Function body
    },
  });
  ```

### Function Registration

- Use `query`, `mutation`, `action` for public functions (part of the API)
- Use `internalQuery`, `internalMutation`, `internalAction` for private functions
- **ALWAYS include argument and return validators** for all functions
- If a function doesn't return anything, use `returns: v.null()`

### Database Queries

- **Do NOT use `filter` in queries** - define indexes in schema and use `withIndex`
- Use `.unique()` to get a single document (throws error if multiple matches)
- For async iteration, use `for await (const row of query)` instead of `.collect()`
- Default order is ascending `_creationTime`; use `.order('desc')` for descending

### Schema Guidelines

- Define schema in `convex/schema.ts`
- Include all index fields in index names (e.g., "by_field1_and_field2")
- Index fields must be queried in the same order they're defined
- System fields `_creationTime` and `_id` are automatically added

### TypeScript Types

- Use `Id<'tableName'>` for document IDs instead of `string`
- Use `Doc<'tableName'>` for document types
- Always use `as const` for string literals in discriminated unions
- Be strict with types, especially around document IDs

### Function Calling

- Use `ctx.runQuery` to call queries from queries, mutations, or actions
- Use `ctx.runMutation` to call mutations from mutations or actions
- Use `ctx.runAction` to call actions from actions
- All calls take `FunctionReference` - use `api.file.function` or `internal.file.function`

### Database Schema Overview

The Convex schema includes the following main tables:

- **users**: User profiles with roles (student, instructor, admin, superadmin)
- **institutes**: Educational institutions with domain-based access
- **courses**: Course definitions with AI feature toggles
- **resources**: Course materials (PDFs, videos, audio, markdown, links)
- **enrollments**: User course enrollments with progress tracking
- **resourceAccess**: Detailed resource completion and progress data
- **quizzes**: AI-generated quizzes for resources
- **chatSessions**: AI chat history per resource
- **invitations**: Institute invitation system

### Authentication (Component implementation)

```typescript
const { signIn } = useAuthActions();

const handleSignin = () => {
  signIn("password", {
    email: formData.email,
    password: formData.password,
    flow: "signIn",
  })
    .then(() => {
      // Redirect on success
      router.push("/");
    })
    .catch((error) => {
      if (error instanceof ConvexError) {
        // Handle Convex-specific errors (invalid credentials)
        setErrors({ general: "Invalid email or password. Please try again." });
      } else {
        // Handle general errors
        setErrors({ general: "Failed to sign in. Please try again." });
      }
    });
};
```

### Accessing params from a nextjs page

On the Server (e.g. a route handler, or a Server Component), you must await the dynamic API to access its properties:

`app/[id]/page.js`:

```typescript
async function Page({ params }) {
  // asynchronous access of `params.id`.
  const { id } = await params;
  return <p>ID: {id}</p>;
}
```

In a synchronous component (e.g. a Client component), you must use `React.use()` to unwrap the Promise first:

`app/[id]/page.js`:

```typescript
"use client";
import * as React from "react";

function Page({ params }) {
  // asynchronous access of `params.id`.
  const { id } = React.use(params);
  return <p>ID: {id}</p>;
}
```

## Navigation Patterns

**Next.js 15 Navigation Best Practices:**

- **Prefer `Link` over `useRouter`** wherever possible because `Link` provides automatic prefetching
- **Link Component Prefetching (Next.js 15):**
  - Default: `prefetch={null}` - Prefetches static routes fully; dynamic routes down to nearest loading.js boundary
  - `prefetch={true}` - Prefetches full route for both static and dynamic routes
  - `prefetch={false}` - Disables prefetching on viewport entry and hover
  - Prefetching only works in production environments
- **When to use `useRouter`:**
  - Programmatic navigation after form submissions
  - Navigation with complex conditional logic
  - Navigation that requires additional processing before redirect
- **When to use `Link`:**
  - Standard navigation links in UI
  - Menu items and buttons that navigate to other pages
  - Any navigation where prefetching improves UX
  - **ALWAYS use `Link` instead of `button` for navigation** - buttons should not be used to navigate around the app

**HTML-to-NextJS Conversion Patterns:**

When converting HTML pages to NextJS components, maintain these patterns:
- Server/client component separation with proper "use client" directives
- Pathname-aware sidebar content using `usePathname()`
- CSS custom properties for design token consistency
- Mobile-first responsive design with separate navigation patterns
- Component composition maintaining exact visual fidelity to original HTML
- Replace all `window.location.href` with appropriate NextJS navigation (`Link` preferred, `router.push()` when needed)
