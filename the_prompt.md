we're going to work on a tech spec for the backend only that involves Convex, Mux and Vercel AI sdk for an AI assisted Agentic learning platform that's going to revolutionize how online learning can be effective. The app we're building uses Nextjs 15 with Convex and Typescript. We'll be using the mux node sdk for all mux related features. The app uses Agentic workflows in generating course content as well as how a consumer can interact with the content via an AI tutor agent that can call multiple agents as needed.

# First step (VERY IMPORTANT STEP) - Go through the following documentation to understand how Convex (a famous backend as a service provider) provides advanced backend components:

## Convex Workflow Documentation

This document provides a comprehensive overview of Convex workflows, a powerful feature for building reliable and durable, long-running, and observable series of functions.

### Overview

Convex workflows allow you to run a series of functions (steps) in a reliable and durable manner. Each step can have its own retry behavior, and the entire workflow can survive server restarts. Workflows can run for extended periods, even months, and can be canceled if needed. You can reactively observe the status of a workflow and the results from each step.

**Key Features:**

- **Asynchronous Execution:** Run workflows asynchronously and observe their status reactively.
- **Long-Running:** Workflows can span months and are resilient to server restarts.
- **Flexible Step Execution:** Run steps in parallel or sequentially, with the ability to pass output from previous steps to subsequent ones.
- **Retry Policies:** Define retry behavior on a per-step basis or set a default policy for all steps.
- **Concurrency Control:** Manage the number of workflows that can run in parallel to control system load.
- **Cancellable:** Long-running workflows can be canceled.
- **Cleanup:** Manually clean up workflows after they are completed.

### Installation

To use Convex workflows, you first need to install the `@convex-dev/workflow` package:

```bash
npm install @convex-dev/workflow
```

Next, integrate the component into your `convex/convex.config.ts` file:

```typescript
// convex/convex.config.ts
import workflow from "@convex-dev/workflow/convex.config";
import { defineApp } from "convex/server";

const app = defineApp();
app.use(workflow);

export default app;
```

Finally, create a workflow manager in your `convex/` folder:

```typescript
// convex/index.ts
import { WorkflowManager } from "@convex-dev/workflow";
import { components } from "./_generated/api";

export const workflow = new WorkflowManager(components.workflow);
```

### Usage

#### Defining a Workflow

You can define a workflow using `workflow.define()`. This function is similar to a Convex action, but with a few restrictions: the workflow runs in the background and must be deterministic, so most of its logic should be implemented by calling other Convex functions.

```typescript
export const exampleWorkflow = workflow.define({
  args: { name: v.string() },
  handler: async (step, args): Promise<string> => {
    const queryResult = await step.runQuery(
      internal.example.exampleQuery,
      args
    );
    const actionResult = await step.runAction(
      internal.example.exampleAction,
      { queryResult } // pass in results from previous steps!
    );
    return actionResult;
  },
});
```

#### Starting a Workflow

Workflows can be started from a mutation or action using `workflow.start()`.

```typescript
export const kickoffWorkflow = mutation({
  handler: async (ctx) => {
    const workflowId = await workflow.start(
      ctx,
      internal.example.exampleWorkflow,
      { name: "James" }
    );
  },
});
```

#### Handling a Workflow's Result

You can handle a workflow's result using the `onComplete` option. This is useful for cleaning up resources used by the workflow.

```typescript
export const foo = mutation({
  handler: async (ctx) => {
    const name = "James";
    const workflowId = await workflow.start(
      ctx,
      internal.example.exampleWorkflow,
      { name },
      {
        onComplete: internal.example.handleOnComplete,
        context: name, // can be anything
      }
    );
  },
});
```

#### Running Steps in Parallel

You can run steps in parallel by using `Promise.all()` with multiple `step.runAction()` calls.

```typescript
export const exampleWorkflow = workflow.define({
  args: { name: v.string() },
  handler: async (step, args): Promise<void> => {
    const [result1, result2] = await Promise.all([
      step.runAction(internal.example.myAction, args),
      step.runAction(internal.example.myAction, args),
    ]);
  },
});
```

#### Specifying Retry Behavior

Workflows support automatic retries for failed actions with exponential backoff and jitter. You can define default retry behavior on the `WorkflowManager` or override it on a per-workflow or per-step basis.

```typescript
const workflow = new WorkflowManager(components.workflow, {
  defaultRetryBehavior: {
    maxAttempts: 3,
    initialBackoffMs: 100,
    base: 2,
  },
});
```

#### Checking a Workflow's Status

The `workflow.start()` method returns a `WorkflowId`, which can be used to query the workflow's status.

```typescript
const status = await workflow.status(ctx, workflowId);
```

#### Canceling a Workflow

You can cancel a workflow using `workflow.cancel()`.

```typescript
await workflow.cancel(ctx, workflowId);
```

#### Cleaning Up a Workflow

After a workflow is complete, you can clean up its storage with `workflow.cleanup()`.

```typescript
await workflow.cleanup(ctx, workflowId);
```

### Limitations

- Steps can only take in and return a total of 1 MiB of data within a single workflow execution.
- `console.log()` is not currently captured.
- Backtraces from within function calls from workflows are not collected.
- Side effects like `fetch`, `Math.random()`, or `Date.now()` must be used in a step, not in the workflow definition.
- Changes to the implementation of a workflow (e.g., adding, removing, or reordering steps) will cause a determinism violation.

## AI Agent Component for Convex

This document provides a guide to using the AI Agent component for Convex, a powerful framework for building AI applications.

### Overview

The AI Agent component for Convex provides a framework for building AI-powered chat applications. It offers automatic storage of chat history, RAG (Retrieval-Augmented Generation) for context, tool usage, and seamless integration with other Convex features like workflows.

**Key Features:**

- **Automatic Chat History:** Stores chat history on a per-user or per-thread basis.
- **Playground UI:** A dedicated UI for testing, debugging, and development.
- **RAG for Context:** Utilizes hybrid text and vector search for providing context to the agent.
- **Tool Calls:** Supports tool calls via the AI SDK with Convex-specific wrappers.
- **Workflow Integration:** Easily integrates with the Convex Workflow component for creating durable, long-running agentic processes.
- **Reactive Updates:** Provides real-time updates from asynchronous functions and workflows.
- **Streaming Support:** Supports streaming text responses and storing the final result.

### Installation

First, you need an existing Convex project. You can create one by running `npm create convex`.

Next, install the agent component package:

```bash
npm install @convex-dev/agent
```

Then, add the component to your `convex.config.ts` file:

```typescript
// convex/convex.config.ts
import { defineApp } from "convex/server";
import agent from "@convex-dev/agent/convex.config";

const app = defineApp();
app.use(agent);

export default app;
```

### Usage

#### Creating the Agent

Define an agent similarly to how you would use the AI SDK. This involves specifying models, instructions, and tools.

```typescript
// convex/myFile.ts
import { Agent, createTool } from "@convex-dev/agent";
import { openai } from "@ai-sdk/openai";
import { z } from "zod";
import { components } from "./_generated/api";

const supportAgent = new Agent(components.agent, {
  chat: openai.chat("gpt-4o-mini"),
  textEmbedding: openai.embedding("text-embedding-3-small"),
  instructions: "You are a helpful assistant.",
  tools: {
    myConvexTool: createTool({
      description: "My Convex tool",
      args: z.object({}),
      handler: async (ctx, args): Promise<string> => {
        return "Hello, world!";
      },
    }),
  },
});
```

#### Starting and Continuing a Thread

You can start a new conversation thread from a mutation or action. The `threadId` returned allows you to continue the conversation later.

**Starting a thread:**

```typescript
// convex/myActions.ts
export const createThreadAndPrompt = action({
  args: { prompt: v.string() },
  handler: async (ctx, { prompt }) => {
    const userId = await getUserId(ctx);
    // Start a new thread for the user.
    const { threadId, thread } = await supportAgent.createThread(ctx, {
      userId,
    });
    // Creates a user message and an assistant reply.
    const result = await thread.generateText({ prompt });
    return { threadId, text: result.text };
  },
});
```

**Continuing a thread:**

```typescript
// convex/myActions.ts
export const continueThread = action({
  args: { prompt: v.string(), threadId: v.string() },
  handler: async (ctx, { prompt, threadId }) => {
    // Continue a thread, picking up where you left off.
    const { thread } = await anotherAgent.continueThread(ctx, { threadId });
    // Includes previous message history automatically.
    const result = await thread.generateText({ prompt });
    return result.text;
  },
});
```

#### Generating Text and Objects

You can generate text or structured objects using the thread object.

**Generating Text:**

```typescript
const result = await thread.generateText({ prompt });
```

**Generating an Object:**

```typescript
import { z } from "zod";

const result = await thread.generateObject({
  prompt: "Generate a plan based on the conversation so far",
  schema: z.object({
    /* ... */
  }),
});
```

#### Showing Messages

You can fetch and display messages on the client-side using the `useThreadMessages` hook.

**Server-side Query:**

```typescript
// convex/chat.ts
export const listThreadMessages = query({
  args: {
    threadId: v.string(),
    paginationOpts: paginationOptsValidator,
  },
  handler: async (ctx, { threadId, paginationOpts }) => {
    const paginated = await agent.listMessages(ctx, {
      threadId,
      paginationOpts,
    });
    return paginated;
  },
});
```

**Client-side Component:**

```jsx
// MyComponent.tsx
import { api } from "../convex/_generated/api";
import { useThreadMessages, toUIMessages } from "@convex-dev/agent/react";

function MyComponent({ threadId }: { threadId: string }) {
  const messages = useThreadMessages(
    api.chat.listThreadMessages,
    { threadId },
    { initialNumItems: 10 }
  );
  return (
    <div>
      {toUIMessages(messages.results ?? []).map((message) => (
        <div key={message.key}>{message.content}</div>
      ))}
    </div>
  );
}
```

### Asynchronous Generation

For better UI responsiveness, you can save a user's message in a mutation first, enabling optimistic updates, and then schedule an action to generate the AI's response asynchronously.

```typescript
// convex/messages.ts
export const sendMessage = mutation({
  args: { threadId: v.id("threads"), prompt: v.string() },
  handler: async (ctx, { threadId, prompt }) => {
    const userId = await getUserId(ctx);
    const { messageId } = await agent.saveMessage(ctx, {
      threadId,
      userId,
      prompt,
      skipEmbeddings: true,
    });
    await ctx.scheduler.runAfter(0, internal.example.myAsyncAction, {
      threadId,
      promptMessageId: messageId,
    });
  },
});

export const myAsyncAction = internalAction({
  args: { threadId: v.string(), promptMessageId: v.string() },
  handler: async (ctx, { threadId, promptMessageId }) => {
    await supportAgent.generateAndSaveEmbeddings(ctx, {
      messageIds: [promptMessageId],
    });
    const { thread } = await supportAgent.continueThread(ctx, { threadId });
    await thread.generateText({ promptMessageId });
  },
});
```

### Using with the Workflow Component

The Agent component integrates with the Workflow component to create durable, long-lived agentic processes that can survive server restarts and include retries. You can expose agent capabilities as Convex actions and use them as steps in a workflow.

```typescript
// convex/workflows.ts
const workflow = new WorkflowManager(components.workflow);

export const supportAgentWorkflow = workflow.define({
  args: { prompt: v.string(), userId: v.string() },
  handler: async (step, { prompt, userId }) => {
    const { threadId } = await step.runMutation(internal.example.createThread, {
      userId,
      title: "Support Request",
    });
    const suggestion = await step.runAction(internal.example.getSupport, {
      threadId,
      userId,
      prompt,
    });
    // ... further steps
  },
});
```

## Convex Workpool Component

This document provides a guide to using the Workpool component for Convex, which allows you to pool actions and mutations to control parallelism, manage retries, and build reliable, durable workflows.

### Overview

The Workpool component is designed to manage and throttle asynchronous workloads in your Convex backend. It allows you to create separate pools for different types of tasks, each with its own concurrency limit. This is particularly useful for preventing less important tasks from delaying critical ones and for managing load on third-party APIs.

**Key Use Cases:**

- **Throttling Async Workloads:** Separate tasks like sending important emails from less critical jobs like API scraping into different pools with different concurrency limits.
- **Durable Workflows with Retries:** Manage actions that interact with third-party services by setting a maximum parallelism to prevent overwhelming your backend or hitting rate limits during outages. The component supports automatic retries with configurable exponential backoff and jitter.
- **Completion Handling:** Use the `onComplete` callback to define what happens after a job finishes, whether it succeeds, fails, or is canceled, enabling the creation of complex, reliable workflows.
- **Optimizing OCC Errors:** Reduce "Optimistic Concurrency Control" errors by running mutations that frequently conflict (e.g., incrementing a global counter) in a workpool with a parallelism of 1, effectively serializing them.

### Installation

First, ensure you have an existing Convex project. Then, install the Workpool package:

```bash
npm install @convex-dev/workpool
```

Next, configure the component in your `convex/convex.config.ts` file. You can create and name multiple pools as needed:

```typescript
// convex/convex.config.ts
import { defineApp } from "convex/server";
import workpool from "@convex-dev/workpool/convex.config";

const app = defineApp();

// Define different pools for different tasks
app.use(workpool, { name: "emailWorkpool" });
app.use(workpool, { name: "scrapeWorkpool" });

export default app;
```

### Usage

#### Creating and Using a Workpool

Instantiate a `Workpool` and use it to enqueue actions or mutations.

```typescript
// convex/myFile.ts
import { components } from "./_generated/api";
import { Workpool } from "@convex-dev/workpool";

// Create a pool with a max parallelism of 10
const emailPool = new Workpool(components.emailWorkpool, {
  maxParallelism: 10,
});

export const userSignUp = mutation({
  args: {
    /*...*/
  },
  handler: async (ctx, args) => {
    const userId = await ctx.db.insert("users", args);
    // Enqueue an action to be run by the pool
    await emailPool.enqueueAction(ctx, internal.auth.sendEmailVerification, {
      userId,
    });
  },
});
```

#### API Interface

A `Workpool` instance provides the following methods:

- `pool.enqueueMutation(ctx, internal.foo.bar, args)`: Schedules a mutation to run in the background.
- `pool.enqueueAction(ctx, internal.foo.baz, args)`: Schedules an action to run in the background.
- `pool.status(id)`: Checks the status of a job (pending, running, or finished).
- `pool.cancel(ctx, id)`: Cancels a specific job if it hasn't finished yet.

#### Configuration Options

When creating a `Workpool`, you can specify several options:

- `maxParallelism`: The maximum number of functions that can run concurrently in this pool.
- `retryActionsByDefault`: A boolean to enable or disable retries for failed actions by default.
- `defaultRetryBehavior`: An object to configure the default retry logic (`maxAttempts`, `initialBackoffMs`, `base`).

When enqueuing work, you can also provide options like:

- `retry`: Overrides the default retry behavior for a specific job.
- `onComplete`: A mutation to run after the job finishes.
- `context`: Data to be passed to the `onComplete` mutation.
- `runAt` / `runAfter`: Schedules the job to run at a specific time or after a delay.

#### Retry Behavior

Failed actions can be automatically retried. The delay between retries follows an exponential backoff strategy (`initialBackoffMs * base^(retryNumber - 1)`) with added jitter to prevent a "thundering herd" of retries.

#### Idempotency

For complex workflows involving retries and third-party APIs, it is crucial that your actions are **idempotent**. This means an action can be executed multiple times without causing incorrect side effects. For example, when charging a credit card, use a unique transaction ID so the payment provider can recognize and ignore duplicate requests for the same transaction.

### Monitoring

The component provides logging that can be integrated with services like Axiom for monitoring. You can create dashboards to track:

- **Backlog Length:** The number of tasks that are past their scheduled start time.
- **Failure Rate:** The average rate of jobs that fail permanently after all retries.
- **Retry Rate:** The ratio of failures per function, indicating how often retries are happening.
- **Start Lag:** The average time between a job being enqueued and when it actually starts running.

## Convex Aggregate Component

This document provides a guide to using the Aggregate component for Convex, which allows you to efficiently compute and query aggregations over your data, such as counts, sums, and averages.

### Overview

The Aggregate component simplifies the process of creating and maintaining aggregated data views in your Convex application. It's particularly useful for building dashboards, analytics, and any feature that requires summarizing large datasets. The component listens for changes in your source data table and automatically updates the aggregated results in a separate table, ensuring your analytics are always up-to-date.

**Key Features:**

- **Efficient Aggregation:** Avoids costly full-table scans by incrementally updating aggregates as your data changes.
- **Automatic Updates:** Listens to a source table and keeps the aggregate data current.
- **Time-Bucket Grouping:** Supports grouping data by time intervals (e.g., day, hour, month).
- **Custom Filtering:** Allows you to specify which documents to include in the aggregation.
- **Multiple Aggregators:** Supports various aggregation types like `count`, `sum`, `average`, `min`, and `max`.

### Installation

First, ensure you have an existing Convex project. Then, install the Aggregate package:

```bash
npm install @convex-dev/aggregate
```

Next, add the component to your `convex.config.ts` file:

```typescript
// convex/convex.config.ts
import { defineApp } from "convex/server";
import aggregate from "@convex-dev/aggregate/convex.config";

const app = defineApp();

app.use(aggregate);

export default app;
```

### Usage

#### Defining an Aggregation

To use the component, you define an aggregation configuration. This involves specifying the source table, the aggregators you want to compute, and how you want to group the data.

```typescript
// convex/myAggregations.ts
import { defineAggregation } from "@convex-dev/aggregate";
import { v } from "convex/values";

export const { count, sum, average, monthly, daily } = defineAggregation(
  "sales", // The name of the aggregate table
  {
    // Define the source table and how to group documents
    source: "sales",
    by: {
      month: "month",
      day: "day",
    },
    // Specify which aggregations to compute
    aggregators: {
      count: {
        // Count all documents
        kind: "count",
      },
      totalValue: {
        // Sum the "value" field
        kind: "sum",
        field: "value",
      },
      averageValue: {
        // Average the "value" field
        kind: "average",
        field: "value",
      },
    },
    // Optional: Add a filter to include only specific documents
    filter: (doc) => doc.type === "online",
  }
);
```

#### Explanation of `defineAggregation`

- **`"sales"`**: The first argument is the name for the new table where aggregated results will be stored (e.g., `_a_sales_by_day` and `_a_sales_by_month`).
- **`source: "sales"`**: Specifies the source table to read data from.
- **`by`**: Defines how to group the data. In the example, it groups by `month` and `day`, which are derived from the `_creationTime` of each document. You can also group by a specific field in your documents.
- **`aggregators`**: An object defining the aggregations to compute. Each key (e.g., `count`, `totalValue`) becomes a field in the aggregate table.
- **`filter`**: An optional function to select which documents from the source table should be included in the aggregation.

#### Querying Aggregated Data

The `defineAggregation` function returns helper functions that make it easy to query the aggregated data.

- **`count()`**: Returns the total count of all documents included in the aggregation.
- **`sum(aggregatorName)`**: Returns the total sum for a given "sum" aggregator.
- **`average(aggregatorName)`**: Returns the overall average for a given "average" aggregator.
- **`monthly()` / `daily()`**: Returns a `QueryBuilder` for querying data grouped by month or day, respectively.

**Example Queries:**

```typescript
// convex/getSalesData.ts
import { query } from "./_generated/server";
import { count, sum, monthly } from "./myAggregations";

// Get the total count of sales
export const totalSalesCount = query(async (ctx) => {
  return await count(ctx);
});

// Get the total sum of the "totalValue" aggregator
export const totalSalesValue = query(async (ctx) => {
  return await sum(ctx, "totalValue");
});

// Get the monthly sales data for the last 6 months
export const last6Months = query(async (ctx) => {
  return await monthly(ctx).order("desc").take(6);
});
```

#### Backfilling Data

After defining an aggregation, you need to run a one-time backfill operation to process existing documents in your source table. You can do this by running an action from the Convex dashboard.

```typescript
// convex/myAggregations.ts
import { backfill } from "@convex-dev/aggregate/server";
import { internalAction } from "./_generated/server";

export const backfillSales = internalAction({
  handler: (ctx) => backfill(ctx, "sales"),
});
```

### How It Works

The Aggregate component sets up internal Convex functions (`_a_add`, `_a_remove`) that are triggered whenever a document in the source table is created, modified, or deleted. These functions incrementally update the values in the aggregate tables, ensuring the aggregations are always consistent with the source data without requiring expensive full-table scans on each query.

# Second step - Consider the following flow for the content creator:

1. A content creator creates a new course by clicking a button that shows a file drop zone and a price field
2. Content creator drops single or multiple Video, Audio, PDF files as needed. enters price
3. Backend triggers a workflow based on file types. (pdfWorkflow, audioWorkflow, videoWorkflow). We'll focus on `videoWorkflow` for this tech spec
4. We'll be using Mux.com to provide video hosting, encoding and playing capabilities
5. Each video will get uploaded directly to Mux, we subscribe to webhook `mux-webhook` and when the track is ready `video.asset.track.ready` we:

- grab the transcript via `https://stream.mux.com/:playbackId/text/:trackId.vtt
- start summarization
- generate key takeaways
- generate key insights
- generate video chapters
- generate title of the video
- generate brief description of the video
- keep updating the course's title and description as more course videos get processed

# Third step - now consider the following scenario from the course consumer:

1. Buys the course. Goes to course page. Sees various data like:

- List of all the lectures in the course
- Current progress
- Active course (total time, type, progress)
- Sees adaptive recommendations if any based on learning journey
- Sees adaptive learning patterns such as `You learn best with video content afternoons. Perfect time for next lecture`
- Can generate AI based quizzes, flashcards, smart notes (AI-enhanced) and track progress
- Sees learning Analytics like hours/week, retention rate, weekly performance

2. Clicks on the video lecture
3. Sees the vide page with Video Player using Mux video player
4. Transcript and Chatpers
5. an AI tutor chatbot:

- Shows the context connection depending on previous lecture like what this course is all about in a very short paragraph. see this example: `This builds on supply & demand concepts from Week 2. Notice how market structure affects pricing power.`
- allows question answering. can reference to an example in the video based on transcript
- allows creating on demand Quiz and Flashcards
- allows creating notes that consumer and AI both can collaborate on (Future feature)
- shows key insights like this: `Integration by parts is the reverse of the product rule. When you see a product that doesn't fit basic patterns, think LIATE!`
- shows the connection to any other lecture like `Connects To: Product Rule for Derivatives`
- provides quick notes as the video progress like `23:18: Integration by parts formula: ∫u dv = uv - ∫v du` so that user can add to notes

for reference i'm sharing the current convex schema. make sure to follow it and suggest any changes as needed to the schema. The tech spec should be thorough and provide sample code snippets as needed. Don't skip any step and ask if you need anything. Don't make up stuff if you don't know or doesn't have the context of anything. Don't assume anything.
