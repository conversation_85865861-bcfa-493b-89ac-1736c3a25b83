import React from "react";
import { Bar<PERSON>hart3 } from "lucide-react";
import { AIAssistantCard } from "../dashboard/ai-assistant-card";
import { StudyToolsCard } from "../dashboard/study-tools-card";

interface CourseDetailSidebarContentProps {
  onToolClick: (toolId: string) => void;
}

export function CourseDetailSidebarContent({ onToolClick }: CourseDetailSidebarContentProps) {
  // Course-specific AI insights
  const aiInsights = [
    {
      id: "1",
      icon: "sparkles" as const,
      title: "Smart Recommendation",
      description:
        "You're ready for Market Structures! Strong performance on Supply & Demand shows mastery.",
    },
    {
      id: "2",
      icon: "chart" as const,
      title: "Learning Pattern",
      description:
        "You learn best with video content afternoons. Perfect timing for next lecture!",
    },
  ];

  const studyTools = [
    {
      id: "ai-quiz",
      icon: "quiz" as const,
      title: "AI Quiz",
      description: "Test knowledge",
    },
    {
      id: "flashcards",
      icon: "flashcards" as const,
      title: "Flashcards",
      description: "Quick review",
    },
    {
      id: "smart-notes",
      icon: "notes" as const,
      title: "Smart Notes",
      description: "AI-enhanced",
    },
    {
      id: "progress",
      icon: "progress" as const,
      title: "Progress",
      description: "Track learning",
    },
  ];

  return (
    <>
      <AIAssistantCard insights={aiInsights} />
      
      <StudyToolsCard tools={studyTools} onToolClick={onToolClick} />
      
      {/* Course Analytics */}
      <div className="bg-white p-7 rounded-2xl shadow-xl stagger-entrance minimal-artwork-subtle border border-gray-100">
        <h3 className="text-xl font-semibold text-[var(--text-primary)] mb-5 flex items-center">
          <BarChart3 className="w-6 h-6 mr-3 icon-primary" />
          Learning Analytics
        </h3>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="text-center bg-[var(--bg-primary)] p-4 rounded-lg">
            <div className="geist-mono text-2xl font-bold text-[var(--accent-primary)]">
              8.7
            </div>
            <div className="text-xs text-[var(--text-muted)] mt-1">
              Study Hours/Week
            </div>
          </div>
          <div className="text-center bg-[var(--bg-primary)] p-4 rounded-lg">
            <div className="geist-mono text-2xl font-bold text-[var(--accent-primary)]">
              94%
            </div>
            <div className="text-xs text-[var(--text-muted)] mt-1">
              Retention Rate
            </div>
          </div>
        </div>
        <div className="bg-[var(--bg-primary)] p-4 rounded-lg text-center">
          <div className="text-xs text-[var(--text-muted)] mb-2">
            Weekly Performance Trend
          </div>
          <div className="h-12 bg-[var(--accent-primary)] rounded flex items-center justify-center text-white text-xs font-semibold">
            📈 Trending upward!
          </div>
        </div>
      </div>
    </>
  );
}