import React from "react";
import { AIAssistantCard } from "../dashboard/ai-assistant-card";
import { QuickActionsCard } from "../dashboard/quick-actions-card";
import { StudyToolsCard } from "../dashboard/study-tools-card";
import { StreakCard } from "../dashboard/streak-card";

interface DashboardSidebarContentProps {
  onActionClick: (actionId: string) => void;
  onToolClick: (toolId: string) => void;
}

export function DashboardSidebarContent({ 
  onActionClick, 
  onToolClick 
}: DashboardSidebarContentProps) {
  // Mock data - replace with real data from your API
  const aiInsights = [
    {
      id: "1",
      icon: "sparkles" as const,
      title: "Personalized Recommendation",
      description:
        "You're excelling at supply & demand. Dive deeper into market structures next.",
    },
    {
      id: "2",
      icon: "chart" as const,
      title: "Study Pattern Analysis",
      description:
        "Your best learning time is 2-4 PM. Good time for the next lecture!",
    },
    {
      id: "3",
      icon: "lightbulb" as const,
      title: "Learning Tip",
      description:
        "You learn best with visuals. I've added more diagrams to the next lecture materials.",
    },
  ];

  const quickActions = [
    {
      id: "generate-quiz",
      icon: "quiz" as const,
      title: "Generate quiz from recent lectures",
    },
    {
      id: "review-flashcards",
      icon: "flashcards" as const,
      title: "Review economics flashcards",
      description: "12 due",
    },
    {
      id: "ask-question",
      icon: "ask" as const,
      title: "Ask about your courses",
    },
  ];

  const studyTools = [
    {
      id: "ai-quiz",
      icon: "quiz" as const,
      title: "AI Quiz",
      description: "Test knowledge",
    },
    {
      id: "flashcards",
      icon: "flashcards" as const,
      title: "Flashcards",
      description: "Quick review",
    },
    {
      id: "smart-notes",
      icon: "notes" as const,
      title: "Smart Notes",
      description: "AI-enhanced",
    },
    {
      id: "progress",
      icon: "progress" as const,
      title: "Progress",
      description: "Track learning",
    },
  ];

  // Mock streak data - replace with real data
  const streakDays = [
    true,
    true,
    true,
    true,
    true,
    false,
    false, // Week 1
    true,
    true,
    true,
    true,
    true,
    true,
    false, // Week 2 (today)
  ];

  return (
    <>
      <AIAssistantCard insights={aiInsights} />
      <QuickActionsCard
        actions={quickActions}
        onActionClick={onActionClick}
      />
      <StudyToolsCard tools={studyTools} onToolClick={onToolClick} />
      <StreakCard currentStreak={12} streakDays={streakDays} />
    </>
  );
}