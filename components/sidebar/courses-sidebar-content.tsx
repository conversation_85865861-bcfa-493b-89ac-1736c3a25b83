import React from "react";
import { Filter } from "lucide-react";
import { AIAssistantCard } from "../dashboard/ai-assistant-card";
import { StudyToolsCard } from "../dashboard/study-tools-card";

interface CoursesSidebarContentProps {
  onToolClick: (toolId: string) => void;
}

export function CoursesSidebarContent({ onToolClick }: CoursesSidebarContentProps) {
  // Mock data for AI insights - course specific
  const aiInsights = [
    {
      id: "1",
      icon: "sparkles" as const,
      title: "Continue Economics",
      description:
        "You're excelling at supply & demand. Next up: Market Structures.",
    },
    {
      id: "2",
      icon: "chart" as const,
      title: "Study Pattern Analysis",
      description:
        "Your peak learning time is 2-4 PM. Perfect for next lecture!",
    },
  ];

  const studyTools = [
    {
      id: "ai-quiz",
      icon: "quiz" as const,
      title: "AI Quiz",
      description: "Test knowledge",
    },
    {
      id: "flashcards",
      icon: "flashcards" as const,
      title: "Flashcards",
      description: "Quick review",
    },
    {
      id: "smart-notes",
      icon: "notes" as const,
      title: "Smart Notes",
      description: "AI-enhanced",
    },
    {
      id: "progress",
      icon: "progress" as const,
      title: "Progress",
      description: "Track learning",
    },
  ];

  // Mock course counts - in real app, this would come from props or context
  const courseCounts = {
    all: 7,
    active: 5,
    completed: 2,
    paused: 1,
  };

  const filters = [
    { id: "all", label: "All Courses", count: courseCounts.all },
    { id: "active", label: "Active", count: courseCounts.active },
    { id: "completed", label: "Completed", count: courseCounts.completed },
    { id: "paused", label: "Paused", count: courseCounts.paused },
  ];

  const getCountColor = (filterId: string) => {
    switch (filterId) {
      case "all": return "bg-[var(--accent-primary)]";
      case "active": return "bg-[var(--success)]";
      case "completed": return "bg-[var(--info)]";
      case "paused": return "bg-[var(--warning)]";
      default: return "bg-[var(--accent-primary)]";
    }
  };

  return (
    <>
      <AIAssistantCard insights={aiInsights} />
      
      {/* Quick Filters */}
      <div className="bg-white p-7 rounded-2xl shadow-xl stagger-entrance minimal-artwork-subtle border border-gray-100">
        <h3 className="text-xl font-semibold text-[var(--text-primary)] mb-5 flex items-center">
          <Filter className="w-6 h-6 mr-3 icon-primary" />
          Quick Filters
        </h3>
        <div className="space-y-2">
          {filters.map((filter) => (
            <button
              key={filter.id}
              className="w-full flex items-center justify-between text-[var(--text-secondary)] hover:bg-[var(--bg-primary)] p-3 rounded-lg transition-colors text-sm sidebar-card-item focus-ring"
              onClick={() => {
                // For now, just log - in real app, this would update URL params or global state
                console.log("Filter clicked:", filter.id);
              }}
            >
              <span className="font-medium">{filter.label}</span>
              <span
                className={`text-xs ${getCountColor(filter.id)} text-white px-2 py-1 rounded-full geist-mono`}
              >
                {filter.count}
              </span>
            </button>
          ))}
        </div>
      </div>

      <StudyToolsCard tools={studyTools} onToolClick={onToolClick} />
    </>
  );
}