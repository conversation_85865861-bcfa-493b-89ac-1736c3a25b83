import React from "react";
import { Spark<PERSON> } from "lucide-react";
import { StatsCard } from "../shared/stats-card";

interface HeroSectionProps {
  userName: string;
  stats: {
    streak: string;
    avgScore: string;
    activeCourses: string;
    studyTime: string;
  };
}

export function HeroSection({ userName, stats }: HeroSectionProps) {
  return (
    <section className="animated-gradient p-6 lg:p-10 rounded-2xl shadow-xl mb-8 lg:mb-10 relative overflow-hidden">
      <div className="absolute inset-0 minimal-artwork-bg opacity-50"></div>
      <div className="relative z-10 flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
        <div className="flex-1">
          <h2 className="text-2xl lg:text-4xl font-bold text-white mb-3">
            Welcome back, {userName}!
          </h2>
          <p className="text-white/90 text-base lg:text-lg mb-6">
            Let's nurture your curiosity and reach new heights today.
          </p>
        </div>
        <Sparkles className="w-8 h-8 lg:w-12 lg:h-12 text-white opacity-80 self-center sm:self-start" />
      </div>
      <div className="relative z-10 grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mt-6 lg:mt-8 text-center">
        <StatsCard
          value={stats.streak}
          label="Day Streak"
        />
        <StatsCard
          value={stats.avgScore}
          label="Avg Score"
          valueClassName="text-[var(--accent-primary)]"
        />
        <StatsCard
          value={stats.activeCourses}
          label="Active Courses"
        />
        <StatsCard
          value={stats.studyTime}
          label="Study Time"
        />
      </div>
    </section>
  );
}
