import React from 'react';
import { Zap, FileQuestion, Layers3, MessageCircleQuestion } from 'lucide-react';

interface QuickAction {
  id: string;
  icon: 'quiz' | 'flashcards' | 'ask';
  title: string;
  description?: string;
}

interface QuickActionsCardProps {
  actions: QuickAction[];
  onActionClick?: (actionId: string) => void;
}

const getIcon = (iconType: QuickAction['icon']) => {
  switch (iconType) {
    case 'quiz':
      return FileQuestion;
    case 'flashcards':
      return Layers3;
    case 'ask':
      return MessageCircleQuestion;
    default:
      return FileQuestion;
  }
};

export function QuickActionsCard({ actions, onActionClick }: QuickActionsCardProps) {
  return (
    <div className="bg-white p-7 rounded-2xl shadow-xl stagger-entrance minimal-artwork-subtle border border-gray-100">
      <h3 className="text-xl font-semibold text-[var(--text-primary)] mb-5 flex items-center">
        <Zap className="w-6 h-6 mr-3 icon-secondary" />
        Quick Actions
      </h3>
      
      <div className="space-y-3">
        {actions.map((action, index) => {
          const Icon = getIcon(action.icon);
          return (
            <button
              key={action.id}
              onClick={() => onActionClick?.(action.id)}
              className="w-full flex items-center space-x-3 text-[var(--text-secondary)] hover:bg-[var(--bg-primary)] p-3.5 rounded-lg transition-colors text-sm sidebar-card-item justify-start focus-ring"
              style={{ animationDelay: `${(index + 1) * 0.1}s` }}
            >
              <Icon className="w-5 h-5 icon-secondary flex-shrink-0" />
              <span className="text-left font-medium">
                {action.title}
                {action.description && ` (${action.description})`}
              </span>
            </button>
          );
        })}
      </div>
    </div>
  );
}
