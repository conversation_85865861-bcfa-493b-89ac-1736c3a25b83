import React from 'react';
import { Brain<PERSON><PERSON><PERSON>it, Sparkles, BarChart3, Lightbulb } from 'lucide-react';

interface AIInsight {
  id: string;
  icon: 'sparkles' | 'chart' | 'lightbulb';
  title: string;
  description: string;
}

interface AIAssistantCardProps {
  insights: AIInsight[];
}

const getIcon = (iconType: AIInsight['icon']) => {
  switch (iconType) {
    case 'sparkles':
      return Sparkles;
    case 'chart':
      return BarChart3;
    case 'lightbulb':
      return Lightbulb;
    default:
      return Sparkles;
  }
};

export function AIAssistantCard({ insights }: AIAssistantCardProps) {
  return (
    <div className="bg-[var(--accent-primary)] text-white p-7 rounded-2xl shadow-xl stagger-entrance relative overflow-hidden">
      <div
        className="absolute inset-0 minimal-artwork-bg opacity-20"
        style={{ backgroundSize: "100px 100px" }}
      />
      
      <div className="relative z-10 flex items-center space-x-4 mb-4">
        <BrainCircuit className="w-10 h-10 text-white" />
        <div>
          <h3 className="text-xl font-semibold text-white">
            AI Learning Assistant
          </h3>
          <p className="text-sm text-white/80">Your personal guide</p>
        </div>
      </div>
      
      {insights.map((insight, index) => {
        const Icon = getIcon(insight.icon);
        return (
          <div
            key={insight.id}
            className="relative z-10 bg-white/20 hover:bg-white/30 p-4 rounded-lg mb-3 cursor-pointer transition-all duration-300 ease-in-out group sidebar-card-item focus-ring"
            style={{ animationDelay: `${(index + 1) * 0.1}s` }}
            tabIndex={0}
          >
            <div className="flex items-start space-x-3">
              <Icon className="w-5 h-5 text-white/90 mt-1 flex-shrink-0 group-hover:text-white transition-colors" />
              <div>
                <h4 className="text-md font-semibold text-white">
                  {insight.title}
                </h4>
                <p className="text-xs text-white/80 mt-1 leading-relaxed">
                  {insight.description}
                </p>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
