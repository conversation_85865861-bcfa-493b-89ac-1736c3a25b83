import React from 'react';
import { FilePlus2, CheckCircle, Layers } from 'lucide-react';
import { ActivityCard } from '../shared/activity-card';

interface ActivityItem {
  id: string;
  type: 'note' | 'quiz' | 'flashcard';
  title: string;
  subtitle: string;
  timestamp: string;
}

interface ActivityFeedProps {
  activities: ActivityItem[];
}

const getActivityConfig = (type: ActivityItem['type']) => {
  switch (type) {
    case 'note':
      return {
        icon: FilePlus2,
        iconColor: 'text-blue-600',
        iconBgColor: 'bg-blue-100'
      };
    case 'quiz':
      return {
        icon: CheckCircle,
        iconColor: 'text-purple-600',
        iconBgColor: 'bg-purple-100'
      };
    case 'flashcard':
      return {
        icon: Layers,
        iconColor: 'text-orange-600',
        iconBgColor: 'bg-orange-100'
      };
    default:
      return {
        icon: FilePlus2,
        iconColor: 'text-blue-600',
        iconBgColor: 'bg-blue-100'
      };
  }
};

export function ActivityFeed({ activities }: ActivityFeedProps) {
  return (
    <div className="space-y-4">
      {activities.map((activity, index) => {
        const config = getActivityConfig(activity.type);
        return (
          <ActivityCard
            key={activity.id}
            icon={config.icon}
            iconColor={config.iconColor}
            iconBgColor={config.iconBgColor}
            title={activity.title}
            subtitle={activity.subtitle}
            timestamp={activity.timestamp}
          />
        );
      })}
    </div>
  );
}
