import React from "react";
import {
  Settings2,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Edit3,
  TrendingUp,
} from "lucide-react";

interface StudyTool {
  id: string;
  icon: "quiz" | "flashcards" | "notes" | "progress";
  title: string;
  description: string;
}

interface StudyToolsCardProps {
  tools: StudyTool[];
  onToolClick?: (toolId: string) => void;
}

const getIcon = (iconType: StudyTool["icon"]) => {
  switch (iconType) {
    case "quiz":
      return ClipboardCheck;
    case "flashcards":
      return Copy;
    case "notes":
      return Edit3;
    case "progress":
      return TrendingUp;
    default:
      return ClipboardCheck;
  }
};

export function StudyToolsCard({ tools, onToolClick }: StudyToolsCardProps) {
  return (
    <div className="bg-white p-7 rounded-2xl shadow-xl stagger-entrance minimal-artwork-subtle border border-gray-100">
      <h3 className="text-xl font-semibold text-[var(--text-primary)] mb-5 flex items-center">
        <Settings2 className="w-6 h-6 mr-3 icon-primary" />
        Study Tools
      </h3>

      <div className="grid grid-cols-2 gap-3 lg:gap-4 text-center">
        {tools.map((tool, index) => {
          const Icon = getIcon(tool.icon);
          return (
            <div
              key={tool.id}
              onClick={() => onToolClick?.(tool.id)}
              className="bg-[var(--bg-primary)] p-3 lg:p-4 rounded-lg hover:bg-[var(--accent-tertiary)]/50 transition-colors cursor-pointer sidebar-card-item focus-ring"
              style={{ animationDelay: `${(index + 1) * 0.1}s` }}
              tabIndex={0}
            >
              <Icon className="w-6 h-6 lg:w-8 lg:h-8 icon-primary mx-auto mb-1.5" />
              <p className="text-xs lg:text-sm font-semibold text-[var(--text-primary)]">
                {tool.title}
              </p>
              <p className="text-xs text-[var(--text-muted)]">
                {tool.description}
              </p>
            </div>
          );
        })}
      </div>
    </div>
  );
}
