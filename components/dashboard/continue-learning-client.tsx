"use client";

import React, { useState, useEffect } from 'react';
import { ContinueLearningCard } from './continue-learning-card';

interface CurrentCourse {
  courseTitle: string;
  courseCode: string;
  instructor: string;
  lectureCount: number;
  currentLecture: number;
  progress: number;
  nextLecture: {
    title: string;
    number: number;
    duration: string;
  };
}

export function ContinueLearningClient() {
  const [currentCourse, setCurrentCourse] = useState<CurrentCourse | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate progressive loading - replace with actual Convex query
    const loadCourseData = async () => {
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay
      
      const mockCourse: CurrentCourse = {
        courseTitle: "Microeconomics Fundamentals",
        courseCode: "Economics 101",
        instructor: "<PERSON><PERSON>",
        lectureCount: 24,
        currentLecture: 9,
        progress: 67,
        nextLecture: {
          title: "Market Structures",
          number: 10,
          duration: "42 min",
        },
      };
      
      setCurrentCourse(mockCourse);
      setIsLoading(false);
    };

    loadCourseData();
  }, []);

  if (isLoading) {
    return (
      <div className="bg-white p-4 lg:p-7 rounded-2xl shadow-xl border border-gray-100 animate-pulse">
        <div className="flex flex-col sm:flex-row sm:items-start space-y-4 sm:space-y-0 sm:space-x-5">
          <div className="bg-gray-200 p-3 lg:p-4 rounded-xl w-16 h-16 lg:w-20 lg:h-20"></div>
          <div className="flex-1 space-y-3">
            <div className="h-6 bg-gray-200 rounded w-3/4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/3"></div>
            </div>
          </div>
        </div>
        <div className="mt-5 space-y-3">
          <div className="h-2 bg-gray-200 rounded"></div>
          <div className="h-16 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!currentCourse) {
    return (
      <div className="bg-white p-4 lg:p-7 rounded-2xl shadow-xl border border-gray-100 text-center">
        <p className="text-gray-500">No active courses found</p>
      </div>
    );
  }

  return <ContinueLearningCard {...currentCourse} />;
}