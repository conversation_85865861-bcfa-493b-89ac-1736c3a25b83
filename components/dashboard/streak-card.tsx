import React from "react";
import { Flame } from "lucide-react";

interface StreakCardProps {
  currentStreak: number;
  streakDays: boolean[]; // Array of 14 booleans representing the last 2 weeks
}

export function StreakCard({ currentStreak, streakDays }: StreakCardProps) {
  const dayLabels = ["M", "T", "W", "T", "F", "S", "S"];

  return (
    <div className="bg-white p-7 rounded-2xl shadow-xl stagger-entrance minimal-artwork-subtle border border-gray-100">
      <h3 className="text-xl font-semibold text-[var(--text-primary)] mb-5 flex items-center">
        <Flame className="w-6 h-6 mr-3 text-[var(--accent-secondary)]" />
        Study Streak
      </h3>

      <div className="text-center mb-4 lg:mb-5">
        <p className="geist-mono text-3xl lg:text-5xl font-bold text-[var(--accent-primary)] mb-1">
          {currentStreak}
        </p>
        <p className="text-sm lg:text-md text-[var(--text-secondary)] font-medium">
          Days in a row!
        </p>
        <p className="text-xs lg:text-sm text-[var(--text-muted)] mt-1">
          Keep up the amazing work! 🎉
        </p>
      </div>

      <div className="grid grid-cols-7 gap-1 lg:gap-1.5 text-center text-xs geist-mono">
        {streakDays.map((hasStudied, index) => {
          const dayIndex = index % 7;
          const isToday = index === streakDays.length - 1;

          return (
            <span
              key={index}
              className={`p-2 lg:p-2.5 rounded-md ${
                isToday
                  ? "bg-[var(--accent-primary)] text-white font-bold ring-2 ring-[var(--accent-primary)]/50 shadow-lg"
                  : hasStudied
                  ? "bg-[var(--accent-tertiary)] text-[var(--text-primary)] font-semibold"
                  : "bg-[var(--bg-primary)] text-[var(--text-subtle)]"
              }`}
            >
              {dayLabels[dayIndex]}
            </span>
          );
        })}
      </div>
    </div>
  );
}
