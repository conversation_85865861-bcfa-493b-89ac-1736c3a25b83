"use client";

import React, { useState, useEffect } from 'react';
import { ActivityFeed } from './activity-feed';

interface ActivityItem {
  id: string;
  type: 'note' | 'quiz' | 'flashcard';
  title: string;
  subtitle: string;
  timestamp: string;
}

export function ActivityFeedClient() {
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate progressive loading - replace with actual Convex query
    const loadActivities = async () => {
      await new Promise(resolve => setTimeout(resolve, 800)); // Simulate network delay
      
      const mockActivities: ActivityItem[] = [
        {
          id: "1",
          type: "note",
          title: "Added notes on Supply & Demand",
          subtitle: "Economics 101 • Lecture 4",
          timestamp: "2h ago",
        },
        {
          id: "2",
          type: "quiz",
          title: "Completed quiz: Market Equilibrium",
          subtitle: "Score: 85% • 8/10 correct",
          timestamp: "Yesterday",
        },
        {
          id: "3",
          type: "flashcard",
          title: "Reviewed flashcards: Economic Terms",
          subtitle: "15 cards reviewed • Good retention",
          timestamp: "2d ago",
        },
      ];
      
      setActivities(mockActivities);
      setIsLoading(false);
    };

    loadActivities();
  }, []);

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-white p-4 lg:p-6 rounded-2xl shadow-xl border border-gray-100 animate-pulse">
            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
              <div className="flex-1 space-y-2">
                <div className="h-5 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/4"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return <ActivityFeed activities={activities} />;
}