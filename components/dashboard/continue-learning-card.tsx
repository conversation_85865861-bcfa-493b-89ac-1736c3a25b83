import React from "react";
import {
  BookOpenText,
  Layers,
  UserCircle,
  Video,
  ArrowRightCircle,
} from "lucide-react";
import { ProgressBar } from "../shared/progress-bar";

interface ContinueLearningCardProps {
  courseTitle: string;
  courseCode: string;
  instructor: string;
  lectureCount: number;
  currentLecture: number;
  progress: number;
  nextLecture: {
    title: string;
    number: number;
    duration: string;
  };
}

export function ContinueLearningCard({
  courseTitle,
  courseCode,
  instructor,
  lectureCount,
  currentLecture,
  progress,
  nextLecture,
}: ContinueLearningCardProps) {
  return (
    <div className="bg-white p-4 lg:p-7 rounded-2xl shadow-xl hover-lift minimal-artwork-subtle border border-gray-100">
      <div className="flex flex-col sm:flex-row sm:items-start space-y-4 sm:space-y-0 sm:space-x-5">
        <div className="bg-[var(--bg-secondary)] p-3 lg:p-4 rounded-xl self-start">
          <BookOpenText className="w-8 h-8 lg:w-10 lg:h-10 text-[var(--accent-primary)]" />
        </div>
        <div className="flex-1">
          <h4 className="text-lg lg:text-xl font-semibold text-[var(--text-primary)] mb-2">
            {courseTitle}
          </h4>
          <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-5 text-sm text-[var(--text-muted)] mb-4">
            <span className="flex items-center font-medium">
              <Layers className="w-4 h-4 mr-1.5" />
              {courseCode}
            </span>
            <span className="flex items-center font-medium">
              <UserCircle className="w-4 h-4 mr-1.5" />
              {instructor}
            </span>
            <span className="flex items-center font-medium">
              <Video className="w-4 h-4 mr-1.5" />
              {lectureCount} lectures
            </span>
          </div>
        </div>
      </div>

      <div className="mt-5">
        <ProgressBar
          value={progress}
          showLabel={true}
          label={`Progress: Lecture ${currentLecture} of ${lectureCount}`}
        />
      </div>

      <div
        className="mt-6 pt-5 border-t border-[var(--bg-secondary)] flex items-center space-x-4 hover:bg-[var(--bg-primary)] p-4 -m-4 rounded-lg transition-colors cursor-pointer group focus-ring"
        tabIndex={0}
      >
        <ArrowRightCircle className="w-6 h-6 text-[var(--accent-primary)] group-hover:text-[var(--highlight)] transition-colors" />
        <div>
          <p className="font-semibold text-[var(--text-primary)]">
            Next: {nextLecture.title}
          </p>
          <p className="text-sm text-[var(--text-muted)]">
            Lecture {nextLecture.number} • {nextLecture.duration}
          </p>
        </div>
      </div>
    </div>
  );
}
