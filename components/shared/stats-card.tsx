import React from "react";

interface StatsCardProps {
  value: string;
  label: string;
  className?: string;
  valueClassName?: string;
}

export function StatsCard({
  value,
  label,
  className,
  valueClassName = "text-[var(--text-primary)]",
}: StatsCardProps) {
  return (
    <div
      className={`bg-white/90 backdrop-blur-sm p-3 lg:p-5 rounded-xl shadow-lg hover-lift border border-white/20 ${className}`}
    >
      <p
        className={`geist-mono text-2xl lg:text-4xl font-bold ${valueClassName}`}
      >
        {value}
      </p>
      <p className="text-xs lg:text-sm text-[var(--text-muted)] mt-1 font-medium">
        {label}
      </p>
    </div>
  );
}
