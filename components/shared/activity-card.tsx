import React from "react";
import { LucideIcon } from "lucide-react";

interface ActivityCardProps {
  icon: LucideIcon;
  iconColor: string;
  iconBgColor: string;
  title: string;
  subtitle: string;
  timestamp: string;
}

export function ActivityCard({
  icon: Icon,
  iconColor,
  iconBgColor,
  title,
  subtitle,
  timestamp,
}: ActivityCardProps) {
  return (
    <div className="bg-white p-4 lg:p-5 rounded-2xl shadow-lg flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 hover-lift minimal-artwork-subtle border border-gray-100">
      <div className="flex items-center space-x-3 lg:space-x-4">
        <div className={`${iconBgColor} p-2 lg:p-3 rounded-lg flex-shrink-0`}>
          <Icon className={`w-5 h-5 lg:w-6 lg:h-6 ${iconColor}`} />
        </div>
        <div className="min-w-0 flex-1">
          <p className="font-semibold text-[var(--text-primary)] text-sm lg:text-base">
            {title}
          </p>
          <p className="text-xs lg:text-sm text-[var(--text-muted)]">
            {subtitle}
          </p>
        </div>
      </div>
      <p className="text-xs text-[var(--text-subtle)] geist-mono self-start sm:self-auto flex-shrink-0">
        {timestamp}
      </p>
    </div>
  );
}
