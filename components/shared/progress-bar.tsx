import React from 'react';

interface ProgressBarProps {
  value: number;
  max?: number;
  className?: string;
  showLabel?: boolean;
  label?: string;
}

export function ProgressBar({ 
  value, 
  max = 100, 
  className,
  showLabel = false,
  label
}: ProgressBarProps) {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);

  return (
    <div className={className}>
      {showLabel && (
        <div className="flex justify-between items-center text-sm mb-2">
          <span className="text-[var(--text-secondary)] font-medium">
            {label}
          </span>
          <span className="geist-mono font-bold text-[var(--text-primary)]">
            {Math.round(percentage)}% Complete
          </span>
        </div>
      )}
      <div className="w-full bg-[var(--bg-secondary)] rounded-full h-3">
        <div
          className="bg-[var(--accent-primary)] h-3 rounded-full progress-bar-fill"
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  );
}
