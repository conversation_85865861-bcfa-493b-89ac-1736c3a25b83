"use client";

import { Menu } from "lucide-react";
import React from "react";

export function MobileMenuButton() {
  return (
    <button
      className="lg:hidden p-2 hover:bg-[var(--bg-secondary)] rounded-lg transition-colors focus-ring"
      id="mobile-menu-button"
      onClick={() => window.dispatchEvent(new Event("toggleMobileNavigation"))}
    >
      <Menu className="w-6 h-6 text-[var(--text-primary)]" />
    </button>
  );
}
