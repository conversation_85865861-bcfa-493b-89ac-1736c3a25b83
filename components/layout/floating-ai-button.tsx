"use client";

import { useState } from "react";
import { BrainCircuit, X } from "lucide-react";

export function FloatingAIButton() {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleToggle = () => {
    setIsExpanded(!isExpanded);
    // Trigger the sidebar to show
    window.dispatchEvent(new CustomEvent("toggleMobileSidebar"));
  };

  return (
    <div className="fixed bottom-6 right-6 z-40 lg:hidden">
      {/* Main floating button */}
      <button
        onClick={handleToggle}
        className={`w-14 h-14 rounded-full shadow-lg transition-all duration-300 flex items-center justify-center ${
          isExpanded 
            ? "bg-red-500 hover:bg-red-600" 
            : "bg-[var(--accent-primary)] hover:bg-[var(--highlight)]"
        }`}
        aria-label={isExpanded ? "Close AI Tools" : "Open AI Tools"}
      >
        {isExpanded ? (
          <X className="w-6 h-6 text-white" />
        ) : (
          <BrainCircuit className="w-6 h-6 text-white" />
        )}
      </button>

      {/* Tooltip */}
      {!isExpanded && (
        <div className="absolute bottom-16 right-0 bg-black text-white text-xs px-2 py-1 rounded opacity-0 hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none">
          AI Assistant & Tools
        </div>
      )}
    </div>
  );
}