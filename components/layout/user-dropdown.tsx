"use client";

import React, { useState, useRef, useEffect } from "react";
import Image from "next/image";
import {
  User,
  Settings,
  LogOut,
  ChevronDown,
  LoaderCircle,
} from "lucide-react";
import { useAuthActions } from "@convex-dev/auth/react";
import { useRouter } from "next/navigation";

interface UserDropdownProps {
  user: {
    firstName?: string;
    profileImage?: string;
  } | null;
}

export function UserDropdown({ user }: UserDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { signOut } = useAuthActions();
  const router = useRouter();

  const handleLogout = () => {
    signOut();
    router.push("/signin");
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-3 focus-ring rounded-lg p-1 hover:bg-[var(--bg-secondary)]/50 transition-colors"
      >
        <div className="w-10 h-10 bg-[var(--accent-secondary)] rounded-full flex items-center justify-center text-white text-lg font-semibold shadow-sm">
          <Image
            src={user?.profileImage || "/images/profile-avatar.png"}
            alt="Profile Avatar"
            width={32}
            height={32}
            className="w-8 h-8 rounded-full"
          />
        </div>
        <div className="hidden lg:block text-left">
          <p className="text-md font-semibold text-[var(--text-primary)]">
            {user?.firstName || "User"}
          </p>
          <p className="text-xs text-[var(--text-muted)]">Premium Plan</p>
        </div>
        <ChevronDown
          className={`w-4 h-4 text-[var(--text-muted)] transition-transform ${
            isOpen ? "rotate-180" : ""
          }`}
        />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-xl border border-[var(--accent-tertiary)]/20 py-2 z-50">
          <div className="px-4 py-3 border-b border-[var(--bg-secondary)]">
            <p className="text-sm font-semibold text-[var(--text-primary)]">
              {user?.firstName || "User"}
            </p>
            <p className="text-xs text-[var(--text-muted)]">Premium Plan</p>
          </div>

          <div className="py-1">
            <button className="w-full flex items-center px-4 py-3 text-sm text-[var(--text-secondary)] hover:bg-[var(--bg-primary)] transition-colors">
              <User className="w-4 h-4 mr-3" />
              Profile
            </button>
            <button className="w-full flex items-center px-4 py-3 text-sm text-[var(--text-secondary)] hover:bg-[var(--bg-primary)] transition-colors">
              <Settings className="w-4 h-4 mr-3" />
              Settings
            </button>
          </div>

          <div className="border-t border-[var(--bg-secondary)] pt-1">
            <button
              onClick={handleLogout}
              className="w-full flex items-center px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors"
            >
              <LogOut className="w-4 h-4 mr-3" />
              Logout
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

// // Export dynamic component that only renders on client
// export const UserDropdown = dynamic(() => Promise.resolve(UserDropdownClient), {
//   ssr: false,
//   loading: () => (
//     <div className="relative">
//       <div className="flex items-center space-x-3 rounded-lg p-1">
//         <LoaderCircle className="w-4 h-4 mr-3" />
//       </div>
//     </div>
//   ),
// });
