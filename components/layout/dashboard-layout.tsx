import React from 'react';
import Header from '@/components/header';
import Sidebar from '@/components/sidebar';
import { FloatingAIButton } from './floating-ai-button';

interface AppLayoutProps {
  children: React.ReactNode;
}

export function AppLayout({ children }: AppLayoutProps) {
  return (
    <div className="flex h-screen text-[var(--text-primary)] relative">
      {/* Sidebar - mobile slides in, desktop always visible, content based on pathname */}
      <Sidebar />
      
      {/* Main content area */}
      <main className="flex-1 flex flex-col min-w-0">
        <Header />
        {children}
      </main>
      
      {/* Floating AI Button for mobile */}
      <FloatingAIButton />
    </div>
  );
}

// Keep DashboardLayout as alias for backward compatibility
export const DashboardLayout = AppLayout;
