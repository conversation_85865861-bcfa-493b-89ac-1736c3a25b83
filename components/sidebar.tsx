"use client";

import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import { X } from "lucide-react";
import { DashboardSidebarContent } from "./sidebar/dashboard-sidebar-content";
import { CoursesSidebarContent } from "./sidebar/courses-sidebar-content";
import { CourseDetailSidebarContent } from "./sidebar/course-detail-sidebar-content";

export default function Sidebar() {
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const pathname = usePathname();

  const handleQuickAction = (actionId: string) => {
    // Handle quick action clicks
    console.log("Quick action clicked:", actionId);
    // Close mobile sidebar after action
    setIsMobileOpen(false);
  };

  const handleToolClick = (toolId: string) => {
    // Handle study tool clicks
    console.log("Study tool clicked:", toolId);
    // Close mobile sidebar after action
    setIsMobileOpen(false);
  };

  // Determine which sidebar content to show based on pathname
  const getSidebarContent = () => {
    // Course detail page (e.g., /courses/123)
    if (pathname?.match(/^\/courses\/[^\/]+$/)) {
      return <CourseDetailSidebarContent onToolClick={handleToolClick} />;
    }
    
    // Courses listing page
    if (pathname?.startsWith('/courses')) {
      return <CoursesSidebarContent onToolClick={handleToolClick} />;
    }
    
    // Default to dashboard content for dashboard and other pages
    return (
      <DashboardSidebarContent 
        onActionClick={handleQuickAction}
        onToolClick={handleToolClick}
      />
    );
  };

  const getSidebarTitle = () => {
    // Course detail page
    if (pathname?.match(/^\/courses\/[^\/]+$/)) {
      return "Course";
    }
    
    // Courses listing page
    if (pathname?.startsWith('/courses')) {
      return "Courses";
    }
    
    return "Menu";
  };

  // Listen for mobile sidebar toggle events
  useEffect(() => {
    const handleToggle = () => {
      console.log("toggleMobileSidebar event triggered");
      setIsMobileOpen((prev) => !prev);
    };

    window.addEventListener("toggleMobileSidebar", handleToggle);
    return () => {
      window.removeEventListener("toggleMobileSidebar", handleToggle);
    };
  }, []);

  // Close sidebar when clicking outside on mobile
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const sidebar = document.getElementById("mobile-sidebar");
      const menuButton = document.getElementById("mobile-menu-button");

      if (
        isMobileOpen &&
        sidebar &&
        !sidebar.contains(event.target as Node) &&
        menuButton &&
        !menuButton.contains(event.target as Node)
      ) {
        setIsMobileOpen(false);
      }
    };

    if (isMobileOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isMobileOpen]);

  return (
    <>
      {/* Mobile Sidebar Overlay - only shows when floating button is used */}
      {isMobileOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 lg:hidden" />
      )}

      {/* Mobile Sidebar - triggered by floating AI button */}
      <aside
        id="mobile-sidebar"
        className={`fixed top-0 right-0 h-full w-80 bg-[var(--bg-secondary)] transform transition-transform duration-300 ease-in-out z-50 lg:hidden ${
          isMobileOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div className="flex justify-between items-center p-4 border-b border-[var(--accent-tertiary)]/50">
          <h2 className="text-lg font-semibold text-[var(--text-primary)]">
            {getSidebarTitle()}
          </h2>
          <button
            onClick={() => setIsMobileOpen(false)}
            className="p-2 hover:bg-[var(--bg-primary)] rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-[var(--text-primary)]" />
          </button>
        </div>

        <div className="overflow-y-auto h-full pb-20 p-4 space-y-6">
          {getSidebarContent()}
        </div>
      </aside>

      {/* Desktop Sidebar */}
      <aside className="hidden lg:block w-96 bg-[var(--bg-secondary)] p-7 border-l border-[var(--accent-tertiary)]/50 overflow-y-auto space-y-7">
        {getSidebarContent()}
      </aside>
    </>
  );
}
