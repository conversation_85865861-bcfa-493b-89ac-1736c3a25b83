"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";

export function Navigation() {
  const pathname = usePathname();
  
  const getNavLinkClass = (href: string) => {
    // Check if current path matches the href or if it's a sub-path
    const isActive = pathname === href || 
                    (href === '/courses' && pathname?.startsWith('/courses'));
    
    return isActive ? "nav-link-active" : "nav-link";
  };

  return (
    <nav className="hidden lg:flex items-center space-x-8 text-md font-medium">
      <Link href="/dashboard" className={getNavLinkClass('/dashboard')}>
        Dashboard
      </Link>
      <Link href="/courses" className={getNavLinkClass('/courses')}>
        Courses
      </Link>
    </nav>
  );
}