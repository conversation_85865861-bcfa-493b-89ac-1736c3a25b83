"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { <PERSON>, <PERSON>, User, Setting<PERSON>, LogOut } from "lucide-react";
import { useState, useEffect } from "react";

interface MobileNavigationDrawerProps {
  user?: {
    firstName?: string;
    name?: string;
    email?: string;
  } | null;
}

export function MobileNavigationDrawer({ user }: MobileNavigationDrawerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();

  const getNavLinkClass = (href: string) => {
    const isActive = pathname === href || 
                    (href === '/courses' && pathname?.startsWith('/courses'));
    
    return `block px-4 py-3 text-lg font-medium transition-colors ${
      isActive 
        ? "text-[var(--accent-primary)] bg-[var(--accent-primary)]/10 border-r-4 border-[var(--accent-primary)]" 
        : "text-[var(--text-primary)] hover:bg-[var(--bg-secondary)]"
    }`;
  };

  // Listen for mobile navigation toggle events
  useEffect(() => {
    const handleToggle = () => {
      setIsOpen(prev => !prev);
    };

    window.addEventListener("toggleMobileNavigation", handleToggle);
    return () => {
      window.removeEventListener("toggleMobileNavigation", handleToggle);
    };
  }, []);

  // Close drawer when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const drawer = document.getElementById("mobile-navigation-drawer");
      
      if (
        isOpen &&
        drawer &&
        !drawer.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  const handleLinkClick = () => {
    setIsOpen(false);
  };

  const userName = user?.firstName || user?.name || "Student";

  return (
    <>
      {/* Overlay */}
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 lg:hidden" />
      )}

      {/* Navigation Drawer */}
      <div
        id="mobile-navigation-drawer"
        className={`fixed top-0 left-0 h-full w-80 bg-[var(--bg-primary)] transform transition-transform duration-300 ease-in-out z-50 lg:hidden shadow-2xl ${
          isOpen ? "translate-x-0" : "-translate-x-full"
        }`}
      >
        {/* Header */}
        <div className="flex justify-between items-center p-4 border-b border-[var(--accent-tertiary)]/50 bg-[var(--bg-secondary)]">
          <div className="flex items-center space-x-3">
            <Brain className="w-8 h-8 icon-primary" />
            <h2 className="text-xl font-bold text-[var(--text-primary)]">
              StudyMind
            </h2>
          </div>
          <button
            onClick={() => setIsOpen(false)}
            className="p-2 hover:bg-[var(--bg-primary)] rounded-lg transition-colors"
          >
            <X className="w-6 h-6 text-[var(--text-primary)]" />
          </button>
        </div>

        {/* User Info */}
        <div className="p-4 border-b border-[var(--accent-tertiary)]/30">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-[var(--accent-secondary)] rounded-full flex items-center justify-center">
              <User className="w-6 h-6 text-white" />
            </div>
            <div>
              <p className="font-semibold text-[var(--text-primary)]">
                {userName}
              </p>
              <p className="text-sm text-[var(--text-muted)]">
                {user?.email || "Premium Plan"}
              </p>
            </div>
          </div>
        </div>

        {/* Navigation Links */}
        <nav className="py-4">
          <div className="space-y-1">
            <Link 
              href="/dashboard" 
              className={getNavLinkClass('/dashboard')}
              onClick={handleLinkClick}
            >
              Dashboard
            </Link>
            <Link 
              href="/courses" 
              className={getNavLinkClass('/courses')}
              onClick={handleLinkClick}
            >
              My Courses
            </Link>
            <Link 
              href="/progress" 
              className={getNavLinkClass('/progress')}
              onClick={handleLinkClick}
            >
              Progress
            </Link>
            <Link 
              href="/study-tools" 
              className={getNavLinkClass('/study-tools')}
              onClick={handleLinkClick}
            >
              Study Tools
            </Link>
          </div>
        </nav>

        {/* Footer Actions */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-[var(--accent-tertiary)]/30 bg-[var(--bg-secondary)]">
          <div className="space-y-2">
            <button className="w-full flex items-center space-x-3 px-4 py-3 text-[var(--text-primary)] hover:bg-[var(--bg-primary)] rounded-lg transition-colors">
              <Settings className="w-5 h-5" />
              <span>Settings</span>
            </button>
            <button className="w-full flex items-center space-x-3 px-4 py-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors">
              <LogOut className="w-5 h-5" />
              <span>Sign Out</span>
            </button>
          </div>
        </div>
      </div>
    </>
  );
}