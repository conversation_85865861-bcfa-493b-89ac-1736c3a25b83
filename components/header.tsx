"use server";

import { <PERSON>, <PERSON>, <PERSON>u } from "lucide-react";
import { fetchQuery } from "convex/nextjs";
import { api } from "@/convex/_generated/api";
import { convexAuthNextjsToken } from "@convex-dev/auth/nextjs/server";
import { UserDropdown } from "./layout/user-dropdown";
import { MobileMenuButton } from "./layout/mobile-menu";
import { Navigation } from "./header/navigation";
import { MobileNavigationDrawer } from "./header/mobile-navigation-drawer";

export default async function Header() {
  const user = await fetchQuery(
    api.users.currentUser,
    {},
    {
      token: await convexAuthNextjsToken(),
    }
  );

  return (
    <>
      <header className="flex justify-between items-center px-4 lg:px-8 py-4 lg:py-6 bg-[var(--bg-primary)] sticky top-0 z-40 border-b border-[var(--accent-tertiary)]/20">
        <div className="flex items-center space-x-3">
          {/* Mobile menu button */}
          <MobileMenuButton />

          <Brain className="w-8 h-8 lg:w-10 lg:h-10 icon-primary" />
          <h1 className="text-xl lg:text-3xl font-bold text-[var(--text-primary)]">
            StudyMind
          </h1>
        </div>

        {/* Desktop Navigation */}
        <Navigation />

        <div className="flex items-center space-x-3 lg:space-x-4">
          {/* Notifications */}
          <div className="relative">
            <Bell className="w-5 h-5 lg:w-6 lg:h-6 text-[var(--text-muted)] hover:text-[var(--accent-primary)] cursor-pointer transition-colors focus-ring" />
            <span className="absolute -top-1.5 -right-1.5 w-2.5 h-2.5 bg-red-500 rounded-full border-2 border-[var(--bg-primary)]" />
          </div>

          <UserDropdown user={user} />
        </div>
      </header>
      
      {/* Mobile Navigation Drawer */}
      <MobileNavigationDrawer user={user} />
    </>
  );
}
