import {
  convexAuthNextjsMiddleware,
  createRouteMatcher,
  nextjsMiddlewareRedirect,
} from "@convex-dev/auth/nextjs/server";
import { NextRequest, NextResponse } from "next/server";
import { api } from "./convex/_generated/api";
import { fetchQuery } from "convex/nextjs";

const isAuthPage = createRouteMatcher([
  "/signin",
  "/signup",
  "/forgot-password",
]);
const isProtectedRoute = (req: NextRequest) => !isAuthPage(req);

export default convexAuthNextjsMiddleware(async (request, { convexAuth }) => {
  const isAuthenticated = await convexAuth.isAuthenticated();

  console.log("isAuthenticated", isAuthenticated);
  if (isProtectedRoute(request) && !isAuthenticated) {
    return nextjsMiddlewareRedirect(request, "/signin");
  }

  if (isAuthenticated) {
    const user = await fetchQuery(
      api.users.currentUser,
      {},
      { token: await convexAuth.getToken() }
    );

    if (user?.status === "disabled") {
      return nextjsMiddlewareRedirect(request, "/signin");
    }

    const rootRoute = user?.role === "admin" ? "/admin" : "/dashboard";

    if (request.nextUrl.pathname === "/") {
      return nextjsMiddlewareRedirect(request, rootRoute);
    }
  }

  return NextResponse.next();
});

export const config = {
  // The following matcher runs middleware on all routes
  // except static assets.
  matcher: ["/((?!.*\\..*|_next).*)", "/", "/(api|trpc)(.*)"],
};
